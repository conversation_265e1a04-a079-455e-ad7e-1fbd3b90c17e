FROM swr.cn-north-1.myhuaweicloud.com/chechecloud/python:3.13.2-slim-bookworm

#  $ docker build -f .devops/docker/app-deps.df -t swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend-deps:latest .
#  $ docker push swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend-deps:latest
#  $ docker run --rm -it swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend-deps:latest /bin/sh

ENV LANG=C.UTF-8 LC_ALL=C.UTF-8 TZ=Asia/Shanghai

COPY pyproject.toml poetry.lock /tmp/
RUN pip install --no-cache-dir poetry==2.1.2 && \
    POETRY_VIRTUALENVS_CREATE=0 poetry install --directory=/tmp --no-root --without check --without misc --without test && \
    poetry cache clear --all aliyun -q

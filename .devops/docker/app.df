FROM swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend-deps:BUILD_IMAGE_TAG

#  $ docker build -f .devops/docker/app.df -t swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend-deps:latest .
#  $ docker run --rm -it swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend-deps:latest /bin/sh
#  $ docker push swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend-deps:latest

COPY bethune /app/bethune
COPY babel.cfg /app/
WORKDIR /app

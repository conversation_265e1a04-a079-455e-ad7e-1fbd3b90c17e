FROM swr.cn-north-1.myhuaweicloud.com/chechecloud/dbscript-job:latest

#  $ docker build -f .devops/docker/db.df -t swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-dbscripts-job:latest .
#  $ docker push swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-dbscripts-job:latest
#  $ docker run --rm -it swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-dbscripts-job:latest /bin/sh

ENV LANG=C.UTF-8 LC_ALL=C.UTF-8 TZ=Asia/Shanghai
COPY db/ /db/
RUN db_utils.sh exploded /db/scripts

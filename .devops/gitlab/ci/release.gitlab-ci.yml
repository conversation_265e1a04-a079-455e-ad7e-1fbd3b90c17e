###########
# 抽象作业 #
###########

# 抽象部署应用作业
.deploy-app:
  extends:
    - .deploy-executable-app-like
    - .release:rules:deploy-app


###########
# 准备资源 #
###########

# 安全不敏感资源（Security Insensitive Resources，SiSR）
prepare-sisr:
  extends:
    - .prepare-sisr

# 安全敏感资源（Security Sensitive Resources，SSR）
prepare-ssr:
  extends:
    - .prepare-ssr


################
# 发布前准备工作 #
################

# 升级数据库
upgrade-db:
  extends:
    - .deploy-executable-job-like
    - .release:rules:upgrade-db
  stage: pre-deploy
  variables:
    CHECHE365_APP_NAME: lancet-upgrade-db


################
# 发布可执行内容 #
################

# 部署应用
deploy-app:
  extends:
    - .deploy-app
  variables:
    CHECHE365_APP_NAME: lancet-backend

##############
# Conditions #
##############
.if-default-refs: &if-default-refs
  if: '$CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH || $CI_COMMIT_TAG || $CI_COMMIT_REF_NAME =~ /.*_cicd_.*/'

# 默认分支
.if-build-default-branch: &if-build-default-branch
  if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null'

.if-build-app-deps-default-branch: &if-build-app-deps-default-branch
  if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP_DEPS != null'

.if-build-app-default-branch: &if-build-app-default-branch
  if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP != null'

.if-build-dbscripts-default-branch: &if-build-dbscripts-default-branch
  if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_BUILD && $CHECHE365_PIPELINE_BUILD_DBSCRIPTS != null'

.if-default-branch: &if-default-branch
  if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CHECHE365_PIPELINE_NO_RELEASE == null'

# 当前版本分支
.if-build-current-version-branch: &if-build-current-version-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null'

.if-build-app-deps-current-version-branch: &if-build-app-deps-current-version-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP_DEPS != null'

.if-build-app-current-version-branch: &if-build-app-current-version-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP != null'

.if-build-dbscripts-current-version-branch: &if-build-dbscripts-current-version-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_DBSCRIPTS != null'

.if-current-version-branch: &if-current-version-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_VERSION_BRANCH && $CHECHE365_PIPELINE_NO_RELEASE == null'

# master分支
.if-build-master-but-not-tag: &if-build-master-but-not-tag
  if: '$CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_BUILD == null'

.if-build-app-deps-master-but-not-tag: &if-build-app-deps-master-but-not-tag
  if: '$CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP_DEPS != null'

.if-build-app-master-but-not-tag: &if-build-app-master-but-not-tag
  if: '$CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP != null'

.if-build-dbscripts-master-but-not-tag: &if-build-dbscripts-master-but-not-tag
  if: '$CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_BUILD && $CHECHE365_PIPELINE_BUILD_DBSCRIPTS != null'

.if-master-but-not-tag: &if-master-but-not-tag
  if: '$CI_COMMIT_REF_NAME == "master" && $CHECHE365_PIPELINE_NO_RELEASE == null'

# release分支
.if-build-current-release-branch: &if-build-current-release-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null'

.if-build-app-deps-current-release-branch: &if-build-app-deps-current-release-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP_DEPS != null'

.if-build-app-current-release-branch: &if-build-app-current-release-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP != null'

.if-build-dbscripts-current-release-branch: &if-build-dbscripts-current-release-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_BUILD == null || $CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_DBSCRIPTS != null'

.if-current-release-branch: &if-current-release-branch
  if: '$CI_COMMIT_REF_NAME == $CHECHE365_CURRENT_RELEASE_BRANCH && $CHECHE365_PIPELINE_NO_RELEASE == null'

# tag
.if-build-master-and-tag: &if-build-master-and-tag
  if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_NO_BUILD == null && $CHECHE365_PIPELINE_RELEASE != null'

.if-build-app-deps-master-and-tag: &if-build-app-deps-master-and-tag
  if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_NO_BUILD == null && $CHECHE365_PIPELINE_RELEASE != null || $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP_DEPS != null && $CHECHE365_PIPELINE_RELEASE != null'

.if-build-app-master-and-tag: &if-build-app-master-and-tag
  if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_NO_BUILD == null && $CHECHE365_PIPELINE_RELEASE != null || $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_APP != null && $CHECHE365_PIPELINE_RELEASE != null'

.if-build-dbscripts-master-and-tag: &if-build-dbscripts-master-and-tag
  if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_NO_BUILD == null && $CHECHE365_PIPELINE_RELEASE != null || $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_NO_BUILD != null && $CHECHE365_PIPELINE_BUILD_DBSCRIPTS != null && $CHECHE365_PIPELINE_RELEASE != null'

.if-master-and-tag: &if-master-and-tag
  if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/ && $CHECHE365_PIPELINE_RELEASE != null && $CHECHE365_PIPELINE_NO_RELEASE == null'

####################
# Changes patterns #
####################
.ci-app-deps-patterns: &ci-app-deps-patterns
  - ".devops/docker/app-deps.df"
  - "environment.yml"
  - "poetry.lock"
  - "pyproject.toml"

.ci-app-like-patterns: &ci-app-like-patterns
  - ".devops/docker/app*.df"
  - ".devops/k8s/kust/bethune/lancet-backend/**/*"
  - ".devops/k8s/kust/bethune/security-insensitive-resources/**/*"
  - "babel.cfg"
  - "bethune/**/*"
  - "environment.yml"
  - "poetry.lock"
  - "pyproject.toml"

.ci-deploy-sisr-patterns: &ci-deploy-sisr-patterns
  - ".devops/k8s/kust/bethune/security-insensitive-resources/**/*"

.ci-deploy-app-patterns: &ci-deploy-app-patterns
  - ".devops/docker/app*.df"
  - ".devops/k8s/kust/bethune/lancet-backend/**/*"
  - ".devops/k8s/kust/bethune/security-insensitive-resources/**/*"
  - "babel.cfg"
  - "bethune/**/*"
  - "environment.yml"
  - "poetry.lock"
  - "pyproject.toml"

.ci-dbscripts-like-patterns: &ci-dbscripts-like-patterns
  - "db/**/*"

################
# Shared rules #
################

######################
# Build images rules #
######################
.build-images:rules:build-app-deps-image:
  rules:
    - <<: *if-build-app-deps-default-branch
#    - <<: *if-default-refs
      changes: *ci-app-deps-patterns
      variables:
        CHECHE365_EXEC_ENV: dev
    - <<: *if-build-app-deps-current-version-branch
      changes: *ci-app-deps-patterns
      variables:
        CHECHE365_EXEC_ENV: dev2
      when: never
    - <<: *if-build-app-deps-master-but-not-tag
      changes: *ci-app-deps-patterns
      variables:
        CHECHE365_EXEC_ENV: staging
    - <<: *if-build-app-deps-current-release-branch
      changes: *ci-app-deps-patterns
      variables:
        CHECHE365_EXEC_ENV: staging2
      when: never
    - <<: *if-build-app-deps-master-and-tag
      changes: *ci-app-deps-patterns
      variables:
        CHECHE365_EXEC_ENV: prod

.build-images:rules:build-app-image:
  rules:
    - <<: *if-build-app-default-branch
#    - <<: *if-default-refs
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev
    - <<: *if-build-app-current-version-branch
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev2
      when: never
    - <<: *if-build-app-master-but-not-tag
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging
    - <<: *if-build-app-current-release-branch
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging2
      when: never
    - <<: *if-build-app-master-and-tag
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: prod

.build-images:rules:build-dbscripts-job-image:
  rules:
    - <<: *if-build-dbscripts-default-branch
#    - <<: *if-default-refs
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev
    - <<: *if-build-dbscripts-current-version-branch
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev2
      when: never
    - <<: *if-build-dbscripts-master-but-not-tag
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging
    - <<: *if-build-dbscripts-current-release-branch
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging2
      when: never
    - <<: *if-build-dbscripts-master-and-tag
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: prod

####################
# Cache repo rules #
####################

##################
# Delivery rules #
##################

##############
# Test rules #
##############

##############
# Docs rules #
##############

##################
# Frontend rules #
##################

#######################
# Execute tasks rules #
#######################
.release:rules:prepare-sisr:
  rules:
    - <<: *if-default-branch
#    - <<: *if-default-refs
      changes: *ci-deploy-sisr-patterns
      variables:
        CHECHE365_EXEC_ENV: dev
        CHECHE365_EXEC_NAMESPACE: cabroker-dev
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1
    - <<: *if-current-version-branch
      changes: *ci-deploy-sisr-patterns
      variables:
        CHECHE365_EXEC_ENV: dev2
      when: never
    - <<: *if-master-but-not-tag
      changes: *ci-deploy-sisr-patterns
      variables:
        CHECHE365_EXEC_ENV: staging
        CHECHE365_EXEC_NAMESPACE: cabroker-staging
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1
    - <<: *if-current-release-branch
      changes: *ci-deploy-sisr-patterns
      variables:
        CHECHE365_EXEC_ENV: staging2
      when: never
    - <<: *if-master-and-tag
      changes: *ci-deploy-sisr-patterns
      variables:
        CHECHE365_EXEC_ENV: prod
        CHECHE365_EXEC_NAMESPACE: cabroker
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1

.release:rules:prepare-ssr:
  rules:
    - <<: *if-default-branch
#    - <<: *if-default-refs
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev
      when: never
    - <<: *if-current-version-branch
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev2
      when: never
    - <<: *if-master-but-not-tag
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging
      when: never
    - <<: *if-current-release-branch
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging2
      when: never
    - <<: *if-master-and-tag
      changes: *ci-app-like-patterns
      variables:
        CHECHE365_EXEC_ENV: prod
        CHECHE365_EXEC_NAMESPACE: cabroker
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1

.release:rules:deploy-app:
  rules:
    - <<: *if-build-app-default-branch
#    - <<: *if-default-refs
      changes: *ci-deploy-app-patterns
      variables:
        CHECHE365_EXEC_ENV: dev
        CHECHE365_EXEC_NAMESPACE: cabroker-dev
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1
    - <<: *if-build-app-current-version-branch
      changes: *ci-deploy-app-patterns
      variables:
        CHECHE365_EXEC_ENV: dev2
      when: never
    - <<: *if-build-app-master-but-not-tag
      changes: *ci-deploy-app-patterns
      variables:
        CHECHE365_EXEC_ENV: staging
        CHECHE365_EXEC_NAMESPACE: cabroker-staging
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1
    - <<: *if-build-app-current-release-branch
      changes: *ci-deploy-app-patterns
      variables:
        CHECHE365_EXEC_ENV: staging2
      when: never
    - <<: *if-build-app-master-and-tag
      changes: *ci-deploy-app-patterns
      variables:
        CHECHE365_EXEC_ENV: prod
        CHECHE365_EXEC_NAMESPACE: cabroker
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1

.release:rules:upgrade-db:
  rules:
    - <<: *if-build-dbscripts-default-branch
#    - <<: *if-default-refs
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev
        CHECHE365_EXEC_NAMESPACE: cabroker-dev
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1
    - <<: *if-build-dbscripts-current-version-branch
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: dev2
      when: never
    - <<: *if-build-dbscripts-master-but-not-tag
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging
        CHECHE365_EXEC_NAMESPACE: cabroker-staging
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1
    - <<: *if-build-dbscripts-current-release-branch
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: staging2
      when: never
    - <<: *if-build-dbscripts-master-and-tag
      changes: *ci-dbscripts-like-patterns
      variables:
        CHECHE365_EXEC_ENV: prod
        CHECHE365_EXEC_NAMESPACE: cabroker
        CHECHE365_EXEC_K8S_CONTEXT: aws-pd1

#################
# Reports rules #
#################

################
# Review rules #
################

###############
# Setup rules #
###############

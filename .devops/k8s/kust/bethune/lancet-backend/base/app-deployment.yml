apiVersion: apps/v1
kind: Deployment
metadata:
  name: bethune-lancet-backend
  labels:
    app: bethune-lancet-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: bethune-lancet-backend
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: bethune-lancet-backend
    spec:
      containers:
        - name: bethune-lancet-backend
          image: swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-backend
          imagePullPolicy: Always
          command:
            - uvicorn
          args:
            - bethune:app
            - --reload
            - --host
            - 0.0.0.0
            - --port
            - "80"
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /openapi.json
              port: 80
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 600
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /openapi.json
              port: 80
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 600
            successThreshold: 1
            timeoutSeconds: 1
          resources: { }
          envFrom:
            - configMapRef:
                name: bethune-common-env-cm
            - secretRef:
                name: bethune-common-env-secret
                optional: true
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          volumeMounts:
            - name: nfs-vol
              mountPath: /data
              subPathExpr: nfs2/data/bethune-lancet-backend-dev
            - name: nfs-vol
              mountPath: /logs
              subPathExpr: nfs2/logs/bethune-lancet-backend-dev/$(POD_NAME)
      imagePullSecrets:
        - name: hw-image-secret
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      volumes:
        - name: nfs-vol
          persistentVolumeClaim:
            claimName: data-01

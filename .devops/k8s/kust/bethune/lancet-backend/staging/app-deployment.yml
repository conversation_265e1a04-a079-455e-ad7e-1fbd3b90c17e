apiVersion: apps/v1
kind: Deployment
metadata:
  name: bethune-lancet-backend
spec:
  template:
    spec:
      containers:
        - name: bethune-lancet-backend
          volumeMounts:
            - name: nfs-vol
              mountPath: /data
              subPathExpr: nfs2/data/bethune-lancet-backend-staging
            - name: nfs-vol
              mountPath: /logs
              subPathExpr: nfs2/logs/bethune-lancet-backend-staging/$(POD_NAME)
      volumes:
        - name: nfs-vol
          persistentVolumeClaim:
            claimName: data-02

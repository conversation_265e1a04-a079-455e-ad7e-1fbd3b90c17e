apiVersion: batch/v1
kind: Job
metadata:
  name: bethune-lancet-upgrade-db
spec:
  template:
    spec:
      containers:
        - name: bethune-lancet-dbscripts-job
          image: swr.cn-north-1.myhuaweicloud.com/chechecloud/bethune-lancet-dbscripts-job
          imagePullPolicy: Always
          command:
            - migrate
          args:
            - -database
            - $(MIGRATION_DB_URI)
            - -path
            - /db/scripts/latest
            - up
          envFrom:
            - configMapRef:
                name: bethune-common-env-cm
            - secretRef:
                name: bethune-common-env-secret
                optional: true
      imagePullSecrets:
        - name: hw-image-secret
      restartPolicy: Never

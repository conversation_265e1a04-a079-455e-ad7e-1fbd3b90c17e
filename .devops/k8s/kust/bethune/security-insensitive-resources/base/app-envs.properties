###################
# SiSR on dev env #
###################
LOG_LEVEL=TRACE
LOGGING_FILE_PATH=/logs/app.log

DATABASE_URL=mysql+pymysql://ca_insurance_dev:Kq)2*!DAWKoa0HZJz%40bp@172.31.5.13:3306/ca_insurance_dev?charset=utf8mb4
MIGRATION_DB_URI=mysql://ca_insurance_dev:Kq)2*!DAWKoa0HZJz%40bp@tcp(172.31.5.13:3306)/ca_insurance_dev

REDIS_URL=redis://:W83BY*6V1LwaptvhzTN!K3MMDU@172.31.5.13:6379/29

SMTP_SERVER=email-smtp.ca-central-1.amazonaws.com
SMTP_PORT=587
SMTP_USERNAME=AKIA37B7MUSOLYZK5KTV
SMTP_PASSWORD=BNSBQswNtBnhn6ssKvZhWXqcmtL52rRCXWlayiy8FV1H
SMTP_SENDER=<EMAIL>

CACHE_PREFIX=BETHUNE_CACHE_REDIS_
BETHUNE_CACHE_NULL_BACKEND=dogpile.cache.null
BETHUNE_CACHE_MEMORY_BACKEND=dogpile.cache.memory
BETHUNE_CACHE_MEMORY_EXPIRATION_TIME=3600
BETHUNE_CACHE_REDIS_BACKEND=dogpile.cache.redis
BETHUNE_CACHE_REDIS_ARGUMENTS_URL=redis://:W83BY*6V1LwaptvhzTN!K3MMDU@172.31.5.13:6379/29
BETHUNE_CACHE_REDIS_ARGUMENTS_DISTRIBUTED_LOCK=True
BETHUNE_CACHE_REDIS_ARGUMENTS_THREAD_LOCAL_LOCK=False

BETHUNE_SITE_URL=https://dev.cabroker.ca
BETHUNE_EXPORT_SERVICE_URL=http://aid-gotenberg-gotenberg-service

DATA_DIR=/data

GOOGLE_CLIENT_TYPE=web
GOOGLE_CLIENT_ID=************-********************************.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URIS=["https://dev.cabroker.ca/api/v1/system/oauth/callback/google"]
GOOGLE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
GOOGLE_TOKEN_URI=https://oauth2.googleapis.com/token

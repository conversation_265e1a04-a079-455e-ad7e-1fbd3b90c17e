name: comintern-bethune-local-env
services:
  mysql:
    image: mysql:8
    container_name: comintern-bethune-mysql
    ports:
      - "127.0.0.1:23306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: Password!23
      MYSQL_AUTHENTICATION_PLUGIN: mysql_native_password
    volumes:
      - ./mysql_data:/var/lib/mysql
    restart: unless-stopped
  redis:
    image: redis:8
    container_name: comintern-bethune-redis
    ports:
      - "127.0.0.1:26379:6379"
    volumes:
      - ./redis_data:/data
    restart: unless-stopped

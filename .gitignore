docs/
tmp/
tags/
.env

# dependencies
/node_modules
/dist
/data
yarn.lock

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/

# Local temporary files
.devops/local/mysql_data/
.devops/local/redis_data/

#System Files
.DS_Store/
Thumbs.db

# Log files
*.log
**/logs

# mac desktop service
.DS_Store

# ignore all python cache folders and files
__pycache__/

# ignore coverage report
cov-report/

# ignore coverage file
.coverage

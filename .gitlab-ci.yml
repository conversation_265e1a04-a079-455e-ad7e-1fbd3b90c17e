include:
  - local: .devops/gitlab/ci/build-images.gitlab-ci.yml
  - local: .devops/gitlab/ci/global.gitlab-ci.yml
  - local: .devops/gitlab/ci/release.gitlab-ci.yml
  - local: .devops/gitlab/ci/rules.gitlab-ci.yml
  - project: 'pub-libs/devops'
    ref: v2.4.2-RC1
    file: '/.devops/gitlab/ci/build-images.gitlab-ci.yml'
  - project: 'pub-libs/devops'
    ref: v2.4.2-RC1
    file: '/.devops/gitlab/ci/global.gitlab-ci.yml'
  - project: 'pub-libs/devops'
    ref: v2.4.2-RC1
    file: '/.devops/gitlab/ci/global-project-side.gitlab-ci.yml'
  - project: 'pub-libs/devops'
    ref: v2.4.2-RC1
    file: '/.devops/gitlab/ci/release-project-side.gitlab-ci.yml'
  - project: 'comintern/public-devops'
    ref: master
    file: '/.devops/gitlab/ci/release-project-side.gitlab-ci.yml'

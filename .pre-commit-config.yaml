repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      # Prevent giant files from being committed.
      - id: check-added-large-files
        args:
          - --maxkb=20480
      # Simply check whether files parse as valid python.
      - id: check-ast
      # Require literal syntax when initializing empty or zero Python builtin types.
      - id: check-builtin-literals
      # Check for files with names that would conflict on a case-insensitive filesystem like MacOS HFS+ or Windows FAT.
      - id: check-case-conflict
      # Checks for a common error of placing code before the docstring.
      - id: check-docstring-first
      # Checks that non-binary executables have a proper shebang.
      - id: check-executables-have-shebangs
      # Attempts to load all json files to verify syntax.
      - id: check-json
      # Check for files that contain merge conflict strings.
      - id: check-merge-conflict
      # Attempts to load all TOML files to verify syntax.
      - id: check-toml
      # Attempts to load all yaml files to verify syntax.
      - id: check-yaml
      # Check for debugger imports and py37+ breakpoint() calls in python source.
      - id: debug-statements
      # Makes sure files end in a newline and only a newline.
      - id: end-of-file-fixer
      # Checks for the existence of private keys.
      - id: detect-private-key
      #  # This hook replaces double quoted strings with single quoted strings.
      #  - id: double-quote-string-fixer
      # removes UTF-8 byte order marker
      - id: fix-byte-order-marker
      # Prevent addition of new git submodules.
      - id: forbid-new-submodules
      # Assert that files in tests/ end in _test.py.
      - id: name-tests-test
      # Checks that all your JSON files are pretty.
      - id: pretty-format-json
        args:
          - --autofix
          - --no-sort-keys
          - --indent=4
          - --no-ensure-ascii
      # Sorts entries in requirements.txt and removes incorrect entry for pkg-resources==0.0.0.
      - id: requirements-txt-fixer
      # Trims trailing whitespace.
      - id: trailing-whitespace
  - repo: https://github.com/PyCQA/flake8
    rev: 7.2.0
    hooks:
      - id: flake8
        exclude: >
          (?x)^(
              .*_pb2.py|
              .*_pb2_grpc.py
          )$
        additional_dependencies:
          # DTZ
          - flake8-datetimez
          # Y
          - flake8-pyi
          # Q
          # - flake8-quotes
          # R
          - flake8-return
          # STR
          # - flake8-strings
          # TYP
          - flake8-typing-imports
          # T
          - flake8-tuple
          # N
          - pep8-naming
        args:
          - --count
          - --max-complexity=10
          # 与Black兼容的行宽与规则设置
          - --ignore=E203,E501,W503,E701,TYP001
          - --max-line-length=120
          - --select=B,C,D,E,F,N,R,S,T,W,Y
          # 其它
          - --show-source
          - --statistics
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.1
    hooks:
      - id: pyupgrade
        args:
          - --py312-plus
  - repo: https://github.com/PyCQA/autoflake
    rev: v2.3.1
    hooks:
      - id: autoflake
        args:
          - --remove-all-unused-imports
          - --in-place
          - --jobs=1
  - repo: https://github.com/asottile/reorder-python-imports
    rev: v3.14.0
    hooks:
      - id: reorder-python-imports
        args:
          - --py312-plus
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.15.0
    hooks:
      - id: mypy
        additional_dependencies:
          - mypy-extensions
          - pydantic
          - pytest-mypy
          - sqlalchemy2-stubs
          - tokenize-rt
          - types-python-dateutil
          - types-pytz
          - types-PyYAML
          - types-requests
          - types-redis
        args:
          - --install-types
          - --ignore-missing-imports
          - --follow-imports=skip
          - --strict-optional
        exclude: >
          (?x)^(
              .*_pb2.py|
              .*_pb2_grpc.py
          )$
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        args:
          - --line-length=120
exclude: |
  (?x)(
      ^.devops/|
      ^.husky/|poe
      ^.run/|
      ^.vscode/|
      ^data/|
      ^output/|
      ^alibabacloud-nls-python-sdk-1.0.0
  )

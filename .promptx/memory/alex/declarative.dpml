<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753236720541_kh8zyzm4l" time="2025/07/23 10:12">
    <content>
      新加入团队，需要学习团队的代码规范、开发流程和项目标准。领导对代码规范、书写规范和一致性有严格要求。准备通过对话了解项目规范和开发流程。
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1753237221732_bvyv1nzud" time="2025/07/23 10:20">
    <content>
      团队代码编写强制性规范：1)私有化常量必须以单下划线开头 2)命名必须清晰反映用途 3)禁止hasattr()使用getattr()替代 4)避免不必要中间变量直接返回表达式 5)推导式替代for循环(仅return和函数返回值场景) 6)鼓励链式调用 7)代码复用DRY原则 8)禁止N+1查询 9)拒绝无意义注释 10)一致性是强制要求体现在各方面 11)代码尽量简洁避免中间变量
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753237503962_aw0dx3xek" time="2025/07/23 10:25">
    <content>
      更新代码规范：禁止hasattr()，尽量也不要用getattr()。99%的属性都可以直接通过对象.属性访问。在编写代码前必须仔细详细阅读项目源码，了解项目的层次架构、业务接口编写方式、代码编写习惯等，确保完全遵循项目现有的模式和风格。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753237724882_wc8mzzgyx" time="2025/07/23 10:28">
    <content>
      项目架构分析：bethune项目采用四层架构 1)API层(endpoint)：FastAPI路由，依赖注入ServiceContext，使用BaseResponse统一响应格式 2)DTO层：Pydantic模型，包含Base/Create/Update/Response类，有to_model()和from_model()方法 3)Service层：继承BaseService，业务逻辑处理，缓存装饰器 4)Repository层：继承BaseRepository，数据访问，SQLModel查询。编码风格：链式调用、推导式、私有常量下划线前缀、直接属性访问、避免中间变量
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753237971831_cocb1cj48" time="2025/07/23 10:32">
    <content>
      实现了完整的保险公司查询接口，严格遵循项目四层架构：1)DTO层：添加QueryFilter、QueryParams、Create、Update类，包含to_model()和from_model()方法 2)Repository层：添加_add_query_conditions私有函数和get_by_query_filters方法，使用链式调用和推导式 3)Service层：继承BaseService，添加get_by_query_filters方法 4)API层：使用Query()依赖注入、BaseResponse统一响应、Pagination分页。完全遵循编码规范：私有函数下划线前缀、链式调用、避免中间变量、直接属性访问
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753238069473_zcb887hlj" time="2025/07/23 10:34">
    <content>
      团队开发标准规范总结：1)代码编写规则：私有常量_前缀、命名清晰、禁用hasattr/getattr、避免中间变量、推导式优先、链式调用、DRY原则、禁止N+1查询、拒绝废话注释、强制一致性 2)四层架构模式：DTO层(QueryFilter/QueryParams/Create/Update/Response，to_model/from_model方法)、Repository层(继承BaseRepository，_add_query_conditions私有函数，链式调用)、Service层(继承BaseService，简洁方法)、API层(Query依赖注入，BaseResponse统一响应，Pagination分页) 3)技术栈规范：FastAPI+SQLModel+Pydantic+pytest+MySQL，ServiceContext依赖注入，session().exec()查询，直接属性访问
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1753238652973_5ayqn4yv9" time="2025/07/23 10:44">
    <content>
      测试用例编写规范：1)测试场景：单接口测试(正确/错误用例)、流程测试(多接口调用链) 2)编写原则：学习项目测试习惯保证一致性、不加非必要assert只验证code字段、不添加注释、默认进行权限验证登录 3)五种角色权限：系统管理员**********************、代理人***************************、泛代**************、内勤用系统管理员、经纪行管理员********************* 4)其他要求：不运行测试、命名规范望文知意、不用class直接函数、禁止修改业务代码、按需求实现禁止拓展、通用代码封装、必须用参数化测试避免重复
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1753266184877_2xjnfz5s5" time="2025/07/23 18:23">
    <content>
      用户偏好：生成内容时只需要用markdown格式给出执行顺序和思路，不需要脑图或流程图
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1753323420944_66r9axbgb" time="2025/07/24 10:17">
    <content>
      代码生成强制规范：1)禁止重复定义DTO类，必须复用项目中已存在的类 2)禁止不必要中间变量，可直接return的表达式不允许定义中间变量 3)强制使用推导式替代for循环append操作 4)API接口只能返回BaseResponse.ok()格式，禁止自定义返回类型 5)路径参数id必须放在URL最后位置 6)代码复用优先级：直接使用&gt;扩展现有&gt;继承现有&gt;创建新代码
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753324080004_mqfmh2o0a" time="2025/07/24 10:28">
    <content>
      代码导入规范强制要求：禁止在方法内部写import语句，所有导入必须写在文件最开始位置。例如禁止在方法内写&quot;from bethune.model.system import ROLE_BROKER_ID&quot;，必须在文件顶部统一导入。这是Python代码组织的基本规范，确保导入语句的统一性和可维护性。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753324602833_jftfck5to" time="2025/07/24 10:36">
    <content>
      禁止无意义的方法重新定义：如果可以直接调用现有方法或在推导式中直接处理异常，就禁止创建额外的辅助方法。例如禁止为了检查权限存在而创建_permission_exists方法，应该直接在推导式中处理NotFoundError异常。避免不必要的代码层次和方法定义。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753324916864_6c7yloq57" time="2025/07/24 10:41">
    <content>
      禁止在生成的代码中加入任何注释：代码中不允许有任何形式的注释，包括行内注释、块注释、规范说明注释等。注释会导致代码混乱，影响可读性。代码应该通过良好的命名和结构来自解释，而不是依赖注释。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753325339900_kuurdvm5d" time="2025/07/24 10:48">
    <content>
      强制使用构造函数依赖注入：禁止在方法内部直接实例化Service类，如禁止&quot;permission_service = PermissionService(PermissionRepository())&quot;。必须通过构造函数注入依赖，遵循SOLID原则。正例：在__init__方法中注入&quot;permission_service: PermissionService&quot;，然后在方法中使用&quot;self.permission_service&quot;。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753421539999_axb1d0hr5" time="2025/07/25 13:32">
    <content>
      用户要求生成一份完整的MD文档，包含：1)我的自我介绍 2)用户对我的所有要求 3)我需要遵守的规范。这是一个重要的工作规范总结文档。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753509568849_zsz5fy0bg" time="2025/07/26 13:59">
    <content>
      团队新增代码规范：禁止使用if elif结构，必须使用独立的if语句。这样可以确保代码清晰性和可维护性，每个条件判断都是独立的，便于理解和修改。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753522806438_r1kkggp4v" time="2025/07/26 17:40">
    <content>
      团队新增依赖注入规范：禁止在接口函数内部直接实例化ServiceContext（如sc_insurance = InsuranceServiceContext()），必须通过FastAPI的依赖注入系统获取，使用Depends()或Security()装饰器。这确保了依赖管理的一致性和代码的可测试性。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753523102606_kdpc0f4v4" time="2025/07/26 17:45">
    <content>
      团队完整开发规范已整合到alex角色中：包含技术栈约束、代码编写强制约束、架构设计规则、测试编写原则、权限角色使用等16项核心要求。所有规范已系统化整理并嵌入到角色的execution文件中，确保开发过程中严格遵循团队标准。
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1753526782919_2zi0l9r4d" time="2025/07/26 18:46">
    <content>
      团队新增异常处理规范：除非一定要抛异常，否则不要进行try except，让异常自然传播。只在需要转换异常类型、特殊处理或返回默认值时才使用try except。避免不必要的异常捕获，让框架和上层处理异常。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753530930426_nz5dll9ge" time="2025/07/26 19:55">
    <content>
      用户要求使用中文回答问题
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753681306134_7oxmcbm9v" time="2025/07/28 13:41">
    <content>
      团队新增枚举命名规范：所有枚举类必须以xxxEnum格式命名，确保命名一致性。例如UserInfoTypeEnum、UserStatusEnum、PaymentMethodEnum等，避免使用UserType、Status等不规范命名。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753932110351_oz36ds7yn" time="2025/07/31 11:21">
    <content>
      **代码修改确认原则**：当用户明确告知&quot;我修改了代码&quot;时，需要：1)仔细分析用户的实际修改(删除了哪些方法、新增了什么、修改逻辑) 2)尊重用户的最终决策(不重新添加用户删除的方法，以用户实际代码为准) 3)基于用户修改提供建议(在修改基础上优化，指出问题，确保后续建议与实际代码一致)。确保协作以实际代码实现为准，避免重复添加已删除内容。
    </content>
    <tags>#其他</tags>
  </item>
</memory>
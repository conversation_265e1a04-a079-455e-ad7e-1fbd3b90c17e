<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753067126049_qsxge8yom" time="2025/07/21 11:05">
    <content>
      项目测试代码规范分析：1. 测试文件命名：*_test.py格式 2. 权限认证：auth_headers_generator(email)函数，5种角色邮箱固定 3. 断言模式：主要验证response.status_code和response.json()[&quot;code&quot;]，最小化assert 4. 测试方法命名：test_功能描述格式，简短明确 5. fixture使用：test_client, api_prefix, auth_headers_generator等标准fixture 6. 参数化测试：使用@pytest.mark.parametrize装饰器
    </content>
    <tags>#其他</tags>
  </item>
</memory>
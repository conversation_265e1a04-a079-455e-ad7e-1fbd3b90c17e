<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753016681841_jlkhlaab1" time="2025/07/20 21:04">
    <content>
      用户偏好：不需要自动实现测试用例，除非用户明确要求测试相关功能，否则专注于业务代码实现
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753064154526_kk56jq3zg" time="2025/07/21 10:15">
    <content>
      项目代码规范分析：1. DTO命名：XxxBase(基础字段)、XxxCreate(创建)、Xxx(响应，继承Base+AuditMixin)、XxxList(列表) 2. 导入顺序：标准库-&gt;第三方库-&gt;项目内部模块 3. from_model方法：@classmethod，参数model: XxxModel，返回&quot;Xxx&quot; 4. to_model方法：实例方法，返回XxxModel，使用model_dump(exclude_unset=True) 5. Repository命名：XxxRepository(BaseRepository[XxxModel]) 6. Repository查询方法：get_by_xxx格式，异常处理用NoResultFound+NotFoundError
    </content>
    <tags>#其他</tags>
  </item>
</memory>
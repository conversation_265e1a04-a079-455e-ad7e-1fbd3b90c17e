{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T02:51:52.677Z", "updatedAt": "2025-07-31T02:51:52.689Z", "resourceCount": 19}, "resources": [{"id": "alex", "source": "project", "protocol": "role", "name": "<PERSON> 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/alex/alex.role.md", "metadata": {"createdAt": "2025-07-31T02:51:52.678Z", "updatedAt": "2025-07-31T02:51:52.678Z", "scannedAt": "2025-07-31T02:51:52.678Z", "path": "role/alex/alex.role.md"}}, {"id": "fastapi-development", "source": "project", "protocol": "execution", "name": "Fastapi Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/alex/execution/fastapi-development.execution.md", "metadata": {"createdAt": "2025-07-31T02:51:52.679Z", "updatedAt": "2025-07-31T02:51:52.679Z", "scannedAt": "2025-07-31T02:51:52.679Z", "path": "role/alex/execution/fastapi-development.execution.md"}}, {"id": "problem-solving", "source": "project", "protocol": "thought", "name": "Problem Solving 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/problem-solving.thought.md", "metadata": {"createdAt": "2025-07-31T02:51:52.680Z", "updatedAt": "2025-07-31T02:51:52.680Z", "scannedAt": "2025-07-31T02:51:52.680Z", "path": "role/alex/thought/problem-solving.thought.md"}}, {"id": "python-senior-thinking", "source": "project", "protocol": "thought", "name": "Python Senior Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/python-senior-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T02:51:52.680Z", "updatedAt": "2025-07-31T02:51:52.680Z", "scannedAt": "2025-07-31T02:51:52.680Z", "path": "role/alex/thought/python-senior-thinking.thought.md"}}, {"id": "research-methodology", "source": "project", "protocol": "execution", "name": "Research Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/research-methodology.execution.md", "metadata": {"createdAt": "2025-07-31T02:51:52.681Z", "updatedAt": "2025-07-31T02:51:52.681Z", "scannedAt": "2025-07-31T02:51:52.681Z", "path": "role/pepper/execution/research-methodology.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-07-31T02:51:52.682Z", "updatedAt": "2025-07-31T02:51:52.682Z", "scannedAt": "2025-07-31T02:51:52.682Z", "path": "role/pepper/pepper.role.md"}}, {"id": "deep-research", "source": "project", "protocol": "thought", "name": "Deep Research 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/deep-research.thought.md", "metadata": {"createdAt": "2025-07-31T02:51:52.682Z", "updatedAt": "2025-07-31T02:51:52.682Z", "scannedAt": "2025-07-31T02:51:52.682Z", "path": "role/pepper/thought/deep-research.thought.md"}}, {"id": "python-development-workflow", "source": "project", "protocol": "execution", "name": "Python Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-backend-expert/execution/python-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T02:51:52.683Z", "updatedAt": "2025-07-31T02:51:52.683Z", "scannedAt": "2025-07-31T02:51:52.683Z", "path": "role/python-backend-expert/execution/python-development-workflow.execution.md"}}, {"id": "python-backend-stack", "source": "project", "protocol": "knowledge", "name": "Python Backend Stack 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/python-backend-expert/knowledge/python-backend-stack.knowledge.md", "metadata": {"createdAt": "2025-07-31T02:51:52.683Z", "updatedAt": "2025-07-31T02:51:52.683Z", "scannedAt": "2025-07-31T02:51:52.683Z", "path": "role/python-backend-expert/knowledge/python-backend-stack.knowledge.md"}}, {"id": "python-backend-expert", "source": "project", "protocol": "role", "name": "Python Backend Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-backend-expert/python-backend-expert.role.md", "metadata": {"createdAt": "2025-07-31T02:51:52.684Z", "updatedAt": "2025-07-31T02:51:52.684Z", "scannedAt": "2025-07-31T02:51:52.684Z", "path": "role/python-backend-expert/python-backend-expert.role.md"}}, {"id": "python-expertise", "source": "project", "protocol": "thought", "name": "Python Expertise 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-backend-expert/thought/python-expertise.thought.md", "metadata": {"createdAt": "2025-07-31T02:51:52.684Z", "updatedAt": "2025-07-31T02:51:52.684Z", "scannedAt": "2025-07-31T02:51:52.684Z", "path": "role/python-backend-expert/thought/python-expertise.thought.md"}}, {"id": "bug-fixer", "source": "project", "protocol": "manual", "name": "Bug Fixer manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/bug-fixer/bug-fixer.manual.md", "metadata": {"createdAt": "2025-07-31T02:51:52.685Z", "updatedAt": "2025-07-31T02:51:52.685Z", "scannedAt": "2025-07-31T02:51:52.685Z", "path": "tool/bug-fixer/bug-fixer.manual.md"}}, {"id": "bug-fixer", "source": "project", "protocol": "tool", "name": "Bug Fixer tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/bug-fixer/bug-fixer.tool.js", "metadata": {"createdAt": "2025-07-31T02:51:52.685Z", "updatedAt": "2025-07-31T02:51:52.685Z", "scannedAt": "2025-07-31T02:51:52.685Z", "path": "tool/bug-fixer/bug-fixer.tool.js"}}, {"id": "crud-generator", "source": "project", "protocol": "manual", "name": "Crud Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/crud-generator/crud-generator.manual.md", "metadata": {"createdAt": "2025-07-31T02:51:52.686Z", "updatedAt": "2025-07-31T02:51:52.686Z", "scannedAt": "2025-07-31T02:51:52.686Z", "path": "tool/crud-generator/crud-generator.manual.md"}}, {"id": "crud-generator", "source": "project", "protocol": "tool", "name": "Crud Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/crud-generator/crud-generator.tool.js", "metadata": {"createdAt": "2025-07-31T02:51:52.686Z", "updatedAt": "2025-07-31T02:51:52.686Z", "scannedAt": "2025-07-31T02:51:52.686Z", "path": "tool/crud-generator/crud-generator.tool.js"}}, {"id": "sql-generator", "source": "project", "protocol": "manual", "name": "Sql Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/sql-generator/sql-generator.manual.md", "metadata": {"createdAt": "2025-07-31T02:51:52.687Z", "updatedAt": "2025-07-31T02:51:52.687Z", "scannedAt": "2025-07-31T02:51:52.687Z", "path": "tool/sql-generator/sql-generator.manual.md"}}, {"id": "sql-generator", "source": "project", "protocol": "tool", "name": "Sql Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/sql-generator/sql-generator.tool.js", "metadata": {"createdAt": "2025-07-31T02:51:52.688Z", "updatedAt": "2025-07-31T02:51:52.688Z", "scannedAt": "2025-07-31T02:51:52.688Z", "path": "tool/sql-generator/sql-generator.tool.js"}}, {"id": "test-generator", "source": "project", "protocol": "manual", "name": "Test Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/test-generator/test-generator.manual.md", "metadata": {"createdAt": "2025-07-31T02:51:52.689Z", "updatedAt": "2025-07-31T02:51:52.689Z", "scannedAt": "2025-07-31T02:51:52.689Z", "path": "tool/test-generator/test-generator.manual.md"}}, {"id": "test-generator", "source": "project", "protocol": "tool", "name": "Test Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/test-generator/test-generator.tool.js", "metadata": {"createdAt": "2025-07-31T02:51:52.689Z", "updatedAt": "2025-07-31T02:51:52.689Z", "scannedAt": "2025-07-31T02:51:52.689Z", "path": "tool/test-generator/test-generator.tool.js"}}], "stats": {"totalResources": 19, "byProtocol": {"role": 3, "execution": 3, "thought": 4, "knowledge": 1, "manual": 4, "tool": 4}, "bySource": {"project": 19}}}
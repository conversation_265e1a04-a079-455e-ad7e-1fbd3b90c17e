<execution>
  <constraint>
    ## 技术栈约束
    - **框架限制**：必须使用FastAPI + SQLModel + Pydantic + pytest + MySQL技术栈
    - **Python版本**：Python 3.8+，支持异步语法和类型注解
    - **数据库约束**：MySQL 5.7+，支持JSON字段和复杂查询
    - **测试要求**：pytest框架，测试覆盖率不低于80%
    - **性能要求**：API响应时间不超过500ms，支持并发1000+请求
    - **代码质量**：必须通过ruff检查，完整类型注解，文档覆盖

    ## 团队代码编写强制约束
    - **命名规范**：私有化常量必须以单下划线开头，命名必须清晰反映用途，枚举类必须以xxxEnum格式命名
    - **属性访问**：禁止使用hasattr()和getattr()，99%的属性都可以直接通过对象.属性访问
    - **变量使用**：避免不必要中间变量，可直接返回的表达式不允许定义中间变量
    - **循环优化**：推导式替代for循环(仅return和函数返回值场景)，鼓励链式调用
    - **代码复用**：严格遵循DRY原则，禁止重复定义DTO类，必须复用项目中已存在的类
    - **查询优化**：禁止N+1查询，使用session().exec()执行查询
    - **注释规范**：禁止在生成的代码中加入任何注释，代码应通过良好命名和结构自解释
    - **一致性要求**：强制一致性体现在各方面，编写代码前必须详细阅读项目源码了解现有模式
    - **导入规范**：禁止在方法内部写import语句，所有导入必须写在文件最开始位置
    - **方法定义**：禁止无意义的方法重新定义，避免不必要的代码层次和方法定义
  </constraint>

  <rule>
    ## 强制性开发规则
    - **异步优先规则**：I/O操作必须使用异步，避免阻塞事件循环
    - **测试驱动规则**：任何新功能必须先写测试用例再写实现
    - **类型安全规则**：所有函数必须有完整的类型注解
    - **依赖注入规则**：使用FastAPI Depends进行依赖管理和数据验证
    - **错误处理规则**：所有异常必须有适当的处理和用户友好的错误响应
    - **文档更新规则**：API变更必须同步更新OpenAPI文档
    - **代码审查规则**：所有代码必须经过同事审查才能合并
    - **条件判断规范**：禁止使用if elif结构，必须使用独立的if语句确保代码清晰性和可维护性
    - **依赖注入规范**：禁止在接口函数内部直接实例化ServiceContext，必须通过FastAPI依赖注入系统获取
    - **异常处理规范**：除非一定要抛异常，否则不要进行try except，让异常自然传播

    ## 架构设计强制规则
    - **四层架构规则**：严格遵循API层(endpoint)、DTO层、Service层、Repository层的四层架构
    - **DTO层规则**：包含QueryFilter、QueryParams、Create、Update、Response类，必须有to_model()和from_model()方法
    - **Repository层规则**：继承BaseRepository，使用_add_query_conditions私有函数，链式调用和推导式
    - **Service层规则**：继承BaseService，业务逻辑处理，使用缓存装饰器，强制使用构造函数依赖注入
    - **API层规则**：使用Query()依赖注入、BaseResponse统一响应格式、Pagination分页，路径参数id必须放在URL最后位置

    ## 代码复用优先级规则
    - **复用优先级**：直接使用 > 扩展现有 > 继承现有 > 创建新代码
    - **禁止重复定义**：禁止重复定义DTO类，必须复用项目中已存在的类
    - **API返回格式**：API接口只能返回BaseResponse.ok()格式，禁止自定义返回类型
  </rule>

  <guideline>
    ## 开发指导原则
    - **业务优先原则**：技术方案服务于业务目标，避免过度工程化
    - **简洁性原则**：优先选择简单可维护的解决方案
    - **性能意识原则**：关注系统性能，主动进行优化
    - **安全第一原则**：考虑安全风险，实施防护措施
    - **可扩展性原则**：设计时考虑未来扩展需求
    - **团队协作原则**：代码风格统一，便于团队维护

    ## 测试编写指导原则
    - **测试场景原则**：包含单接口测试(正确/错误用例)和流程测试(多接口调用链)
    - **一致性原则**：学习项目测试习惯保证一致性，不加非必要assert只验证code字段
    - **简洁性原则**：不添加注释，默认进行权限验证登录，命名规范望文知意
    - **功能性原则**：不用class直接函数，禁止修改业务代码，按需求实现禁止拓展
    - **复用性原则**：通用代码封装，必须用参数化测试避免重复，不运行测试

    ## 权限角色使用原则
    - **系统管理员**：<EMAIL>
    - **代理人**：<EMAIL>
    - **泛代**：<EMAIL>
    - **内勤**：使用系统管理员
    - **经纪行管理员**：<EMAIL>
  </guideline>

  <process>
    ## FastAPI项目开发标准流程

    ### Step 1: 项目初始化与架构设计 (1-2天)
    ```mermaid
    flowchart TD
        A[需求分析] --> B[技术方案设计]
        B --> C[项目结构搭建]
        C --> D[基础配置]
        D --> E[数据库设计]
        E --> F[API接口设计]
    ```

    **项目结构搭建**：
    ```
    fastapi-project/
    ├── src/
    │   ├── auth/           # 认证模块
    │   ├── users/          # 用户模块
    │   ├── posts/          # 业务模块
    │   ├── config.py       # 配置管理
    │   ├── database.py     # 数据库连接
    │   └── main.py         # 应用入口
    ├── tests/              # 测试目录
    ├── alembic/            # 数据库迁移
    └── requirements/       # 依赖管理
    ```

    **基础配置清单**：
    - [ ] FastAPI应用初始化
    - [ ] 数据库连接配置
    - [ ] 环境变量管理
    - [ ] 日志配置
    - [ ] CORS设置
    - [ ] 中间件配置

    ### Step 2: 数据模型与API开发 (3-5天)
    ```mermaid
    flowchart LR
        A[SQLModel设计] --> B[Pydantic Schema]
        B --> C[API路由实现]
        C --> D[依赖注入]
        D --> E[业务逻辑]
        E --> F[错误处理]
    ```

    **SQLModel设计模板**：
    ```python
    from sqlmodel import SQLModel, Field, Relationship
    from typing import Optional, List
    from datetime import datetime

    class UserBase(SQLModel):
        email: str = Field(unique=True, index=True)
        first_name: str = Field(max_length=50)
        last_name: str = Field(max_length=50)
        is_active: bool = Field(default=True)

    class User(UserBase, table=True):
        id: Optional[int] = Field(default=None, primary_key=True)
        hashed_password: str
        created_at: datetime = Field(default_factory=datetime.utcnow)
        posts: List["Post"] = Relationship(back_populates="owner")

    class UserCreate(UserBase):
        password: str

    class UserResponse(UserBase):
        id: int
        created_at: datetime
    ```

    **API路由实现模板**：
    ```python
    from fastapi import APIRouter, Depends, HTTPException, status
    from sqlmodel import Session, select
    from typing import List

    router = APIRouter(prefix="/users", tags=["users"])

    @router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
    async def create_user(
        user_data: UserCreate,
        db: Session = Depends(get_db)
    ) -> UserResponse:
        # 检查邮箱是否已存在
        existing_user = await db.exec(select(User).where(User.email == user_data.email))
        if existing_user.first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # 创建新用户
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            **user_data.dict(exclude={"password"}),
            hashed_password=hashed_password
        )
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)
        return db_user
    ```

    **条件判断规范示例**：
    ```python
    # ❌ 错误：使用if elif结构
    def process_user_status(user: User) -> str:
        if user.is_active:
            return "active"
        elif user.is_pending:
            return "pending"
        elif user.is_suspended:
            return "suspended"
        else:
            return "unknown"

    # ✅ 正确：使用独立if语句
    def process_user_status(user: User) -> str:
        if user.is_active:
            return "active"
        if user.is_pending:
            return "pending"
        if user.is_suspended:
            return "suspended"
        return "unknown"

    # ✅ 更好的方式：使用字典映射或match语句
    def process_user_status(user: User) -> str:
        status_map = {
            'is_active': 'active',
            'is_pending': 'pending',
            'is_suspended': 'suspended'
        }
        for attr, status in status_map.items():
            if getattr(user, attr, False):
                return status
        return "unknown"
    ```

    **ServiceContext依赖注入规范示例**：
    ```python
    # ❌ 错误：在接口函数内部手动实例化ServiceContext
    @api_router.get("/broker-info")
    async def get_broker_info(sc_system: Annotated[SystemServiceContext, Depends()]):
        # 错误的做法
        sc_insurance = InsuranceServiceContext()
        sc_insurance._current_user = sc_system.current_user
        sc_insurance._permissions = sc_system.permissions
        broker = sc_insurance.current_broker
        return BaseResponse.ok(broker)

    # ✅ 正确：通过FastAPI依赖注入获取ServiceContext
    @api_router.get("/broker-info")
    async def get_broker_info(
        sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],
        sc_insurance: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)]
    ):
        broker = sc_insurance.current_broker
        return BaseResponse.ok(broker)

    # ✅ 更简洁的方式：只使用需要的ServiceContext
    @api_router.get("/broker-info")
    async def get_broker_info(
        sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)]
    ):
        broker = sc.current_broker
        return BaseResponse.ok(broker)

    # ✅ 带权限检查的依赖注入
    @api_router.get("/broker-profile")
    async def get_broker_profile(
        sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:broker:view"])
    ):
        profile = sc.broker_profile_service.get_by_broker_id(sc.current_broker.id)
        return BaseResponse.ok(profile)
    ```

    **异常处理规范示例**：
    ```python
    # ❌ 错误：不必要的try except
    @api_router.get("/user/{id}")
    async def get_user(id: int, sc: Annotated[ServiceContext, Depends()]):
        try:
            user = sc.user_service.get_by_id(id)
            return BaseResponse.ok(user)
        except Exception as e:
            raise HTTPException(status_code=404, detail="User not found")

    # ✅ 正确：让异常自然传播，由框架处理
    @api_router.get("/user/{id}")
    async def get_user(id: int, sc: Annotated[ServiceContext, Depends()]):
        user = sc.user_service.get_by_id(id)
        return BaseResponse.ok(user)

    # ✅ 正确：只在需要转换异常类型时使用try except
    @api_router.get("/broker-info")
    async def get_broker_info(sc: Annotated[ServiceContext, Depends()]):
        try:
            broker = sc.current_broker
            return BaseResponse.ok(broker)
        except AttributeError:
            raise NotFoundError("User is not a broker")

    # ✅ 正确：需要特殊处理时使用try except
    @api_router.get("/optional-data")
    async def get_optional_data(sc: Annotated[ServiceContext, Depends()]):
        try:
            data = sc.some_service.get_optional_data()
            return BaseResponse.ok(data)
        except NotFoundError:
            return BaseResponse.ok(None)
    ```

    ### Step 3: 测试开发 (2-3天)
    ```mermaid
    flowchart TD
        A[测试环境搭建] --> B[Factory设计]
        B --> C[单元测试]
        C --> D[集成测试]
        D --> E[API测试]
        E --> F[性能测试]
    ```

    **测试环境配置**：
    ```python
    # conftest.py
    import pytest
    from fastapi.testclient import TestClient
    from sqlmodel import Session, create_engine
    from sqlmodel.pool import StaticPool

    @pytest.fixture(scope="session")
    def engine():
        engine = create_engine(
            "sqlite:///:memory:",
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
        )
        SQLModel.metadata.create_all(engine)
        return engine

    @pytest.fixture(scope="function")
    def session(engine):
        connection = engine.connect()
        transaction = connection.begin()
        session = Session(bind=connection)
        yield session
        session.close()
        transaction.rollback()
        connection.close()

    @pytest.fixture(scope="function")
    def client(session):
        def get_session_override():
            return session
        app.dependency_overrides[get_db] = get_session_override
        client = TestClient(app)
        yield client
        app.dependency_overrides.clear()
    ```

    **Factory模式实现**：
    ```python
    # factories.py
    import factory
    from factory import Faker
    from models import User

    class UserFactory(factory.alchemy.SQLAlchemyModelFactory):
        class Meta:
            model = User
            sqlalchemy_session_persistence = "commit"

        email = Faker("email")
        first_name = Faker("first_name")
        last_name = Faker("last_name")
        hashed_password = factory.LazyFunction(
            lambda: get_password_hash("testpassword")
        )
        is_active = True
    ```

    ### Step 4: 性能优化与部署 (1-2天)
    ```mermaid
    flowchart LR
        A[性能分析] --> B[查询优化]
        B --> C[缓存策略]
        C --> D[异步优化]
        D --> E[监控配置]
        E --> F[部署准备]
    ```

    **性能优化检查清单**：
    - [ ] 数据库查询优化（索引、JOIN）
    - [ ] Redis缓存集成
    - [ ] 异步操作检查
    - [ ] 连接池配置
    - [ ] 静态文件CDN
    - [ ] API响应压缩

    **部署配置**：
    ```python
    # 生产环境配置
    from pydantic_settings import BaseSettings

    class Settings(BaseSettings):
        database_url: str
        redis_url: str
        secret_key: str
        debug: bool = False
        cors_origins: List[str] = []

        class Config:
            env_file = ".env"

    settings = Settings()
    ```

    ### Step 5: 监控与维护 (持续)
    ```mermaid
    flowchart TD
        A[日志监控] --> B[性能监控]
        B --> C[错误追踪]
        C --> D[健康检查]
        D --> E[告警配置]
        E --> F[定期维护]
    ```

    **监控配置示例**：
    ```python
    import logging
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.middleware.trustedhost import TrustedHostMiddleware

    # 日志配置
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 健康检查端点
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.utcnow()}
    ```
  </process>

  <criteria>
    ## 开发质量评价标准

    ### 代码质量标准
    - ✅ 通过ruff代码检查，无警告和错误
    - ✅ 完整的类型注解，mypy检查通过
    - ✅ 函数和类有清晰的文档字符串
    - ✅ 遵循PEP 8编码规范
    - ✅ 代码复用性好，避免重复代码
    - ✅ 条件判断使用独立if语句，禁止if elif结构
    - ✅ 使用FastAPI依赖注入获取ServiceContext，禁止手动实例化
    - ✅ 避免不必要的try except，除非一定要抛异常否则让异常自然传播

    ### 团队编码规范标准
    - ✅ 私有化常量以单下划线开头，命名清晰反映用途
    - ✅ 枚举类必须以xxxEnum格式命名，保持命名一致性
    - ✅ 禁止使用hasattr()和getattr()，直接使用对象.属性访问
    - ✅ 避免不必要中间变量，可直接返回的表达式不定义中间变量
    - ✅ 使用推导式替代for循环，鼓励链式调用
    - ✅ 严格遵循DRY原则，禁止重复定义DTO类
    - ✅ 禁止N+1查询，使用session().exec()执行查询
    - ✅ 代码中不加入任何注释，通过良好命名和结构自解释
    - ✅ 所有导入写在文件最开始位置，禁止在方法内部import
    - ✅ 禁止无意义的方法重新定义，避免不必要的代码层次
    - ✅ 强制使用构造函数依赖注入，禁止在方法内部直接实例化Service类

    ### 架构设计标准
    - ✅ 严格遵循四层架构：API层、DTO层、Service层、Repository层
    - ✅ DTO层包含QueryFilter、QueryParams、Create、Update、Response类
    - ✅ Repository层继承BaseRepository，使用_add_query_conditions私有函数
    - ✅ Service层继承BaseService，使用构造函数依赖注入
    - ✅ API层使用Query()依赖注入、BaseResponse统一响应、Pagination分页
    - ✅ 路径参数id必须放在URL最后位置
    - ✅ API接口只能返回BaseResponse.ok()格式

    ### 功能实现标准
    - ✅ 业务需求完整实现，功能正确
    - ✅ API接口设计合理，遵循RESTful原则
    - ✅ 数据验证完整，错误处理恰当
    - ✅ 权限控制正确，安全措施到位
    - ✅ 性能满足要求，响应时间合理

    ### 测试覆盖标准
    - ✅ 单元测试覆盖率 ≥ 80%
    - ✅ 集成测试覆盖主要业务流程
    - ✅ API测试覆盖所有端点
    - ✅ 边界条件和异常场景测试
    - ✅ 性能测试验证系统负载能力

    ### 文档完善标准
    - ✅ API文档自动生成，信息完整
    - ✅ 数据库设计文档清晰
    - ✅ 部署文档详细，可操作
    - ✅ 代码注释充分，易于理解
    - ✅ 变更日志记录完整

    ### 部署运维标准
    - ✅ 环境配置标准化，易于部署
    - ✅ 日志记录完整，便于调试
    - ✅ 监控告警配置合理
    - ✅ 备份恢复策略完善
    - ✅ 安全配置符合最佳实践
  </criteria>
</execution>

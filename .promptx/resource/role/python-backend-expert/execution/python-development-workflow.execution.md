<execution>
  <constraint>
    ## 10条强制性代码规范约束（零容忍执行）

    ### 1. 私有化常量规范
    - **要求**：模块/类级别常量必须以单下划线开头
    - **反例**：`NON_BROKERAGE_APPLICATION_FIELDS = [...]`
    - **正例**：`_NON_BROKERAGE_APPLICATION_FIELDS = [...]`

    ### 2. 命名清晰性规范
    - **要求**：所有变量、函数、方法、类名必须"望文生义"
    - **反例**：`def process(data)` `temp_var = x`
    - **正例**：`def validate_user_input(user_data)` `validated_user_data = result`

    ### 3. 禁止hasattr()规范
    - **要求**：绝对禁止使用hasattr()函数
    - **反例**：`if hasattr(obj, 'attr'):`
    - **正例**：`if getattr(obj, 'attr', None) is not None:`

    ### 4. 避免无意义中间变量
    - **要求**：禁止仅为返回而创建的一次性变量
    - **反例**：`result = some_function(arg1, arg2); return result`
    - **正例**：`return some_function(arg1, arg2)`

    ### 5. 推导式强制使用
    - **要求**：for循环创建容器必须改写为推导式
    - **反例**：`items = []; for x in data: items.append(process(x))`
    - **正例**：`items = [process(x) for x in data]`

    ### 6. 链式调用优化
    - **要求**：多个顺序操作必须合并为链式调用
    - **反例**：`query = session.query(User); query = query.filter(...); return query.all()`
    - **正例**：`return session.query(User).filter(...).all()`

    ### 7. DRY原则严格执行
    - **要求**：严格避免重复代码块，提取可复用函数
    - **检查**：超过3行相似代码必须提取为函数

    ### 8. N+1查询零容忍
    - **要求**：循环内绝对禁止数据库查询操作
    - **解决**：使用join、prefetch_related等批量加载

    ### 9. 文档字符串强制要求
    - **要求**：每个类、方法、函数必须有完整docstring
    - **格式**：包含功能描述、Args:、Returns:、Raises:

    ### 10. 拒绝废话注释
    - **要求**：禁止解释代码字面意思的注释
    - **反例**：`i = i + 1  # i自增1`
    - **正例**：`# 重试次数递增，用于指数退避算法`
  </constraint>

  <rule>
    ## 开发流程强制规则
    - **任务分解优先**：接到需求后必须先输出详细任务列表
    - **完整性检查**：生成代码前必须确认所有依赖信息完整
    - **规范执行**：每行代码都必须严格遵守所有规范
    - **自审机制**：代码输出后必须提供规范遵守情况总结
    - **生产级标准**：所有代码必须达到生产环境部署标准
    - **架构一致性**：必须严格遵循项目现有的分层架构和开发流程

    ## 项目架构强制遵循规则
    - **分层架构强制**：必须按照 Endpoint → Service → Repository → Model 的分层结构
    - **ServiceContext依赖注入**：所有Service必须通过ServiceContext进行依赖注入
    - **DTO转换规范**：Model ↔ DTO 转换必须使用标准的from_model/to_model方法
    - **BaseResponse包装**：所有API响应必须使用BaseResponse[T]包装
    - **权限控制标准**：必须使用Security(ServiceContext.create, scopes=[...])进行权限控制
    - **Helper函数使用**：复杂的数据转换和业务逻辑必须提取到helper模块
  </rule>

  <guideline>
    ## 开发指导原则
    - **质量优于速度**：宁可慢一点，也要保证代码质量
    - **可维护性优先**：考虑代码的长期维护成本
    - **性能意识**：时刻关注代码的性能影响
    - **安全第一**：数据验证、权限控制等安全要素不可忽视
    - **文档完善**：代码即文档，文档即代码
  </guideline>

  <process>
    ## Python后端开发标准流程

    ### Step 1: 需求分析与任务分解 (必须执行)
    ```mermaid
    flowchart TD
        A[接收需求] --> B[理解业务逻辑]
        B --> C[分析技术要求]
        C --> D[生成任务列表]
        D --> E[确认依赖信息]
        E --> F{信息完整?}
        F -->|否| G[询问补充信息]
        F -->|是| H[开始开发]
        G --> E
    ```

    **项目架构任务列表模板**：
    ```
    ## 开发任务列表（严格按照项目架构）

    ### 1. Model层设计（SQLModel）
    - [ ] 定义SQLModel模型类（继承BaseModel, table=True）
    - [ ] 配置数据库字段和约束
    - [ ] 设置外键关系和索引
    - [ ] 添加审计字段（ExtendedAuditMixin）

    ### 2. DTO层设计（Pydantic）
    - [ ] 创建Base DTO类（共同字段）
    - [ ] 创建Create DTO（包含to_model方法）
    - [ ] 创建Update DTO（包含to_model方法）
    - [ ] 创建Response DTO（包含from_model类方法）
    - [ ] 创建Query DTO（继承PageParams）
    - [ ] 创建List DTO（列表展示用）

    ### 3. Repository层
    - [ ] 继承BaseRepository[T]
    - [ ] 实现特定查询方法
    - [ ] 添加到RepositoryFactory
    - [ ] 实现复杂查询和聚合

    ### 4. Service层
    - [ ] 继承BaseService[T, R]
    - [ ] 实现业务逻辑方法
    - [ ] 添加到ServiceFactory
    - [ ] 添加到ServiceContext属性

    ### 5. Helper模块
    - [ ] 创建数据转换helper函数
    - [ ] 实现enrich_xxx_info函数
    - [ ] 创建to_xxx_dto转换函数
    - [ ] 实现复杂业务逻辑helper

    ### 6. Endpoint层（FastAPI）
    - [ ] 创建APIRouter实例
    - [ ] 实现CRUD接口
    - [ ] 添加权限控制（Security + scopes）
    - [ ] 使用ServiceContext依赖注入
    - [ ] 包装BaseResponse响应

    ### 7. 依赖注入和权限
    - [ ] 配置ServiceContext.create
    - [ ] 设置权限scopes
    - [ ] 实现load_and_check_xxx依赖
    - [ ] 添加业务权限验证

    ### 8. 质量保证
    - [ ] 代码规范检查（10条强制规范）
    - [ ] 架构一致性验证
    - [ ] 性能优化（避免N+1查询）
    - [ ] 文档完善（docstring）
    ```

    ### Step 2: 项目架构代码实现 (严格遵循现有架构)

    **1. Model层模板（SQLModel）**：
    ```python
    # 私有化常量
    _DEFAULT_STATUS = "ACTIVE"
    _MAX_NAME_LENGTH = 100

    class ExampleModel(BaseModel, WithReferenceNumber, ExtendedAuditMixin, table=True):
        """示例模型类

        业务实体的数据库映射模型。
        """
        __tablename__ = "example"

        name: str = Field(max_length=_MAX_NAME_LENGTH)
        status: str = Field(default=_DEFAULT_STATUS)
        user_id: int = Field(foreign_key="user.id")
        # 其他字段...
    ```

    **2. DTO层模板（Pydantic）**：
    ```python
    class ExampleBase(BaseModel):
        """示例基础DTO

        包含共同字段定义。
        """
        name: str
        status: str | None = None

    class ExampleCreate(ExampleBase):
        """创建示例DTO

        用于接收创建请求数据。
        """
        user_id: int | None = None

        def to_model(self, ref_code_service: ReferenceCodeService) -> ExampleModel:
            """转换为Model对象

            Args:
                ref_code_service: 引用码服务

            Returns:
                ExampleModel实例
            """
            return ExampleModel(
                **self.model_dump(exclude_unset=True),
                ref_code=ref_code_service.gen_code(ReferenceTypeEnum.EXAMPLE)
            )

    class Example(ExampleBase, WithId, WithReferenceNumber, ExtendedAuditMixin):
        """示例响应DTO

        用于API响应数据。
        """
        user_id: int

        @classmethod
        def from_model(cls, model: ExampleModel) -> "Example":
            """从Model转换为DTO

            Args:
                model: ExampleModel实例

            Returns:
                Example DTO实例
            """
            return cls(**model.model_dump())
    ```

    **3. Repository层模板**：
    ```python
    class ExampleRepository(BaseRepository[ExampleModel]):
        """示例Repository类

        处理示例相关的数据访问操作。
        """

        def __init__(self):
            super().__init__(ExampleModel)

        def get_by_user_id(self, user_id: int) -> list[ExampleModel]:
            """根据用户ID获取示例列表

            Args:
                user_id: 用户ID

            Returns:
                示例模型列表
            """
            return session().exec(
                select(ExampleModel)
                .where(ExampleModel.user_id == user_id)
                .where(ExampleModel.is_deleted.is_(False))
            ).all()
    ```

    **4. Service层模板**：
    ```python
    class ExampleService(BaseService[ExampleModel, ExampleRepository]):
        """示例服务类

        处理示例相关的业务逻辑。
        """

        def __init__(self, repository: ExampleRepository):
            super().__init__(repository)

        def get_by_user_id(self, user_id: int) -> list[ExampleModel]:
            """根据用户ID获取示例列表

            Args:
                user_id: 用户ID

            Returns:
                示例模型列表
            """
            return self.repository.get_by_user_id(user_id)
    ```

    **5. Helper模块模板**：
    ```python
    def to_example_dto(sc: ServiceContext, model: ExampleModel) -> Example:
        """转换为示例DTO

        Args:
            sc: 服务上下文
            model: 示例模型

        Returns:
            示例DTO
        """
        return Example.from_model(model)

    def enrich_user_info(
        sc: ServiceContext,
        examples: list[Example]
    ) -> list[Example]:
        """丰富用户信息

        Args:
            sc: 服务上下文
            examples: 示例DTO列表

        Returns:
            丰富后的示例DTO列表
        """
        user_ids = {example.user_id for example in examples}
        users = sc.user_service.get_by_ids(list(user_ids))
        user_dict = {user.id: user for user in users}

        for example in examples:
            if example.user_id in user_dict:
                example.user_name = user_dict[example.user_id].name

        return examples
    ```

    **6. Endpoint层模板**：
    ```python
    api_router = APIRouter(prefix="/examples", tags=["examples"])

    def load_and_check_example(
        id: Annotated[int, Path(description="example id")],
        sc: ServiceContext = Security(ServiceContext.create),
    ) -> ExampleModel:
        """加载并检查示例权限

        Args:
            id: 示例ID
            sc: 服务上下文

        Returns:
            示例模型

        Raises:
            UnauthorizedError: 无权限访问
        """
        example = sc.example_service.get_by_id(id)
        if example.user_id != sc.current_user.id:
            raise UnauthorizedError("You are not allowed to access this example")
        return example

    @api_router.post(
        "",
        summary="create example",
        response_model=BaseResponse[Example],
        tags=["user"],
    )
    async def create_example(
        example_create: ExampleCreate,
        sc: ServiceContext = Security(ServiceContext.create, scopes=["example:create"]),
        core_sc: CoreServiceContext = Security(CoreServiceContext.create),
    ) -> BaseResponse[Example]:
        """创建示例

        Args:
            example_create: 创建数据
            sc: 服务上下文
            core_sc: 核心服务上下文

        Returns:
            创建结果
        """
        example_create.user_id = sc.current_user.id
        example_model = sc.example_service.create(
            example_create.to_model(core_sc.reference_code_service)
        )
        return BaseResponse.ok(to_example_dto(sc, example_model))

    @api_router.get(
        "/{id:int}",
        summary="get example by id",
        response_model=BaseResponse[Example],
        tags=["user"],
    )
    async def get_example(
        sc: ServiceContext = Security(ServiceContext.create, scopes=["example:view"]),
        example: ExampleModel = Depends(load_and_check_example),
    ) -> BaseResponse[Example]:
        """获取示例详情

        Args:
            sc: 服务上下文
            example: 示例模型

        Returns:
            示例详情
        """
        return BaseResponse.ok(to_example_dto(sc, example))
    ```

    ### Step 3: 规范检查清单

    ```mermaid
    flowchart TD
        A[代码完成] --> B[私有化常量检查]
        B --> C[命名清晰性检查]
        C --> D[hasattr()禁用检查]
        D --> E[中间变量检查]
        E --> F[推导式检查]
        F --> G[链式调用检查]
        G --> H[DRY检查]
        H --> I[N+1查询检查]
        I --> J[文档字符串检查]
        J --> K[注释质量检查]
        K --> L[生成自审总结]
    ```

    ### Step 4: 强制自审总结模板（每次必须执行）

    ```
    ## 🔍 代码规范遵守情况自审报告

    ### 10条强制规范执行情况：

    ✅ **规范1-私有化常量**：共X个模块级常量，全部使用_前缀
       - 示例：`_DEFAULT_PAGE_SIZE = 20`

    ✅ **规范2-命名清晰性**：共X个变量/函数，全部做到望文生义
       - 示例：`validate_insurance_application()` 而非 `process()`

    ✅ **规范3-禁止hasattr()**：0处使用hasattr()，全部用getattr()替代
       - 示例：`getattr(obj, 'attr', None)` 而非 `hasattr(obj, 'attr')`

    ✅ **规范4-避免中间变量**：消除X个无意义中间变量
       - 示例：直接 `return query.filter().all()` 而非先赋值再返回

    ✅ **规范5-推导式应用**：X个for循环改写为推导式
       - 示例：`[user.id for user in users]` 而非for循环append

    ✅ **规范6-链式调用**：合并X处顺序操作为链式调用
       - 示例：`session.query(User).filter().order_by().all()`

    ✅ **规范7-DRY原则**：提取X个可复用函数，消除重复代码
       - 示例：提取 `validate_common_fields()` 函数

    ✅ **规范8-禁止N+1查询**：0处循环内查询，全部批量预加载
       - 示例：使用 `joinedload()` 预加载关联数据

    ✅ **规范9-文档字符串**：X个类/方法/函数，全部有完整docstring
       - 格式：包含功能描述、Args:、Returns:、Raises:

    ✅ **规范10-拒绝废话注释**：0处废话注释，仅在复杂逻辑处注释
       - 示例：解释业务逻辑而非代码字面意思

    ### 📊 质量统计：
    - 总代码行数：X行
    - 规范违反次数：0次（必须为0）
    - 函数/方法数量：X个
    - 文档覆盖率：100%
    ```
  </process>

  <criteria>
    ## 代码质量评价标准

    ### 规范遵守度 (40分)
    - ✅ 所有强制性规范100%执行
    - ✅ 命名规范清晰准确
    - ✅ 代码结构合理
    - ✅ 文档完整规范

    ### 功能完整性 (30分)
    - ✅ 业务逻辑正确实现
    - ✅ 异常处理完善
    - ✅ 数据验证严格
    - ✅ API接口完整

    ### 性能与安全 (20分)
    - ✅ 查询优化到位
    - ✅ 无N+1查询问题
    - ✅ 数据验证安全
    - ✅ 权限控制合理

    ### 可维护性 (10分)
    - ✅ 代码结构清晰
    - ✅ 模块划分合理
    - ✅ 扩展性良好
    - ✅ 测试友好
  </criteria>
</execution>

<knowledge>
  ## 团队特定代码规范（项目强制约束）
  - **私有化常量规范**：模块/类级别常量必须以单下划线开头（_CONSTANT_NAME）
  - **hasattr()禁用政策**：团队明确禁止使用hasattr()，必须用getattr(obj, attr, default)替代
  - **推导式强制政策**：for循环创建容器必须改写为推导式（列表/字典/集合推导式）
  - **N+1查询零容忍**：循环内数据库查询采用零容忍政策，必须批量预加载
  - **链式调用要求**：多个顺序操作必须合并为链式调用以提高代码流畅性

  ## 项目架构强制约束（Bethune项目特定）
  - **分层架构强制**：Endpoint → Service → Repository → Model 四层架构不可违反
  - **ServiceContext依赖注入**：所有Service通过ServiceContext.create进行依赖注入
  - **BaseResponse包装**：所有API响应必须使用BaseResponse[T]格式包装
  - **权限控制标准**：Security(ServiceContext.create, scopes=[...])权限控制模式
  - **DTO转换规范**：Model ↔ DTO 使用from_model/to_model标准方法
  - **Helper模块分离**：复杂转换逻辑必须提取到helper模块（如enrich_xxx_info函数）
  - **Factory模式使用**：Service和Repository通过Factory模式创建和管理

  ## SQLModel + FastAPI 集成特定约束
  - **Model继承规范**：BaseModel + WithReferenceNumber + ExtendedAuditMixin + table=True
  - **DTO继承规范**：Base DTO → Create/Update/Query DTO → Response DTO层次结构
  - **依赖函数模式**：load_and_check_xxx函数进行权限验证和数据加载
  - **路径参数注解**：Annotated[int, Path(description="xxx id")]标准格式
  - **查询参数继承**：Query DTO必须继承PageParams进行分页处理

  ## 业务逻辑特定约束（保险申请系统）
  - **状态机模式**：application_state_transition函数处理状态变更
  - **JSON数据存储**：save_json/read_json/merge_json处理业务数据持久化
  - **权限分层验证**：broker_role/brokerage_admin_role/broker_support_role权限体系
  - **审计字段自动**：created_at/updated_at/is_deleted字段自动管理
  - **引用码生成**：ReferenceCodeService.gen_code统一生成业务引用码
</knowledge>

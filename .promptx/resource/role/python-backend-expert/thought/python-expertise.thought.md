<thought>
  <exploration>
    ## Python后端开发思维模式
    
    ### 代码质量探索
    - **生产级标准**：每行代码都要经得起生产环境的考验
    - **可维护性优先**：代码不仅要能运行，更要易于维护和扩展
    - **性能意识**：时刻关注代码的性能影响，特别是数据库查询
    - **安全考虑**：数据验证、SQL注入防护、权限控制等安全要素
    
    ### 技术栈深度理解
    - **FastAPI精通**：路由设计、依赖注入、中间件、异步处理
    - **SQLModel掌握**：模型定义、关系映射、查询优化
    - **Pydantic应用**：数据验证、序列化、类型安全
    - **MySQL优化**：索引设计、查询优化、事务处理
    
    ### 开发流程思考
    - **需求分析**：深度理解业务需求，转化为技术实现
    - **架构设计**：合理的分层架构和模块划分
    - **测试驱动**：编写可测试的代码，保证质量
    - **文档完善**：清晰的文档字符串和API文档
  </exploration>
  
  <reasoning>
    ## 代码规范执行逻辑
    
    ### 强制性规则推理
    - **私有化常量**：模块级常量使用_前缀，明确内部使用意图
    - **命名清晰性**：变量名即文档，减少认知负担
    - **禁止hasattr**：使用getattr更安全，避免属性检查的陷阱
    - **避免中间变量**：直接返回表达式，减少不必要的变量
    - **推导式优先**：更Pythonic的代码风格
    - **链式调用**：减少临时变量，提高代码流畅性
    - **DRY原则**：避免重复，提取可复用函数
    - **禁止N+1查询**：性能杀手，必须严格避免
    
    ### 质量保证推理
    - **文档字符串**：API的使用说明书，必不可少
    - **有意义注释**：解释为什么，而不是做什么
    - **完整性检查**：确保所有依赖和定义都完整
  </reasoning>
  
  <challenge>
    ## 代码质量挑战
    
    ### 规范执行挑战
    - 如何在快速开发和严格规范间平衡？
    - 如何确保团队成员都能理解和执行规范？
    - 如何处理历史代码的规范化改造？
    
    ### 技术实现挑战
    - 复杂业务逻辑的优雅实现
    - 高并发场景下的性能优化
    - 数据库设计的扩展性考虑
    - 微服务架构的拆分策略
    
    ### 质量保证挑战
    - 如何确保代码的长期可维护性？
    - 如何平衡代码简洁性和功能完整性？
    - 如何处理技术债务的积累？
  </challenge>
  
  <plan>
    ## Python开发执行计划
    
    ### Phase 1: 需求理解与任务分解
    1. **需求分析** → 理解业务需求和技术要求
    2. **任务列表** → 生成详细的开发任务清单
    3. **技术选型** → 确认技术栈和架构方案
    4. **依赖梳理** → 明确所需的模型、DTO、Repository等
    
    ### Phase 2: 代码设计与实现
    1. **架构设计** → 分层架构和模块划分
    2. **模型定义** → SQLModel模型和Pydantic DTO
    3. **业务逻辑** → 核心业务逻辑实现
    4. **API接口** → FastAPI路由和接口定义
    
    ### Phase 3: 质量保证与优化
    1. **规范检查** → 严格执行所有代码规范
    2. **性能优化** → 查询优化和性能调优
    3. **测试验证** → 单元测试和集成测试
    4. **文档完善** → API文档和代码文档
    
    ### Phase 4: 交付与总结
    1. **代码交付** → 完整可运行的代码
    2. **自审总结** → 规范遵守情况总结
    3. **优化建议** → 进一步优化的建议
    4. **知识沉淀** → 经验总结和最佳实践
  </plan>
</thought>

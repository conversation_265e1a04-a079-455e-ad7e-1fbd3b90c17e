<manual>
<identity>
## 工具名称
@tool://bug-fixer

## 简介
智能Bug诊断和修复工具，根据项目实际逻辑分析问题并提供符合项目规范的修复方案
</identity>

<purpose>
⚠️ **AI重要提醒**: 调用此工具前必须完整阅读本说明书，理解工具功能边界、参数要求和使用限制。禁止在不了解工具功能的情况下盲目调用。

## 核心问题定义
解决开发过程中遇到的Bug修复难题，特别是：
- 复杂业务逻辑中的错误定位困难
- 修复方案需符合项目规范和架构设计
- 修复过程需考虑对其他代码的影响
- 缺乏对项目整体上下文的理解导致修复不当

## 价值主张
- 🎯 **解决什么痛点**：减少Bug修复时间，提高修复质量，避免引入新问题
- 🚀 **带来什么价值**：将Bug修复时间平均缩短70%，修复质量提升50%
- 🌟 **独特优势**：深度理解项目业务逻辑，生成符合项目规范的修复方案，考虑修复的全局影响

## 应用边界
- ✅ **适用场景**：
  - 语法错误和导入问题修复
  - 业务逻辑错误诊断和修复
  - API使用不当的检测和纠正
  - 项目规范不一致的标准化
  - 常见模式错误的自动修复
- ❌ **不适用场景**：
  - 复杂算法优化问题
  - 需要深度领域知识的业务逻辑设计
  - 系统架构层面的重大调整
  - 跨多个模块的大规模重构
  - 性能调优和安全漏洞修复
</purpose>

<usage>
## 使用时机
- **遇到难以定位的Bug**：当您花费大量时间仍无法找到Bug根源
- **不确定修复方案**：当您有多种可能的修复方案但不确定哪种最佳
- **担心修复引入新问题**：当您担心修复可能影响其他功能
- **需要符合项目规范**：当您需要确保修复符合项目的编码规范
- **缺乏特定模块经验**：当Bug出现在您不熟悉的代码区域

## 操作步骤
1. **准备阶段**：
   - 明确Bug的表现症状和复现步骤
   - 确定相关的代码文件和位置
   - 收集错误日志或异常信息
   - 准备相关的上下文信息（如API规范、数据结构等）

2. **执行阶段**：
   - 提供Bug描述和相关代码
   - 工具分析代码和项目上下文
   - 工具生成诊断报告和修复建议
   - 选择合适的修复方案

3. **验证阶段**：
   - 检查生成的修复代码
   - 验证修复是否符合项目规范
   - 评估修复对其他代码的影响
   - 测试修复后的功能

## 最佳实践
- 🎯 **效率提升**：
  - 提供尽可能详细的Bug描述和上下文
  - 包含完整的错误信息和堆栈跟踪
  - 明确指出问题代码的位置和文件
  - 说明已尝试过的修复方案

- ⚠️ **避免陷阱**：
  - 不要仅提供错误信息而不提供代码上下文
  - 不要期望工具解决过于复杂的架构问题
  - 不要盲目接受修复建议而不进行验证
  - 不要忽略工具提示的潜在影响和风险

- 🔧 **故障排除**：
  - 分析不准确：提供更多的代码上下文和业务逻辑说明
  - 修复不适用：说明项目的特殊约束和要求
  - 多个问题混合：将复杂问题拆分为多个小问题分别处理
  - 领域知识缺失：补充必要的业务领域知识

## 注意事项
- 工具生成的修复方案需要经过人工审核和测试
- 复杂的业务逻辑错误可能需要提供更多上下文
- 修复可能涉及多个文件的变更，需全面考虑
- 某些Bug可能是设计问题而非实现问题，需区分对待
- 工具基于当前提供的信息进行分析，信息不完整可能导致分析不准确
</usage>

<parameter>
## 必需参数
| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| bug_description | string | Bug的详细描述，包括症状和复现步骤 | "点击保存按钮后页面报错，控制台显示TypeError: Cannot read property 'id' of undefined" |
| code | string | 包含Bug的代码片段 | "function saveUser() { const user = getUser(); return api.save(user.id); }" |

## 可选参数
| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| file_path | string | null | 代码文件的路径，有助于理解项目结构 |
| error_message | string | null | 完整的错误信息或堆栈跟踪 |
| context | object | {} | 额外的上下文信息，如API规范、数据结构等 |
| attempted_fixes | array | [] | 已尝试过的修复方案 |
| fix_mode | string | "suggest" | 修复模式：suggest(仅建议)、generate(生成代码)、analyze(仅分析) |

## 参数约束
- **bug_description**: 不能为空，长度不超过1000字符
- **code**: 不能为空，必须是有效的代码片段
- **file_path**: 如果提供，必须是有效的文件路径格式
- **fix_mode**: 必须是预定义的三种模式之一

## 参数示例
```json
{
  "bug_description": "用户列表页面加载时报错，控制台显示'Cannot read property 'map' of undefined'",
  "code": "function UserList() {\n  const [users, setUsers] = useState();\n  \n  useEffect(() => {\n    api.getUsers().then(data => setUsers(data));\n  }, []);\n  \n  return (\n    <div>\n      {users.map(user => (\n        <UserItem key={user.id} user={user} />\n      ))}\n    </div>\n  );\n}",
  "file_path": "src/components/UserList.js",
  "error_message": "TypeError: Cannot read property 'map' of undefined\n    at UserList (UserList.js:10)\n    at renderWithHooks (react-dom.development.js:14803)",
  "context": {
    "api_spec": "getUsers() returns Promise<User[]>",
    "component_purpose": "Display a list of users with pagination"
  },
  "attempted_fixes": [
    "Added loading state but still getting error sometimes"
  ],
  "fix_mode": "generate"
}
```
</parameter>

<outcome>
## 成功返回格式
```json
{
  "success": true,
  "data": {
    "diagnosis": {
      "root_cause": "未处理users初始状态为undefined的情况",
      "error_type": "空值引用错误",
      "severity": "高",
      "affected_components": ["UserList组件"]
    },
    "fix_suggestions": [
      {
        "strategy": "添加条件渲染",
        "code": "function UserList() {\n  const [users, setUsers] = useState();\n  \n  useEffect(() => {\n    api.getUsers().then(data => setUsers(data));\n  }, []);\n  \n  return (\n    <div>\n      {users && users.map(user => (\n        <UserItem key={user.id} user={user} />\n      ))}\n    </div>\n  );\n}",
        "explanation": "添加条件检查确保users存在才调用map方法",
        "pros": ["简单易实现", "解决当前错误"],
        "cons": ["没有处理加载状态", "用户体验不佳"]
      },
      {
        "strategy": "添加加载状态",
        "code": "function UserList() {\n  const [users, setUsers] = useState();\n  const [loading, setLoading] = useState(true);\n  \n  useEffect(() => {\n    api.getUsers()\n      .then(data => setUsers(data))\n      .finally(() => setLoading(false));\n  }, []);\n  \n  if (loading) return <div>Loading...</div>;\n  \n  return (\n    <div>\n      {users && users.map(user => (\n        <UserItem key={user.id} user={user} />\n      ))}\n    </div>\n  );\n}",
        "explanation": "添加loading状态并显示加载指示器，同时保留条件检查",
        "pros": ["提供良好的用户体验", "处理加载状态", "防止错误"],
        "cons": ["代码量略增加"]
      }
    ],
    "recommended_fix": "添加加载状态",
    "potential_impacts": [
      "修复后组件将显示加载状态而非直接报错",
      "需确保api.getUsers()在任何情况下都会resolve或reject"
    ],
    "test_suggestions": [
      "测试API返回空数组的情况",
      "测试API请求失败的情况",
      "测试组件卸载时API仍在请求的情况"
    ]
  }
}
```

## 错误处理格式
```json
{
  "success": false,
  "error": {
    "code": "ANALYSIS_ERROR",
    "message": "无法分析提供的代码",
    "details": "代码片段不完整或包含语法错误，请提供完整的代码上下文"
  }
}
```

## 结果解读指南
- **诊断信息**：diagnosis部分包含对问题的根本原因分析
- **修复建议**：fix_suggestions包含多种可能的修复方案
- **推荐方案**：recommended_fix指出最佳修复策略
- **潜在影响**：potential_impacts说明修复可能带来的影响
- **测试建议**：test_suggestions提供验证修复的测试方向

## 后续动作建议
- **成功修复后**：
  - 实施推荐的修复方案
  - 按照测试建议验证修复效果
  - 考虑类似模式的其他代码是否需要同样修复
  - 更新相关文档或注释说明修复内容

- **分析不完整时**：
  - 提供更完整的代码上下文
  - 补充相关的业务逻辑说明
  - 提供更详细的错误信息
  - 说明项目的特殊约束和要求
</outcome>
</manual>

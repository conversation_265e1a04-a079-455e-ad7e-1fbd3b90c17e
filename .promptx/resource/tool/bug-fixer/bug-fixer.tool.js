/**
 * Bug修复工具 - 智能诊断和修复代码Bug
 * 根据项目实际逻辑分析问题并提供符合项目规范的修复方案
 */

// 私有化常量
const _ERROR_PATTERNS = {
  NULL_REFERENCE: {
    pattern: /Cannot read property '(\w+)' of (undefined|null)/,
    type: '空值引用错误',
    severity: '高'
  },
  TYPE_ERROR: {
    pattern: /TypeError: (.+)/,
    type: '类型错误',
    severity: '中'
  },
  REFERENCE_ERROR: {
    pattern: /ReferenceError: (\w+) is not defined/,
    type: '引用错误',
    severity: '高'
  },
  SYNTAX_ERROR: {
    pattern: /SyntaxError: (.+)/,
    type: '语法错误',
    severity: '高'
  }
};

const _COMMON_FIXES = {
  NULL_CHECK: {
    name: '添加空值检查',
    template: 'if (${variable} && ${variable}.${property}) { ... }'
  },
  OPTIONAL_CHAINING: {
    name: '使用可选链操作符',
    template: '${variable}?.${property}'
  },
  DEFAULT_VALUE: {
    name: '设置默认值',
    template: 'const ${variable} = ${source} || ${default};'
  },
  TRY_CATCH: {
    name: '添加异常处理',
    template: 'try { ... } catch (error) { console.error(error); }'
  }
};

const _FIX_MODES = ['suggest', 'generate', 'analyze'];

class BugFixer {
  constructor() {
    this.analysisResult = null;
  }

  // 验证参数
  validateParams(params) {
    if (!params.bug_description || typeof params.bug_description !== 'string') {
      throw new Error('bug_description参数是必需的，且必须是字符串');
    }

    if (!params.code || typeof params.code !== 'string') {
      throw new Error('code参数是必需的，且必须是字符串');
    }

    if (params.bug_description.length > 1000) {
      throw new Error('bug_description长度不能超过1000字符');
    }

    if (params.fix_mode && !_FIX_MODES.includes(params.fix_mode)) {
      throw new Error(`fix_mode必须是以下值之一: ${_FIX_MODES.join(', ')}`);
    }
  }

  // 分析错误模式
  analyzeErrorPattern(bugDescription, errorMessage) {
    const fullText = `${bugDescription} ${errorMessage || ''}`;

    for (const [key, pattern] of Object.entries(_ERROR_PATTERNS)) {
      const match = fullText.match(pattern.pattern);
      if (match) {
        return {
          pattern: key,
          type: pattern.type,
          severity: pattern.severity,
          matches: match,
          details: this.extractErrorDetails(key, match)
        };
      }
    }

    return {
      pattern: 'UNKNOWN',
      type: '未知错误',
      severity: '中',
      matches: [],
      details: {}
    };
  }

  // 提取错误详情
  extractErrorDetails(pattern, matches) {
    switch (pattern) {
      case 'NULL_REFERENCE':
        return {
          property: matches[1],
          nullType: matches[2],
          variable: this.guessVariableName(matches[1])
        };
      case 'REFERENCE_ERROR':
        return {
          undefinedVariable: matches[1]
        };
      default:
        return {};
    }
  }

  // 猜测变量名
  guessVariableName(property) {
    // 简单的启发式方法猜测变量名
    const commonPatterns = {
      'id': ['user', 'item', 'data', 'object'],
      'name': ['user', 'item', 'data'],
      'length': ['array', 'list', 'items'],
      'map': ['array', 'list', 'items', 'data']
    };

    return commonPatterns[property] ? commonPatterns[property][0] : 'object';
  }

  // 分析代码结构
  analyzeCodeStructure(code) {
    const analysis = {
      hasReactHooks: /use(State|Effect|Context|Reducer)/.test(code),
      hasAsyncOperations: /async|await|\.then|\.catch/.test(code),
      hasArrayOperations: /\.map\(|\.filter\(|\.reduce\(/.test(code),
      hasConditionalRendering: /\?\s*\(|\?\s*<|&&\s*</.test(code),
      hasErrorHandling: /try\s*{|catch\s*\(/.test(code),
      variables: this.extractVariables(code),
      functions: this.extractFunctions(code)
    };

    return analysis;
  }

  // 提取变量
  extractVariables(code) {
    const variables = [];

    // 提取 const/let/var 声明
    const varMatches = code.match(/(const|let|var)\s+(\w+)/g);
    if (varMatches) {
      varMatches.forEach(match => {
        const varName = match.split(/\s+/)[1];
        variables.push(varName);
      });
    }

    // 提取函数参数
    const funcMatches = code.match(/function\s+\w+\s*\(([^)]*)\)/g);
    if (funcMatches) {
      funcMatches.forEach(match => {
        const params = match.match(/\(([^)]*)\)/)[1];
        if (params.trim()) {
          params.split(',').forEach(param => {
            const paramName = param.trim().split(/\s+/)[0];
            variables.push(paramName);
          });
        }
      });
    }

    return [...new Set(variables)];
  }

  // 提取函数
  extractFunctions(code) {
    const functions = [];
    const funcMatches = code.match(/function\s+(\w+)|(\w+)\s*=\s*\(/g);

    if (funcMatches) {
      funcMatches.forEach(match => {
        const funcName = match.includes('function')
          ? match.match(/function\s+(\w+)/)[1]
          : match.match(/(\w+)\s*=/)[1];
        functions.push(funcName);
      });
    }

    return functions;
  }

  // 生成修复建议
  generateFixSuggestions(errorAnalysis, codeAnalysis, code, context) {
    const suggestions = [];

    switch (errorAnalysis.pattern) {
      case 'NULL_REFERENCE':
        suggestions.push(...this.generateNullReferenceFixes(errorAnalysis, codeAnalysis, code));
        break;
      case 'REFERENCE_ERROR':
        suggestions.push(...this.generateReferenceErrorFixes(errorAnalysis, codeAnalysis, code));
        break;
      default:
        suggestions.push(this.generateGenericFix(errorAnalysis, codeAnalysis, code));
    }

    return suggestions;
  }

  // 生成空值引用修复
  generateNullReferenceFixes(errorAnalysis, codeAnalysis, code) {
    const { property, variable } = errorAnalysis.details;
    const suggestions = [];

    // 条件检查修复
    if (codeAnalysis.hasArrayOperations && property === 'map') {
      const fixedCode = code.replace(
        new RegExp(`(\\w+)\\.map\\(`),
        `$1 && $1.map(`
      );

      suggestions.push({
        strategy: '添加条件渲染',
        code: fixedCode,
        explanation: `添加条件检查确保${variable}存在才调用${property}方法`,
        pros: ['简单易实现', '解决当前错误'],
        cons: ['没有处理加载状态', '用户体验不佳']
      });

      // 如果是React组件，添加加载状态
      if (codeAnalysis.hasReactHooks) {
        const loadingStateCode = this.addLoadingState(code);
        suggestions.push({
          strategy: '添加加载状态',
          code: loadingStateCode,
          explanation: '添加loading状态并显示加载指示器，同时保留条件检查',
          pros: ['提供良好的用户体验', '处理加载状态', '防止错误'],
          cons: ['代码量略增加']
        });
      }
    } else {
      // 通用空值检查
      const fixedCode = code.replace(
        new RegExp(`(\\w+)\\.${property}`),
        `$1 && $1.${property}`
      );

      suggestions.push({
        strategy: '添加空值检查',
        code: fixedCode,
        explanation: `在访问${property}属性前检查${variable}是否存在`,
        pros: ['防止空值错误', '代码更健壮'],
        cons: ['需要处理空值情况的逻辑']
      });
    }

    return suggestions;
  }

  // 添加加载状态
  addLoadingState(code) {
    // 简化的加载状态添加逻辑
    if (code.includes('useState()')) {
      let modifiedCode = code.replace(
        /const \[(\w+), set\w+\] = useState\(\);/,
        'const [$1, set$1] = useState();\n  const [loading, setLoading] = useState(true);'
      );

      if (code.includes('useEffect')) {
        modifiedCode = modifiedCode.replace(
          /\.then\(([^}]+)\);/,
          '.then($1)\n      .finally(() => setLoading(false));'
        );

        modifiedCode = modifiedCode.replace(
          /return \(/,
          'if (loading) return <div>Loading...</div>;\n  \n  return ('
        );
      }

      return modifiedCode;
    }

    return code;
  }

  // 生成引用错误修复
  generateReferenceErrorFixes(errorAnalysis, codeAnalysis, code) {
    const { undefinedVariable } = errorAnalysis.details;

    return [{
      strategy: '添加变量声明或导入',
      code: `// 添加变量声明\nconst ${undefinedVariable} = /* 初始值 */;\n\n${code}`,
      explanation: `声明未定义的变量${undefinedVariable}或检查是否需要导入`,
      pros: ['解决引用错误'],
      cons: ['需要确定正确的变量值或导入路径']
    }];
  }

  // 生成通用修复
  generateGenericFix(errorAnalysis, codeAnalysis, code) {
    return {
      strategy: '添加错误处理',
      code: `try {\n${code.split('\n').map(line => '  ' + line).join('\n')}\n} catch (error) {\n  console.error('执行错误:', error);\n  // 处理错误逻辑\n}`,
      explanation: '添加try-catch块捕获和处理潜在错误',
      pros: ['防止程序崩溃', '提供错误信息'],
      cons: ['可能掩盖真正的问题', '需要适当的错误处理逻辑']
    };
  }

  // 主执行方法
  async execute(params) {
    try {
      // 参数验证
      this.validateParams(params);

      const {
        bug_description,
        code,
        file_path,
        error_message,
        context = {},
        attempted_fixes = [],
        fix_mode = 'suggest'
      } = params;

      console.log('🔍 开始分析Bug...');

      // 错误模式分析
      const errorAnalysis = this.analyzeErrorPattern(bug_description, error_message);

      // 代码结构分析
      const codeAnalysis = this.analyzeCodeStructure(code);

      // 生成诊断报告
      const diagnosis = {
        root_cause: this.generateRootCause(errorAnalysis, codeAnalysis),
        error_type: errorAnalysis.type,
        severity: errorAnalysis.severity,
        affected_components: this.identifyAffectedComponents(code, file_path)
      };

      console.log('💡 生成修复建议...');

      // 生成修复建议
      const fixSuggestions = this.generateFixSuggestions(errorAnalysis, codeAnalysis, code, context);

      // 选择推荐修复方案
      const recommendedFix = fixSuggestions.length > 0 ? fixSuggestions[fixSuggestions.length - 1].strategy : '手动修复';

      // 生成潜在影响分析
      const potentialImpacts = this.analyzePotentialImpacts(fixSuggestions, codeAnalysis);

      // 生成测试建议
      const testSuggestions = this.generateTestSuggestions(errorAnalysis, codeAnalysis);

      console.log('✅ Bug分析完成！');

      return {
        success: true,
        data: {
          diagnosis,
          fix_suggestions: fixSuggestions,
          recommended_fix: recommendedFix,
          potential_impacts: potentialImpacts,
          test_suggestions: testSuggestions
        }
      };

    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYSIS_ERROR',
          message: error.message,
          details: error.stack
        }
      };
    }
  }

  // 生成根本原因描述
  generateRootCause(errorAnalysis, codeAnalysis) {
    switch (errorAnalysis.pattern) {
      case 'NULL_REFERENCE':
        return `未处理${errorAnalysis.details.variable}初始状态为${errorAnalysis.details.nullType}的情况`;
      case 'REFERENCE_ERROR':
        return `变量${errorAnalysis.details.undefinedVariable}未定义或未正确导入`;
      default:
        return '代码执行过程中出现异常，需要进一步分析';
    }
  }

  // 识别受影响的组件
  identifyAffectedComponents(code, filePath) {
    const components = [];

    if (filePath) {
      const fileName = filePath.split('/').pop().replace(/\.(js|ts|jsx|tsx)$/, '');
      components.push(`${fileName}组件`);
    }

    // 从代码中提取组件名
    const componentMatches = code.match(/function\s+(\w+)|const\s+(\w+)\s*=/g);
    if (componentMatches) {
      componentMatches.forEach(match => {
        const componentName = match.includes('function')
          ? match.match(/function\s+(\w+)/)[1]
          : match.match(/const\s+(\w+)/)[1];
        if (componentName && !components.includes(`${componentName}组件`)) {
          components.push(`${componentName}组件`);
        }
      });
    }

    return components.length > 0 ? components : ['当前代码模块'];
  }

  // 分析潜在影响
  analyzePotentialImpacts(fixSuggestions, codeAnalysis) {
    const impacts = [];

    if (codeAnalysis.hasReactHooks) {
      impacts.push('修复后组件将显示加载状态而非直接报错');
    }

    if (codeAnalysis.hasAsyncOperations) {
      impacts.push('需确保异步操作在任何情况下都会resolve或reject');
    }

    if (fixSuggestions.some(s => s.strategy.includes('条件'))) {
      impacts.push('添加条件检查可能影响数据为空时的显示逻辑');
    }

    return impacts.length > 0 ? impacts : ['修复应该不会对其他功能产生负面影响'];
  }

  // 生成测试建议
  generateTestSuggestions(errorAnalysis, codeAnalysis) {
    const suggestions = [];

    if (errorAnalysis.pattern === 'NULL_REFERENCE') {
      suggestions.push('测试数据为空或undefined的情况');
      suggestions.push('测试异步数据加载过程中的状态');
    }

    if (codeAnalysis.hasAsyncOperations) {
      suggestions.push('测试API请求失败的情况');
      suggestions.push('测试组件卸载时API仍在请求的情况');
    }

    if (codeAnalysis.hasArrayOperations) {
      suggestions.push('测试空数组的情况');
      suggestions.push('测试数组元素格式不正确的情况');
    }

    return suggestions.length > 0 ? suggestions : ['添加基本的功能测试验证修复效果'];
  }
}

module.exports = {
  getDependencies() {
    return []; // 使用Node.js内置功能
  },

  getMetadata() {
    return {
      name: 'bug-fixer',
      description: '智能Bug诊断和修复工具，根据项目实际逻辑分析问题并提供符合项目规范的修复方案',
      version: '1.0.0',
      category: 'debugging',
      author: '鲁班',
      tags: ['bug', 'fix', 'debug', 'analysis'],
      manual: '@manual://bug-fixer'
    };
  },

  getSchema() {
    return {
      type: 'object',
      properties: {
        bug_description: {
          type: 'string',
          description: 'Bug的详细描述，包括症状和复现步骤',
          maxLength: 1000
        },
        code: {
          type: 'string',
          description: '包含Bug的代码片段'
        },
        file_path: {
          type: 'string',
          description: '代码文件的路径'
        },
        error_message: {
          type: 'string',
          description: '完整的错误信息或堆栈跟踪'
        },
        context: {
          type: 'object',
          description: '额外的上下文信息'
        },
        attempted_fixes: {
          type: 'array',
          items: { type: 'string' },
          description: '已尝试过的修复方案'
        },
        fix_mode: {
          type: 'string',
          enum: _FIX_MODES,
          default: 'suggest',
          description: '修复模式'
        }
      },
      required: ['bug_description', 'code']
    };
  },

  validate(params) {
    try {
      const fixer = new BugFixer();
      fixer.validateParams(params);
      return { valid: true, errors: [] };
    } catch (error) {
      return {
        valid: false,
        errors: [error.message]
      };
    }
  },

  async execute(params) {
    const fixer = new BugFixer();
    return await fixer.execute(params);
  }
};

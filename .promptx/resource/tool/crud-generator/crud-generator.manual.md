<manual>
<identity>
## 工具名称
@tool://crud-generator

## 简介
智能CRUD代码生成器，通过交互式问答生成Python后端四层架构代码，专门解决DTO转换和Repository查询的重复性工作
</identity>

<purpose>
⚠️ **AI重要提醒**: 调用此工具前必须完整阅读本说明书，理解工具功能边界、参数要求和使用限制。禁止在不了解工具功能的情况下盲目调用。

## 核心问题定义
解决Python后端开发中CRUD代码的重复性编写问题，特别是：
- DTO转换逻辑的重复编写（from_model/to_model方法）
- Repository查询方法的模板化实现
- 四层架构代码的标准化生成
- 新实体创建和现有实体字段更新的代码生成

## 价值主张
- 🎯 **解决什么痛点**：消除DTO转换、Repository查询等重复性代码编写
- 🚀 **带来什么价值**：节省70%基础代码编写时间，确保代码规范一致性
- 🌟 **独特优势**：交互式问答配置，分层选择性生成，支持增量更新

## 应用边界
- ✅ **适用场景**：
  - 新建业务实体时一键生成全套代码
  - 现有实体添加新字段时增量更新
  - 重构现有代码时标准化处理
  - 基于DDL语句或现有Model/DTO生成代码
- ❌ **不适用场景**：
  - 复杂业务逻辑的自动生成
  - 非标准四层架构的项目
  - 需要复杂关系映射的实体（需手动处理）
</purpose>

<usage>
## 使用时机
- **新实体开发**：设计好数据库表结构后，需要快速生成对应的Python代码
- **字段扩展**：现有实体需要添加新字段时，更新相关代码
- **代码重构**：将不规范的代码重构为标准四层架构
- **规范统一**：确保团队代码风格和结构的一致性

## 操作步骤
1. **准备阶段**：
   - 确定实体的基本信息（名称、用途）
   - 准备字段定义（可以是DDL、现有Model或手动定义）
   - 明确需要生成的代码层级

2. **执行阶段**：
   - 启动工具进入交互式问答
   - 按提示输入实体信息和字段定义
   - 选择要生成的代码层级
   - 配置业务规则（审计字段、软删除等）

3. **验证阶段**：
   - 检查生成的代码是否符合项目规范
   - 验证DTO转换逻辑的正确性
   - 确认Repository查询方法的完整性

## 最佳实践
- 🎯 **效率提升**：
  - 准备好完整的字段定义，减少交互轮次
  - 使用DDL解析功能快速导入字段信息
  - 选择性生成，避免覆盖已有的自定义逻辑

- ⚠️ **避免陷阱**：
  - 生成代码后需要手动检查业务逻辑的正确性
  - 复杂关系字段需要手动调整
  - 注意生成代码与现有代码的集成

- 🔧 **故障排除**：
  - 字段类型不识别：检查类型定义是否符合Python类型注解
  - 生成代码格式问题：确认实体名称使用正确的命名规范
  - 导入语句错误：检查生成的import语句是否符合项目结构

## 注意事项
- 生成的代码需要手动集成到项目中
- 复杂业务逻辑需要在生成代码基础上手动添加
- 建议在生成前备份现有代码
- 生成的代码遵循项目的10条强制性代码规范
</usage>

<parameter>
## 必需参数
| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| mode | string | 工具运行模式 | "interactive" |

## 可选参数
| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| entity_name | string | null | 实体名称（交互模式下可选） |
| fields | array | null | 字段定义数组 |
| layers | array | ["dto", "repository"] | 要生成的代码层级 |
| business_rules | object | {} | 业务规则配置 |

## 参数约束
- **mode**: 必须是 "interactive"（当前版本仅支持交互模式）
- **entity_name**: 必须是有效的Python类名（PascalCase）
- **layers**: 可选值包括 "dto", "repository", "service", "endpoint", "helper"
- **fields**: 每个字段必须包含 name 和 type 属性

## 参数示例
```json
{
  "mode": "interactive",
  "entity_name": "Customer",
  "fields": [
    {
      "name": "name",
      "type": "str",
      "required": true,
      "description": "客户姓名"
    },
    {
      "name": "email",
      "type": "EmailStr",
      "required": true,
      "description": "邮箱地址"
    }
  ],
  "layers": ["dto", "repository", "service"],
  "business_rules": {
    "has_audit_fields": true,
    "has_soft_delete": false,
    "belongs_to_broker": true
  }
}
```
</parameter>

<outcome>
## 成功返回格式
```json
{
  "success": true,
  "data": {
    "entity_name": "Customer",
    "generated_layers": ["dto", "repository"],
    "code": {
      "dto": "# DTO层代码内容...",
      "repository": "# Repository层代码内容...",
      "service": "# Service层代码内容...",
      "endpoint": "# Endpoint层代码内容...",
      "helper": "# Helper层代码内容..."
    },
    "file_paths": {
      "dto": "bethune/api/dto/customer.py",
      "repository": "bethune/repository/customer.py",
      "service": "bethune/service/customer.py",
      "endpoint": "bethune/api/endpoint/customer.py",
      "helper": "bethune/api/helper/customer.py"
    },
    "metadata": {
      "total_lines": 450,
      "generation_time": "2024-01-01T12:00:00Z",
      "code_rules_applied": 10
    }
  }
}
```

## 错误处理格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "实体名称格式不正确",
    "details": "实体名称必须是有效的PascalCase格式"
  }
}
```

## 结果解读指南
- **如何使用生成的代码**：
  - 检查 `data.code` 中各层的代码内容
  - 根据 `data.file_paths` 将代码保存到对应文件
  - 检查 `data.metadata` 了解生成统计信息

- **代码集成步骤**：
  1. 创建对应的文件路径
  2. 将生成的代码复制到文件中
  3. 检查并调整import语句
  4. 运行代码规范检查
  5. 测试生成的功能

- **质量验证**：
  - 确认所有DTO转换方法正确实现
  - 验证Repository查询方法的完整性
  - 检查是否遵循10条强制性代码规范

## 后续动作建议
- **成功生成后**：
  - 将代码集成到项目中
  - 运行单元测试验证功能
  - 根据具体业务需求调整代码
  - 添加必要的业务逻辑

- **生成失败时**：
  - 检查输入参数的格式和有效性
  - 简化字段定义重新尝试
  - 分层生成，逐步完善代码
</outcome>
</manual>

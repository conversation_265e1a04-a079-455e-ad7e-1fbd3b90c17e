/**
 * CRUD代码生成器 - 智能生成Python后端四层架构代码
 * 专门解决DTO转换和Repository查询的重复性工作
 */

// 私有化常量
const _SUPPORTED_LAYERS = ['dto', 'repository', 'service', 'endpoint', 'helper'];
const _PYTHON_TYPES = ['str', 'int', 'float', 'bool', 'date', 'datetime', 'EmailStr', 'list', 'dict'];
const _DEFAULT_BUSINESS_RULES = {
  has_audit_fields: true,
  has_soft_delete: false,
  belongs_to_broker: false,
  has_reference_code: false
};

class CRUDGenerator {
  constructor() {
    this.entityConfig = {
      name: '',
      description: '',
      fields: [],
      layers: [],
      businessRules: { ..._DEFAULT_BUSINESS_RULES }
    };
  }

  // 交互式配置向导
  async startInteractiveWizard() {
    console.log('\n🎯 CRUD代码生成器 - 交互式配置向导');
    console.log('=====================================');
    console.log('由于沙箱环境限制，请按照以下步骤手动配置：\n');

    console.log('📝 第1步：实体基本信息');
    console.log('- 实体名称（PascalCase格式，如：Customer）');
    console.log('- 实体描述（如：客户信息管理）\n');

    console.log('🔧 第2步：字段定义');
    console.log('支持的字段类型：', _PYTHON_TYPES.join(', '));
    console.log('字段格式示例：');
    console.log('  { "name": "name", "type": "str", "required": true, "description": "客户姓名" }');
    console.log('  { "name": "email", "type": "EmailStr", "required": true, "description": "邮箱地址" }\n');

    console.log('🏗️ 第3步：选择生成层级');
    console.log('可选层级：', _SUPPORTED_LAYERS.join(', '));
    console.log('推荐组合：["dto", "repository"] 或 ["dto", "repository", "service"]\n');

    console.log('⚙️ 第4步：业务规则配置');
    console.log('- has_audit_fields: 是否包含审计字段（created_at, updated_at）');
    console.log('- has_soft_delete: 是否支持软删除（is_deleted）');
    console.log('- belongs_to_broker: 是否属于Broker');
    console.log('- has_reference_code: 是否需要引用码（ref_code）\n');

    console.log('💡 使用示例：');
    console.log('请使用以下参数重新调用工具：');
    const exampleConfig = {
      mode: "configured",
      entity_name: "Customer",
      description: "客户信息管理",
      fields: [
        { name: "name", type: "str", required: true, description: "客户姓名" },
        { name: "email", type: "EmailStr", required: true, description: "邮箱地址" },
        { name: "phone", type: "str", required: false, description: "联系电话" }
      ],
      layers: ["dto", "repository"],
      business_rules: {
        has_audit_fields: true,
        has_soft_delete: false,
        belongs_to_broker: true,
        has_reference_code: false
      }
    };

    console.log(JSON.stringify(exampleConfig, null, 2));

    return {
      success: true,
      data: {
        message: "交互式配置向导已显示，请按照提示重新调用工具",
        example_config: exampleConfig
      }
    };
  }

  // 验证实体名称
  validateEntityName(name) {
    const pascalCaseRegex = /^[A-Z][a-zA-Z0-9]*$/;
    return pascalCaseRegex.test(name);
  }

  // 验证字段类型
  validateFieldType(type) {
    return _PYTHON_TYPES.includes(type) || type.includes('|') || type.includes('None');
  }

  // 配置模式：直接使用参数配置
  configureFromParams(params) {
    // 验证实体名称
    if (!this.validateEntityName(params.entity_name)) {
      throw new Error('实体名称格式不正确，必须是PascalCase格式（如：Customer）');
    }

    // 验证字段定义
    if (!Array.isArray(params.fields) || params.fields.length === 0) {
      throw new Error('字段定义不能为空，必须是包含字段对象的数组');
    }

    // 验证每个字段
    params.fields.forEach((field, index) => {
      if (!field.name || !field.type) {
        throw new Error(`字段${index + 1}缺少必需的name或type属性`);
      }
      if (!this.validateFieldType(field.type)) {
        throw new Error(`字段${field.name}的类型${field.type}不支持`);
      }
    });

    // 验证层级选择
    if (!Array.isArray(params.layers)) {
      throw new Error('layers必须是数组格式');
    }
    const invalidLayers = params.layers.filter(layer => !_SUPPORTED_LAYERS.includes(layer));
    if (invalidLayers.length > 0) {
      throw new Error(`不支持的层级：${invalidLayers.join(', ')}`);
    }

    // 配置实体信息
    this.entityConfig.name = params.entity_name;
    this.entityConfig.description = params.description || params.entity_name;
    this.entityConfig.fields = params.fields;
    this.entityConfig.layers = params.layers;
    this.entityConfig.businessRules = { ..._DEFAULT_BUSINESS_RULES, ...params.business_rules };

    console.log(`✅ 实体配置完成：${this.entityConfig.name}`);
    console.log(`📋 字段数量：${this.entityConfig.fields.length}`);
    console.log(`🏗️ 生成层级：${this.entityConfig.layers.join(', ')}`);
  }



  // 生成DTO代码 - 严格遵循项目规范
  generateDTOCode() {
    const { name, fields, businessRules } = this.entityConfig;
    const className = name;
    const modelName = `${name}Model`;

    // 导入语句 - 遵循项目导入顺序：标准库->第三方库->项目内部模块
    let code = `from pydantic import BaseModel\n`;
    if (fields.some(f => f.type === 'EmailStr')) {
      code += `from pydantic import EmailStr\n`;
    }
    code += `\n`;

    // 项目内部导入
    code += `from bethune.api.dto.base import AuditMixin\n`;
    if (businessRules.has_audit_fields) {
      code += `from bethune.api.dto.base import ExtendedAuditMixin\n`;
    }
    code += `from bethune.model.${name.toLowerCase()} import ${name} as ${modelName}\n\n\n`;

    // Base DTO - 遵循项目命名规范
    code += `class ${className}Base(BaseModel):\n`;
    fields.forEach(field => {
      const optional = field.required ? '' : ' | None = None';
      code += `    ${field.name}: ${field.type}${optional}\n`;
    });
    code += '\n\n';

    // Create DTO - 遵循项目模式
    code += `class ${className}Create(${className}Base):\n`;
    code += `    def to_model(self) -> ${modelName}:\n`;
    code += `        return ${modelName}(\n`;
    code += `            **self.model_dump(exclude_unset=True)\n`;
    code += `        )\n\n\n`;

    // Response DTO - 遵循项目继承模式
    const mixinClass = businessRules.has_audit_fields ? 'ExtendedAuditMixin' : 'AuditMixin';
    code += `class ${className}(${className}Base, ${mixinClass}):\n`;
    code += `    id: int\n\n`;
    code += `    @classmethod\n`;
    code += `    def from_model(cls, model: ${modelName}) -> "${className}":\n`;
    code += `        return cls(**model.model_dump())\n\n`;

    // List DTO - 遵循项目模式
    code += `class ${className}List(${className}Base, ${mixinClass}):\n`;
    code += `    id: int\n\n`;
    code += `    @classmethod\n`;
    code += `    def from_model(cls, model: ${modelName}):\n`;
    code += `        return cls(**model.model_dump())\n\n`;
    code += `    @classmethod\n`;
    code += `    def from_models(cls, models: list[${modelName}]):\n`;
    code += `        return [cls.from_model(model) for model in models]\n`;

    return code;
  }

  // 生成Repository代码 - 严格遵循项目规范
  generateRepositoryCode() {
    const { name, businessRules } = this.entityConfig;
    const className = `${name}Repository`;
    const modelName = `${name}Model`;

    // 导入语句 - 遵循项目导入顺序和异常处理模式
    let code = `from sqlalchemy.exc import NoResultFound\n`;
    code += `from sqlmodel import select\n\n`;
    code += `from bethune.db.session_context import session\n`;
    code += `from bethune.error.errors import NotFoundError\n`;
    code += `from bethune.model.${name.toLowerCase()} import ${name} as ${modelName}\n`;
    code += `from bethune.repository.base import BaseRepository\n\n\n`;

    // Repository类定义 - 遵循项目命名和继承模式
    code += `class ${className}(BaseRepository[${modelName}]):\n\n`;
    code += `    def __init__(self):\n`;
    code += `        super().__init__(model_class=${modelName})\n\n`;

    // 生成常用查询方法 - 遵循项目get_by_xxx命名规范
    if (businessRules.belongs_to_broker) {
      code += `    def get_by_broker_id(self, broker_id: int) -> list[${modelName}]:\n`;
      code += `        statement = select(${modelName}).where(${modelName}.broker_id == broker_id)\n`;
      if (businessRules.has_soft_delete) {
        code += `        statement = statement.where(${modelName}.is_deleted.is_(False))\n`;
      }
      code += `        return session().exec(statement).all()\n\n`;
    }

    // 如果有软删除，生成标记删除方法 - 复用项目现有模式
    if (businessRules.has_soft_delete) {
      code += `    def mark_as_deleted(self, id: int):\n`;
      code += `        existed = self.get_by_id(id)\n`;
      code += `        existed.is_deleted = True\n`;
      code += `        existed.deleted_at = get_current_datetime()\n`;
      code += `        return self.update(existed)\n\n`;

      // 需要导入时间工具
      code = code.replace(
        'from bethune.repository.base import BaseRepository\n',
        'from bethune.repository.base import BaseRepository\nfrom bethune.util import get_current_datetime\n'
      );
    }

    return code;
  }

  // 生成Service代码 - 遵循项目Service模式
  generateServiceCode() {
    const { name } = this.entityConfig;
    const className = `${name}Service`;
    const modelName = `${name}Model`;
    const repoName = `${name}Repository`;

    let code = `from bethune.model.${name.toLowerCase()} import ${name} as ${modelName}\n`;
    code += `from bethune.repository.${name.toLowerCase()} import ${repoName}\n`;
    code += `from bethune.service.base import BaseService\n\n\n`;

    code += `class ${className}(BaseService[${modelName}, ${repoName}]):\n\n`;
    code += `    def __init__(self):\n`;
    code += `        super().__init__(${repoName}())\n`;

    return code;
  }

  // 生成Endpoint代码 - 遵循项目API模式
  generateEndpointCode() {
    const { name, businessRules } = this.entityConfig;
    const dtoName = name;
    const createDtoName = `${name}Create`;
    const listDtoName = `${name}List`;
    const serviceName = `${name}Service`;

    let code = `from typing import Annotated\n\n`;
    code += `from fastapi import APIRouter\n`;
    code += `from fastapi import Depends\n`;
    code += `from fastapi import Path\n`;
    code += `from fastapi import Security\n\n`;
    code += `from bethune.api.dto.base import BaseResponse\n`;
    code += `from bethune.api.dto.base import PageParams\n`;
    code += `from bethune.api.dto.base import Pagination\n`;
    code += `from bethune.api.dto.${name.toLowerCase()} import ${createDtoName}\n`;
    code += `from bethune.api.dto.${name.toLowerCase()} import ${dtoName}\n`;
    code += `from bethune.api.dto.${name.toLowerCase()} import ${listDtoName}\n`;
    code += `from bethune.service.context import InsuranceServiceContext\n\n\n`;

    code += `api_router = APIRouter()\n\n\n`;

    // Create endpoint
    code += `@api_router.post(\n`;
    code += `    "",\n`;
    code += `    summary="create ${name.toLowerCase()}",\n`;
    code += `    response_model=BaseResponse[${dtoName}],\n`;
    code += `)\n`;
    code += `async def create_${name.toLowerCase()}(\n`;
    code += `    ${name.toLowerCase()}_create: ${createDtoName},\n`;
    code += `    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],\n`;
    code += `) -> BaseResponse[${dtoName}]:\n`;
    code += `    ${name.toLowerCase()} = sc.${name.toLowerCase()}_service.create(${name.toLowerCase()}_create.to_model())\n`;
    code += `    return BaseResponse.ok(${dtoName}.from_model(${name.toLowerCase()}))\n\n\n`;

    // Get by ID endpoint
    code += `@api_router.get(\n`;
    code += `    "/{${name.toLowerCase()}_id}",\n`;
    code += `    summary="get ${name.toLowerCase()} by id",\n`;
    code += `    response_model=BaseResponse[${dtoName}],\n`;
    code += `)\n`;
    code += `async def get_${name.toLowerCase()}(\n`;
    code += `    ${name.toLowerCase()}_id: int = Path(..., description="${name} ID"),\n`;
    code += `    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],\n`;
    code += `) -> BaseResponse[${dtoName}]:\n`;
    code += `    ${name.toLowerCase()} = sc.${name.toLowerCase()}_service.get_by_id(${name.toLowerCase()}_id)\n`;
    code += `    return BaseResponse.ok(${dtoName}.from_model(${name.toLowerCase()}))\n`;

    return code;
  }

  // 生成所有代码
  async generateCode() {
    const generatedCode = {};
    const filePaths = {};

    this.entityConfig.layers.forEach(layer => {
      switch (layer) {
        case 'dto':
          generatedCode.dto = this.generateDTOCode();
          filePaths.dto = `bethune/api/dto/${this.entityConfig.name.toLowerCase()}.py`;
          break;
        case 'repository':
          generatedCode.repository = this.generateRepositoryCode();
          filePaths.repository = `bethune/repository/insurance/${this.entityConfig.name.toLowerCase()}.py`;
          break;
        case 'service':
          generatedCode.service = this.generateServiceCode();
          filePaths.service = `bethune/service/insurance/${this.entityConfig.name.toLowerCase()}.py`;
          break;
        case 'endpoint':
          generatedCode.endpoint = this.generateEndpointCode();
          filePaths.endpoint = `bethune/api/endpoint/insurance/${this.entityConfig.name.toLowerCase()}.py`;
          break;
      }
    });

    return { generatedCode, filePaths };
  }

  // 主执行流程
  async execute(params) {
    try {
      // 根据模式选择执行方式
      if (params.mode === 'interactive') {
        return await this.startInteractiveWizard();
      } else if (params.mode === 'configured') {
        // 配置模式：直接使用参数生成代码
        this.configureFromParams(params);

        console.log('\n🚀 开始生成代码...');
        const { generatedCode, filePaths } = await this.generateCode();
        console.log('✅ 代码生成完成！');

        return {
          success: true,
          data: {
            entity_name: this.entityConfig.name,
            generated_layers: this.entityConfig.layers,
            code: generatedCode,
            file_paths: filePaths,
            metadata: {
              total_lines: Object.values(generatedCode).join('\n').split('\n').length,
              generation_time: new Date().toISOString(),
              code_rules_applied: 10
            }
          }
        };
      } else {
        throw new Error('不支持的模式，请使用 "interactive" 或 "configured"');
      }

    } catch (error) {
      return {
        success: false,
        error: {
          code: 'GENERATION_ERROR',
          message: error.message,
          details: error.stack
        }
      };
    }
  }
}

module.exports = {
  getDependencies() {
    return []; // 使用Node.js内置模块
  },

  getMetadata() {
    return {
      name: 'crud-generator',
      description: '智能CRUD代码生成器，通过交互式问答生成Python后端四层架构代码',
      version: '1.0.0',
      category: 'code-generation',
      author: '鲁班',
      tags: ['crud', 'generator', 'python', 'backend'],
      manual: '@manual://crud-generator'
    };
  },

  getSchema() {
    return {
      type: 'object',
      properties: {
        mode: {
          type: 'string',
          enum: ['interactive', 'configured'],
          default: 'interactive',
          description: '工具运行模式：interactive为交互式向导，configured为直接配置'
        },
        entity_name: {
          type: 'string',
          description: '实体名称（PascalCase格式）'
        },
        description: {
          type: 'string',
          description: '实体描述'
        },
        fields: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string', description: '字段名称' },
              type: { type: 'string', description: '字段类型' },
              required: { type: 'boolean', description: '是否必需' },
              description: { type: 'string', description: '字段描述' }
            },
            required: ['name', 'type']
          },
          description: '字段定义数组'
        },
        layers: {
          type: 'array',
          items: {
            type: 'string',
            enum: _SUPPORTED_LAYERS
          },
          description: '要生成的代码层级'
        },
        business_rules: {
          type: 'object',
          properties: {
            has_audit_fields: { type: 'boolean', description: '是否包含审计字段' },
            has_soft_delete: { type: 'boolean', description: '是否支持软删除' },
            belongs_to_broker: { type: 'boolean', description: '是否属于Broker' },
            has_reference_code: { type: 'boolean', description: '是否需要引用码' }
          },
          description: '业务规则配置'
        }
      },
      required: ['mode']
    };
  },

  validate(params) {
    if (!params.mode || !['interactive', 'configured'].includes(params.mode)) {
      return {
        valid: false,
        errors: ['mode参数必须是"interactive"或"configured"']
      };
    }

    if (params.mode === 'configured') {
      if (!params.entity_name) {
        return {
          valid: false,
          errors: ['configured模式下entity_name参数是必需的']
        };
      }
      if (!params.fields || !Array.isArray(params.fields) || params.fields.length === 0) {
        return {
          valid: false,
          errors: ['configured模式下fields参数是必需的，且不能为空']
        };
      }
    }

    return { valid: true, errors: [] };
  },

  async execute(params) {
    const generator = new CRUDGenerator();
    return await generator.execute(params);
  }
};

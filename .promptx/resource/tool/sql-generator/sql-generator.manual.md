<manual>
<identity>
## 工具名称
@tool://sql-generator

## 简介
智能MySQL SQL脚本生成器，根据自然语言需求生成符合项目业务逻辑的SQL脚本，支持复杂的多表关联操作
</identity>

<purpose>
⚠️ **AI重要提醒**: 调用此工具前必须完整阅读本说明书，理解工具功能边界、参数要求和使用限制。禁止在不了解工具功能的情况下盲目调用。

## 核心问题定义
解决MySQL数据库脚本编写的重复性工作，特别是：
- 根据自然语言需求自动生成SQL脚本
- 理解项目业务逻辑生成符合实际的数据
- 处理复杂的多表关联操作（用户-角色-权限等）
- 确保生成的数据值真实可信，字段精简必要

## 价值主张
- 🎯 **解决什么痛点**：消除手写SQL脚本的重复劳动，避免业务逻辑错误
- 🚀 **带来什么价值**：节省90%的SQL编写时间，确保数据一致性和业务合理性
- 🌟 **独特优势**：深度理解项目业务逻辑，生成真实可用的数据脚本

## 应用边界
- ✅ **适用场景**：
  - 用户管理相关的SQL脚本生成
  - 角色权限体系的数据初始化
  - 业务数据的批量插入脚本
  - 测试数据的快速生成
  - 数据库初始化和迁移脚本
- ❌ **不适用场景**：
  - 复杂的数据分析查询
  - 性能优化相关的SQL
  - 数据库结构变更（DDL）
  - 跨数据库平台的SQL转换
</purpose>

<usage>
## 使用时机
- **新功能开发**：需要为新功能准备测试数据
- **数据初始化**：系统部署时需要初始化基础数据
- **测试场景准备**：为测试用例准备特定的数据场景
- **演示数据生成**：为产品演示准备真实的示例数据

## 操作步骤
1. **需求描述阶段**：
   - 用自然语言清晰描述需要实现的功能
   - 明确涉及的业务实体和关系
   - 指定特殊的数据要求或约束

2. **执行阶段**：
   - 工具分析需求并识别涉及的数据库表
   - 根据项目业务逻辑生成合理的数据值
   - 生成完整的SQL脚本包含所有必要操作

3. **验证阶段**：
   - 检查生成的SQL语法正确性
   - 验证数据的业务逻辑合理性
   - 确认脚本的执行顺序和依赖关系

## 最佳实践
- 🎯 **效率提升**：
  - 描述需求时尽量具体明确
  - 一次性描述完整的业务场景
  - 利用工具的业务逻辑理解能力

- ⚠️ **避免陷阱**：
  - 不要在生产环境直接执行生成的脚本
  - 注意检查生成的ID是否与现有数据冲突
  - 复杂业务逻辑需要人工验证

- 🔧 **故障排除**：
  - SQL语法错误：检查表名和字段名是否正确
  - 数据冲突：调整生成的ID值或唯一字段
  - 业务逻辑错误：补充更详细的需求描述

## 注意事项
- 生成的脚本需要在测试环境验证后再用于生产
- 工具基于当前项目的数据库结构，不适用于其他项目
- 复杂的业务规则可能需要手动调整生成的脚本
- 建议在执行前备份相关数据
</usage>

<parameter>
## 必需参数
| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| requirement | string | 自然语言需求描述 | "创建一个系统管理员用户，给他分配管理员角色和所有权限" |

## 可选参数
| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| scenario | string | "production" | 使用场景：production/testing/demo |
| include_comments | boolean | true | 是否在SQL中包含注释说明 |
| data_style | string | "realistic" | 数据风格：realistic/simple/demo |
| output_format | string | "mysql" | 输出格式：mysql/postgresql |

## 参数约束
- **requirement**: 不能为空，长度不超过1000字符
- **scenario**: 必须是预定义的场景类型之一
- **data_style**: 影响生成数据的真实程度和复杂度
- **output_format**: 当前版本仅支持mysql格式

## 参数示例
```json
{
  "requirement": "创建一个经纪人用户张三，邮箱zhang<EMAIL>，给他分配经纪人角色，并添加客户管理和线索查看权限",
  "scenario": "testing",
  "include_comments": true,
  "data_style": "realistic",
  "output_format": "mysql"
}
```
</parameter>

<outcome>
## 成功返回格式
```json
{
  "success": true,
  "data": {
    "sql_script": "-- 完整的SQL脚本内容\nINSERT INTO user ...",
    "operations": [
      {
        "table": "user",
        "operation": "INSERT",
        "description": "创建用户张三"
      },
      {
        "table": "user_role", 
        "operation": "INSERT",
        "description": "分配经纪人角色"
      }
    ],
    "affected_tables": ["user", "role", "permission", "user_role", "role_permission"],
    "metadata": {
      "total_statements": 5,
      "estimated_execution_time": "< 1s",
      "data_consistency_check": "passed"
    }
  }
}
```

## 错误处理格式
```json
{
  "success": false,
  "error": {
    "code": "REQUIREMENT_PARSE_ERROR",
    "message": "无法理解需求描述",
    "details": "请提供更具体的需求描述，包含明确的业务实体和操作"
  }
}
```

## 结果解读指南
- **如何使用生成的脚本**：
  - 复制sql_script内容到MySQL客户端执行
  - 按照operations列表了解每个操作的目的
  - 检查affected_tables确认影响范围

- **脚本执行步骤**：
  1. 在测试环境中执行脚本
  2. 验证数据的正确性和完整性
  3. 检查业务逻辑是否符合预期
  4. 确认无误后在目标环境执行

- **质量验证**：
  - 检查SQL语法是否正确
  - 验证生成的数据是否符合业务规则
  - 确认表关联关系是否正确
  - 检查数据一致性

## 后续动作建议
- **成功生成后**：
  - 在测试环境验证脚本执行
  - 检查生成数据的业务合理性
  - 根据实际需要调整数据值
  - 记录脚本用途便于后续维护

- **生成失败时**：
  - 检查需求描述的清晰度
  - 补充必要的业务上下文
  - 简化复杂需求分步处理
  - 参考示例重新组织需求描述
</outcome>
</manual>

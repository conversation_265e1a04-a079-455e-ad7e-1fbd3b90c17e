<manual>
<identity>
## 工具名称
@tool://test-generator

## 简介
智能pytest测试代码生成器，严格遵循项目测试规范，自动生成包含权限认证的API测试用例
</identity>

<purpose>
⚠️ **AI重要提醒**: 调用此工具前必须完整阅读本说明书，理解工具功能边界、参数要求和使用限制。禁止在不了解工具功能的情况下盲目调用。

## 核心问题定义
解决Python后端API接口测试代码的重复性编写问题，特别是：
- pytest测试用例的标准化生成
- 5种角色权限认证的自动化处理
- 项目测试规范的严格遵循
- 测试方法命名的一致性保证

## 价值主张
- 🎯 **解决什么痛点**：消除API测试代码的重复编写，确保测试规范一致性
- 🚀 **带来什么价值**：节省80%测试代码编写时间，保证测试质量标准化
- 🌟 **独特优势**：深度学习项目测试模式，自动处理权限认证，最小化断言

## 应用边界
- ✅ **适用场景**：
  - API接口的pytest测试用例生成
  - 需要权限认证的接口测试
  - 标准CRUD操作的测试覆盖
  - 项目测试规范的标准化执行
- ❌ **不适用场景**：
  - 复杂业务逻辑的单元测试
  - 非API接口的功能测试
  - 需要复杂数据准备的集成测试
  - 性能测试和压力测试
</purpose>

<usage>
## 使用时机
- **新API接口开发完成**：需要快速生成对应的测试用例
- **测试覆盖率提升**：为现有接口补充标准化测试
- **测试规范统一**：将不规范的测试代码标准化
- **权限测试完善**：为接口添加多角色权限验证测试

## 操作步骤
1. **准备阶段**：
   - 确定要测试的API接口信息（路径、方法、参数）
   - 明确需要测试的用户角色类型
   - 准备接口的预期响应格式

2. **执行阶段**：
   - 提供接口基本信息和测试需求
   - 选择需要测试的用户角色
   - 配置测试用例的验证重点

3. **验证阶段**：
   - 检查生成的测试代码是否符合项目规范
   - 验证权限认证配置的正确性
   - 确认测试方法命名的合理性

## 最佳实践
- 🎯 **效率提升**：
  - 批量生成多个接口的测试用例
  - 使用标准的测试数据模板
  - 复用项目现有的fixture和工具函数

- ⚠️ **避免陷阱**：
  - 不要为简单接口添加过多断言
  - 避免在测试代码中添加业务逻辑
  - 不要忽略权限边界的测试覆盖

- 🔧 **故障排除**：
  - 权限认证失败：检查角色邮箱配置
  - 测试执行失败：验证API路径和参数格式
  - 断言错误：确认预期响应码的正确性

## 注意事项
- 生成的测试代码需要手动集成到项目中
- 复杂的业务逻辑验证需要手动添加
- 建议先运行生成的测试确保基础功能正常
- 遵循项目的测试文件组织结构
</usage>

<parameter>
## 必需参数
| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| api_info | object | API接口信息 | {"method": "POST", "path": "/customer", "description": "创建客户"} |
| test_scenarios | array | 测试场景列表 | [{"name": "create_success", "role": "admin", "expected_code": 1000}] |

## 可选参数
| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| module_name | string | 从path推导 | 测试模块名称 |
| include_fixtures | boolean | true | 是否包含fixture定义 |
| test_data | object | {} | 测试数据模板 |

## 参数约束
- **api_info.method**: 必须是有效的HTTP方法（GET, POST, PUT, DELETE等）
- **api_info.path**: 必须是有效的API路径格式
- **test_scenarios.role**: 必须是5种预定义角色之一（admin, broker, referral, staff, company_admin）
- **test_scenarios.expected_code**: 必须是有效的响应状态码

## 参数示例
```json
{
  "api_info": {
    "method": "POST",
    "path": "/api/v1/insurance/customer",
    "description": "创建客户信息"
  },
  "test_scenarios": [
    {
      "name": "create_success",
      "role": "admin",
      "expected_code": 1000,
      "test_data": {
        "name": "Test Customer",
        "email": "<EMAIL>"
      }
    },
    {
      "name": "create_unauthorized",
      "role": "referral",
      "expected_code": 4003
    }
  ],
  "module_name": "customer",
  "include_fixtures": true
}
```
</parameter>

<outcome>
## 成功返回格式
```json
{
  "success": true,
  "data": {
    "test_file_name": "customer_test.py",
    "test_code": "# 生成的完整测试代码...",
    "test_methods": [
      "test_create_success",
      "test_create_unauthorized"
    ],
    "fixtures_used": [
      "test_client",
      "api_prefix", 
      "auth_headers_generator"
    ],
    "metadata": {
      "total_lines": 45,
      "test_count": 2,
      "roles_covered": ["admin", "referral"],
      "generation_time": "2024-01-01T12:00:00Z"
    }
  }
}
```

## 错误处理格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "API信息验证失败",
    "details": "method字段必须是有效的HTTP方法"
  }
}
```

## 结果解读指南
- **如何使用生成的代码**：
  - 将test_code内容保存为对应的测试文件
  - 根据test_file_name确定文件位置
  - 检查fixtures_used确保项目中存在对应的fixture

- **代码集成步骤**：
  1. 在tests目录下创建对应的测试文件
  2. 将生成的代码复制到文件中
  3. 检查并调整import语句
  4. 运行pytest验证测试是否正常

- **质量验证**：
  - 确认所有测试方法命名符合规范
  - 验证权限认证配置的正确性
  - 检查断言逻辑的最小化原则

## 后续动作建议
- **成功生成后**：
  - 将测试代码集成到项目中
  - 运行pytest验证基础功能
  - 根据具体业务需求调整测试数据
  - 添加必要的业务逻辑验证

- **生成失败时**：
  - 检查API信息的格式和有效性
  - 验证测试场景配置的完整性
  - 简化测试需求重新尝试
  - 分步生成，逐步完善测试用例
</outcome>
</manual>

/**
 * 测试代码生成器 - 智能生成pytest测试代码
 * 严格遵循项目测试规范，自动处理权限认证
 */

// 私有化常量
const _ROLE_EMAILS = {
  admin: "<EMAIL>",           // 系统管理员
  broker: "<EMAIL>",     // 代理人
  referral: "<EMAIL>",                // 泛代理人
  staff: "<EMAIL>",           // 内勤（暂用系统管理员）
  company_admin: "<EMAIL>"     // 经纪行管理员
};

const _HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
const _VALID_ROLES = Object.keys(_ROLE_EMAILS);

class TestGenerator {
  constructor() {
    this.testConfig = {
      api_info: {},
      test_scenarios: [],
      module_name: '',
      include_fixtures: true,
      test_data: {}
    };
  }

  // 验证API信息
  validateApiInfo(api_info) {
    if (!api_info.method || !_HTTP_METHODS.includes(api_info.method.toUpperCase())) {
      throw new Error(`HTTP方法无效，必须是: ${_HTTP_METHODS.join(', ')}`);
    }

    if (!api_info.path || typeof api_info.path !== 'string') {
      throw new Error('API路径不能为空且必须是字符串');
    }

    if (!api_info.description) {
      throw new Error('API描述不能为空');
    }
  }

  // 验证测试场景
  validateTestScenarios(scenarios) {
    if (!Array.isArray(scenarios) || scenarios.length === 0) {
      throw new Error('测试场景必须是非空数组');
    }

    scenarios.forEach((scenario, index) => {
      if (!scenario.name) {
        throw new Error(`测试场景${index + 1}缺少name字段`);
      }

      if (!scenario.role || !_VALID_ROLES.includes(scenario.role)) {
        throw new Error(`测试场景${index + 1}的role无效，必须是: ${_VALID_ROLES.join(', ')}`);
      }

      if (typeof scenario.expected_code !== 'number') {
        throw new Error(`测试场景${index + 1}的expected_code必须是数字`);
      }
    });
  }

  // 配置测试参数
  configureFromParams(params) {
    // 验证必需参数
    this.validateApiInfo(params.api_info);
    this.validateTestScenarios(params.test_scenarios);

    // 配置测试信息
    this.testConfig.api_info = params.api_info;
    this.testConfig.test_scenarios = params.test_scenarios;
    this.testConfig.module_name = params.module_name || this.extractModuleName(params.api_info.path);
    this.testConfig.include_fixtures = params.include_fixtures !== false;
    this.testConfig.test_data = params.test_data || {};

    console.log(`✅ 测试配置完成：${this.testConfig.module_name}`);
    console.log(`📋 测试场景数量：${this.testConfig.test_scenarios.length}`);
    console.log(`🎭 涉及角色：${[...new Set(this.testConfig.test_scenarios.map(s => s.role))].join(', ')}`);
  }

  // 从API路径提取模块名
  extractModuleName(path) {
    const parts = path.split('/').filter(p => p && p !== 'api' && p !== 'v1');
    return parts[parts.length - 1] || 'api';
  }

  // 生成导入语句
  generateImports() {
    let imports = `import pytest\nfrom fastapi.testclient import TestClient\n\n`;

    // 根据API信息添加特定导入
    const { api_info } = this.testConfig;
    if (api_info.path.includes('insurance')) {
      imports += `from bethune.api.dto.base import InsuranceType\n`;
    }

    return imports;
  }

  // 生成fixture定义
  generateFixtures() {
    if (!this.testConfig.include_fixtures) return '';

    const { module_name } = this.testConfig;

    return `@pytest.fixture
def ${module_name}_api_prefix(api_prefix):
    return f"{api_prefix}${this.testConfig.api_info.path.replace('/api/v1', '')}"


`;
  }

  // 分析是否可以参数化测试
  analyzeParametrization() {
    const { test_scenarios, api_info } = this.testConfig;

    // 按HTTP方法和基本结构分组
    const groups = {};
    test_scenarios.forEach(scenario => {
      const key = `${api_info.method}_${scenario.expected_code >= 1000 ? 'success' : 'error'}`;
      if (!groups[key]) groups[key] = [];
      groups[key].push(scenario);
    });

    // 找出可以参数化的组（2个或以上场景）
    const parametrizable = {};
    const individual = [];

    Object.entries(groups).forEach(([key, scenarios]) => {
      if (scenarios.length >= 2) {
        parametrizable[key] = scenarios;
      } else {
        individual.push(...scenarios);
      }
    });

    return { parametrizable, individual };
  }

  // 提取重复的辅助方法
  extractHelperMethods() {
    const { api_info } = this.testConfig;
    const http_method = api_info.method.toLowerCase();
    let helpers = '';

    // 生成通用的API调用方法
    helpers += `def _make_${http_method}_request(test_client, api_prefix, headers, data=None):\n`;
    if (http_method === 'get') {
      helpers += `    return test_client.get(api_prefix, headers=headers)\n\n`;
    } else if (http_method === 'post') {
      helpers += `    return test_client.post(api_prefix, json=data or {}, headers=headers)\n\n`;
    } else {
      helpers += `    return test_client.${http_method}(api_prefix, json=data or {}, headers=headers)\n\n`;
    }

    // 生成通用的断言方法
    helpers += `def _assert_response(response, expected_code, verify_data=None):\n`;
    helpers += `    assert response.status_code == 200\n`;
    helpers += `    assert response.json()["code"] == expected_code\n`;
    helpers += `    if verify_data and expected_code == 1000:\n`;
    helpers += `        for key, value in verify_data.items():\n`;
    helpers += `            assert response.json()["data"][key] == value\n\n`;

    return helpers;
  }

  // 生成参数化测试方法
  generateParametrizedTest(group_key, scenarios) {
    const { api_info } = this.testConfig;
    const http_method = api_info.method.toLowerCase();
    const test_name = `test_${group_key.toLowerCase()}`;

    // 构建参数化数据
    const params = scenarios.map(scenario => {
      const role_email = _ROLE_EMAILS[scenario.role];
      const test_data = scenario.test_data || {};
      const verify_data = scenario.verify_data || {};

      return `("${scenario.role}", "${role_email}", ${JSON.stringify(test_data)}, ${scenario.expected_code}, ${JSON.stringify(verify_data)})`;
    });

    let code = `@pytest.mark.parametrize(\n`;
    code += `    "role,email,test_data,expected_code,verify_data",\n`;
    code += `    [\n`;
    params.forEach(param => {
      code += `        ${param},\n`;
    });
    code += `    ],\n`;
    code += `)\n`;
    code += `def ${test_name}(test_client: TestClient, ${this.testConfig.module_name}_api_prefix, auth_headers_generator, role, email, test_data, expected_code, verify_data):\n`;
    code += `    headers = auth_headers_generator(email)\n`;
    code += `    response = _make_${http_method}_request(test_client, ${this.testConfig.module_name}_api_prefix, headers, test_data)\n`;
    code += `    _assert_response(response, expected_code, verify_data)\n\n`;

    return code;
  }

  // 生成单独的测试方法（使用辅助方法）
  generateIndividualTest(scenario) {
    const { api_info } = this.testConfig;
    const method_name = `test_${scenario.name}`;
    const role_email = _ROLE_EMAILS[scenario.role];
    const http_method = api_info.method.toLowerCase();
    const test_data = scenario.test_data || {};
    const verify_data = scenario.verify_data || {};

    let code = `def ${method_name}(test_client: TestClient, ${this.testConfig.module_name}_api_prefix, auth_headers_generator):\n`;
    code += `    headers = auth_headers_generator("${role_email}")\n`;
    code += `    response = _make_${http_method}_request(test_client, ${this.testConfig.module_name}_api_prefix, headers, ${JSON.stringify(test_data)})\n`;
    code += `    _assert_response(response, ${scenario.expected_code}, ${JSON.stringify(verify_data)})\n\n`;

    return code;
  }

  // 生成完整测试代码
  generateTestCode() {
    let code = this.generateImports();
    code += this.generateFixtures();

    // 分析参数化可能性
    const { parametrizable, individual } = this.analyzeParametrization();

    // 如果有重复逻辑，生成辅助方法
    if (Object.keys(parametrizable).length > 0 || individual.length > 0) {
      code += this.extractHelperMethods();
    }

    // 生成参数化测试
    Object.entries(parametrizable).forEach(([group_key, scenarios]) => {
      code += this.generateParametrizedTest(group_key, scenarios);
    });

    // 生成单独的测试方法
    individual.forEach(scenario => {
      code += this.generateIndividualTest(scenario);
    });

    return code.trim();
  }

  // 生成测试文件名
  generateTestFileName() {
    return `${this.testConfig.module_name}_test.py`;
  }

  // 主执行流程
  async execute(params) {
    try {
      // 配置测试参数
      this.configureFromParams(params);

      console.log('\n🚀 开始生成测试代码...');

      // 分析参数化情况
      const { parametrizable, individual } = this.analyzeParametrization();

      // 生成测试代码
      const test_code = this.generateTestCode();
      const test_file_name = this.generateTestFileName();

      // 提取测试方法名
      const parametrized_methods = Object.keys(parametrizable).map(key => `test_${key.toLowerCase()}`);
      const individual_methods = individual.map(s => `test_${s.name}`);
      const test_methods = [...parametrized_methods, ...individual_methods];

      // 统计信息
      const roles_covered = [...new Set(this.testConfig.test_scenarios.map(s => s.role))];
      const total_lines = test_code.split('\n').length;
      const has_parametrized = Object.keys(parametrizable).length > 0;
      const has_helpers = has_parametrized || individual.length > 0;

      console.log('✅ 测试代码生成完成！');

      return {
        success: true,
        data: {
          test_file_name,
          test_code,
          test_methods,
          fixtures_used: [
            'test_client',
            `${this.testConfig.module_name}_api_prefix`,
            'auth_headers_generator'
          ],
          metadata: {
            total_lines,
            test_count: this.testConfig.test_scenarios.length,
            roles_covered,
            has_parametrized_tests: has_parametrized,
            has_helper_methods: has_helpers,
            parametrized_groups: Object.keys(parametrizable).length,
            individual_tests: individual.length,
            generation_time: new Date().toISOString()
          }
        }
      };

    } catch (error) {
      return {
        success: false,
        error: {
          code: 'GENERATION_ERROR',
          message: error.message,
          details: error.stack
        }
      };
    }
  }
}

module.exports = {
  getDependencies() {
    return []; // 使用Node.js内置模块
  },

  getMetadata() {
    return {
      name: 'test-generator',
      description: '智能pytest测试代码生成器，严格遵循项目测试规范，自动处理权限认证',
      version: '1.0.0',
      category: 'testing',
      author: '鲁班',
      tags: ['pytest', 'testing', 'api', 'authentication'],
      manual: '@manual://test-generator'
    };
  },

  getSchema() {
    return {
      type: 'object',
      properties: {
        api_info: {
          type: 'object',
          properties: {
            method: { type: 'string', description: 'HTTP方法' },
            path: { type: 'string', description: 'API路径' },
            description: { type: 'string', description: 'API描述' }
          },
          required: ['method', 'path', 'description']
        },
        test_scenarios: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string', description: '测试场景名称' },
              role: { type: 'string', enum: _VALID_ROLES, description: '用户角色' },
              expected_code: { type: 'number', description: '预期响应码' },
              test_data: { type: 'object', description: '测试数据' },
              verify_data: { type: 'object', description: '验证数据' }
            },
            required: ['name', 'role', 'expected_code']
          }
        },
        module_name: { type: 'string', description: '模块名称' },
        include_fixtures: { type: 'boolean', default: true, description: '是否包含fixture' },
        test_data: { type: 'object', description: '默认测试数据' }
      },
      required: ['api_info', 'test_scenarios']
    };
  },

  validate(params) {
    try {
      const generator = new TestGenerator();
      generator.validateApiInfo(params.api_info);
      generator.validateTestScenarios(params.test_scenarios);
      return { valid: true, errors: [] };
    } catch (error) {
      return {
        valid: false,
        errors: [error.message]
      };
    }
  },

  async execute(params) {
    const generator = new TestGenerator();
    return await generator.execute(params);
  }
};

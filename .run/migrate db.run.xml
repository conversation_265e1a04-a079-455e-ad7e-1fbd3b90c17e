<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="migrate db" type="BatchConfigurationType" factoryName="Batch" editBeforeRun="true">
    <module name="bethune" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SCRIPT_NAME" value="migrate" />
    <option name="PARAMETERS" value="-database 'mysql://root:Password!23@tcp(localhost:23306)/ca_insurance_dev' -path ./db/scripts/1.4.0 up" />
    <method v="2" />
  </configuration>
</component>

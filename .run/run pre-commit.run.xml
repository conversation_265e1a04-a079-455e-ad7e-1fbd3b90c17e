<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="run pre-commit" type="BatchConfigurationType" factoryName="Batch">
    <module name="bethune" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SCRIPT_NAME" value="conda" />
    <option name="PARAMETERS" value="run -n comintern_bethune_ve pre-commit run -a" />
    <method v="2" />
  </configuration>
</component>

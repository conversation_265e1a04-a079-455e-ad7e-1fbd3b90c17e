# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于FastAPI + SQLModel + Pydantic + pytest + MySQL技术栈的加拿大房产保险服务后端系统。项目采用严格的四层架构模式和规范化的开发流程。

## 常用开发命令

### 环境初始化
```bash
# 初始化开发环境
conda env create
conda activate comintern_bethune_ve
poetry install --all-groups

# 安装Git Hooks
yarn install
```

### 代码质量检查
```bash
# 手动执行静态检查
conda activate comintern_bethune_ve
pre-commit run -a
```

### 测试执行
```bash
# 运行所有测试
pytest

# 运行单个测试文件
pytest tests/api/endpoint/system/user_test.py

# 运行特定测试函数
pytest tests/api/endpoint/system/user_test.py::test_user_login

# 运行测试并生成覆盖率报告
pytest --cov=bethune --cov-report=html
```

### 应用启动
```bash
# 启动应用（需要先配置数据库和Redis连接）
python -m bethune.app
```

## 核心架构设计

### 四层架构模式

项目严格遵循四层架构，每一层都有明确的职责：

1. **API层** (`bethune/api/endpoint/`)
   - FastAPI路由实现
   - 使用ServiceContext进行依赖注入
   - 统一使用BaseResponse格式返回响应
   - 支持Query()依赖注入和Pagination分页

2. **DTO层** (`bethune/api/dto/`)
   - Pydantic数据传输对象
   - 包含Base/Create/Update/Response类结构
   - 必须实现to_model()和from_model()方法
   - 继承AuditMixin和ExtendedAuditMixin进行审计

3. **Service层** (`bethune/service/`)
   - 继承BaseService基类
   - 业务逻辑处理中心
   - 缓存装饰器集成
   - ServiceFactory工厂模式创建服务实例

4. **Repository层** (`bethune/repository/`)
   - 继承BaseRepository基类
   - 数据访问和SQLModel查询
   - 支持get_by_example和count_by_example方法
   - 使用session().exec()执行查询

### 关键组件

- **ServiceContext**: 服务上下文管理，实现依赖注入
- **BaseResponse**: 统一API响应格式 
- **Pagination**: 分页查询结果封装
- **AuditMixin**: 审计字段混入(created_at, updated_at)
- **Factory模式**: 服务和仓库的工厂创建

## 强制性编码规范

### 代码编写规则
- 私有常量必须以单下划线`_`开头
- 禁止使用`hasattr()`和`getattr()`，直接通过对象属性访问
- 避免不必要的中间变量，可直接return的表达式禁止定义中间变量
- 优先使用推导式替代for循环append操作
- 鼓励链式调用提升代码简洁性
- 严格遵循DRY原则，避免代码重复
- 禁止N+1查询问题
- 拒绝无意义注释
- **禁止使用if elif结构，必须使用独立的if语句**

### 导入和依赖规范
- 所有import语句必须写在文件最开始位置，禁止在方法内部写import
- 使用构造函数依赖注入，禁止在方法内部直接实例化Service类
- 代码复用优先级：直接使用 > 扩展现有 > 继承现有 > 创建新代码

### API返回格式
- API接口只能返回`BaseResponse.ok()`格式
- 路径参数id必须放在URL最后位置
- 禁止自定义返回类型

## 测试开发规范

### 测试结构
- 使用pytest框架，配置在`pytest.ini`
- 测试文件命名以`*_test.py`结尾
- 使用testcontainers进行MySQL和Redis容器化测试

### 测试编写原则
- 单接口测试：正确用例和错误用例
- 流程测试：多接口调用链测试
- 默认进行权限验证登录
- 只验证code字段，不加非必要assert
- 使用参数化测试避免重复代码

### 测试用户角色
- 系统管理员：<EMAIL>
- 代理人：<EMAIL>  
- 泛代：<EMAIL>
- 经纪行管理员：<EMAIL>

### Fixture设计
- `setup_db_session`: 数据库会话管理(rollback_only=True)
- `create_model/update_model/get_models`: 通用CRUD操作
- `create_user`: 用户创建辅助函数
- `auth_headers`: 认证头部信息

## 数据库设计

### 基线版本管理
- 当前基线版本：1.4.0
- 数据库脚本位于`db/scripts/`和`db/baseline/`
- 使用版本化SQL迁移脚本
- 测试环境自动应用基线和增量脚本

### SQLModel规范
- 继承BaseModel和AuditMixin
- 使用session().exec()执行查询
- 支持relationship关系映射
- 统一的to_column_expression_argument查询条件构建

## 国际化支持

- 支持语言：英语(en)、法语(fr)、中文(zh)
- 配置文件：`babel.cfg`
- 模板目录：`bethune/templates/`
- 使用fastapi-babel中间件

## 关键注意事项

1. **严格遵循四层架构**：不允许跨层调用或职责混乱
2. **统一异常处理**：使用BusinessError和NotFoundError
3. **依赖注入模式**：通过ServiceContext管理服务依赖
4. **测试驱动开发**：任何新功能必须有对应测试用例
5. **代码质量保证**：通过pre-commit hooks和静态检查
6. **条件判断规范**：禁止if elif结构，使用独立if语句

## 项目特定配置

- Python版本要求：>= 3.13
- 主要依赖：FastAPI 0.116+, SQLModel 0.0.24+, Pydantic 2.11+
- 数据库：MySQL 8.0+ with JSON字段支持
- 缓存：Redis 6.2+ with hiredis
- 任务调度：APScheduler集成
- 邮件服务：aiosmtplib异步邮件发送
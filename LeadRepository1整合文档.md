# LeadRepository1 整合文档

## 概述

`LeadRepository1` 是一个整合了原有三个Repository类功能的统一数据访问层，继承自 `GenericRepository`，提供了对Lead相关所有实体的完整CRUD操作和业务查询功能。

## 设计目标

- **统一管理**：将Lead、LeadReferralFeePayment、LeadApplication三个实体的数据访问逻辑整合到一个类中
- **简化依赖**：减少Service层和API层需要注入的Repository数量
- **提升维护性**：统一的接口设计，便于后续功能扩展和维护
- **保持兼容**：保留原有方法签名，确保现有代码无需修改

## 类结构

```python
class LeadRepository1(GenericRepository):
    # Lead实体相关方法
    # LeadReferralFeePayment实体相关方法  
    # LeadApplication实体相关方法
```

## 功能模块

### 1. Lead实体操作

#### 基础CRUD操作
- `get_lead_by_id(id: int) -> Lead`：根据ID获取Lead实体
- `get_lead_by_id_safely(id: int) -> Lead | None`：安全获取Lead实体，不存在时返回None
- `create_lead(lead: Lead) -> Lead`：创建新的Lead实体
- `update_lead(lead: Lead) -> Lead`：更新Lead实体
- `delete_lead(lead: Lead) -> None`：删除Lead实体
- `delete_lead_by_id(id: int) -> None`：根据ID删除Lead实体

#### 业务查询方法
- `get_by_id_with_rich_info(id: int) -> LeadRichInfoComposite`：获取包含关联信息的Lead详情
- `get_by_broker_id(broker_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> list[Lead]`：根据代理人ID获取Lead列表
- `get_by_query_filters(broker: Broker, filters: LeadQueryFilters, offset: int = 0, limit: int = DEFAULT_LIMIT) -> tuple[int, list[LeadRichInfoComposite]]`：根据查询条件获取Lead列表和总数
- `get_all_lead_info() -> list[LeadRichInfoComposite]`：获取所有Lead的详细信息
- `update_leads_by_expiration(expired_datetime: datetime) -> int`：批量更新过期Lead状态

### 2. LeadReferralFeePayment实体操作

#### 基础CRUD操作
- `get_referral_fee_payment_by_id(id: int) -> LeadReferralFeePayment`：根据ID获取推荐费支付记录
- `get_referral_fee_payment_by_id_safely(id: int) -> LeadReferralFeePayment | None`：安全获取推荐费支付记录
- `create_referral_fee_payment(payment: LeadReferralFeePayment) -> LeadReferralFeePayment`：创建推荐费支付记录
- `update_referral_fee_payment(payment: LeadReferralFeePayment) -> LeadReferralFeePayment`：更新推荐费支付记录
- `delete_referral_fee_payment(payment: LeadReferralFeePayment) -> None`：删除推荐费支付记录
- `delete_referral_fee_payment_by_id(id: int) -> None`：根据ID删除推荐费支付记录

#### 业务查询方法
- `get_referral_fee_payment_by_lead(lead_id: int) -> LeadReferralFeePayment | None`：根据Lead ID获取推荐费支付记录
- `get_total_lead_referral_fee(broker: Broker) -> float`：获取代理人的总推荐费金额
- `get_total_lead_count(broker: Broker) -> int`：获取代理人的Lead总数
- `get_referral_fee_payments_by_lead_ids(lead_ids: list[int]) -> list[LeadReferralFeePayment]`：根据Lead ID列表批量获取推荐费支付记录

### 3. LeadApplication实体操作

#### 基础CRUD操作
- `get_lead_application_by_id(id: int) -> LeadApplication`：根据ID获取Lead申请关联记录
- `get_lead_application_by_id_safely(id: int) -> LeadApplication | None`：安全获取Lead申请关联记录
- `create_lead_application(application: LeadApplication) -> LeadApplication`：创建Lead申请关联记录
- `update_lead_application(application: LeadApplication) -> LeadApplication`：更新Lead申请关联记录
- `delete_lead_application(application: LeadApplication) -> None`：删除Lead申请关联记录
- `delete_lead_application_by_id(id: int) -> None`：根据ID删除Lead申请关联记录

#### 业务查询方法
- `get_lead_application_by_application_id(application_id: int) -> LeadApplication | None`：根据申请ID获取Lead申请关联记录
- `get_lead_applications_by_application_ids(application_ids: list[int]) -> list[LeadApplication]`：根据申请ID列表批量获取Lead申请关联记录

## 使用示例

### Service层使用
```python
class LeadService(BaseService):
    def __init__(self, lead_repository: LeadRepository1):
        self.lead_repository = lead_repository
    
    def get_lead_detail(self, lead_id: int) -> LeadRichInfoComposite:
        return self.lead_repository.get_by_id_with_rich_info(lead_id)
    
    def create_lead_with_payment(self, lead: Lead, payment: LeadReferralFeePayment) -> tuple[Lead, LeadReferralFeePayment]:
        created_lead = self.lead_repository.create_lead(lead)
        payment.lead_id = created_lead.id
        created_payment = self.lead_repository.create_referral_fee_payment(payment)
        return created_lead, created_payment
```

### API层使用
```python
@router.get("/leads/{lead_id}")
async def get_lead_detail(
    lead_id: int,
    lead_repository: Annotated[LeadRepository1, Depends()]
) -> BaseResponse[LeadRichInfoComposite]:
    lead_detail = lead_repository.get_by_id_with_rich_info(lead_id)
    return BaseResponse.ok(lead_detail)
```

## 迁移指南

### 从原有Repository迁移

1. **替换依赖注入**
   ```python
   # 原有方式
   def __init__(
       self, 
       lead_repo: LeadRepository,
       payment_repo: LeadReferralFeePaymentRepository,
       application_repo: LeadApplicationRepository
   ):
   
   # 新方式
   def __init__(self, lead_repository: LeadRepository1):
   ```

2. **方法调用更新**
   ```python
   # 原有方式
   lead = lead_repo.get_by_id(lead_id)
   payment = payment_repo.get_referral_fee_payment_by_lead(lead_id)
   
   # 新方式
   lead = lead_repository.get_lead_by_id(lead_id)
   payment = lead_repository.get_referral_fee_payment_by_lead(lead_id)
   ```

## 优势

1. **减少依赖复杂度**：Service层只需注入一个Repository而不是三个
2. **统一事务管理**：所有相关操作在同一个Repository中，便于事务控制
3. **提升开发效率**：减少样板代码，统一的接口设计
4. **便于维护**：相关功能集中管理，便于后续扩展和维护
5. **保持向后兼容**：保留原有方法签名，现有代码无需大幅修改

## 注意事项

1. **方法命名**：为了区分不同实体的操作，方法名添加了实体前缀（如`get_lead_by_id`）
2. **原有类保留**：原有的三个Repository类仍然保留，确保现有代码的兼容性
3. **链式调用**：遵循团队编码规范，使用链式调用优化SQL语句构建
4. **推导式优化**：使用列表推导式替代循环操作，提升代码简洁性

## 后续计划

1. **逐步迁移**：建议逐步将现有Service层迁移到使用`LeadRepository1`
2. **性能优化**：根据实际使用情况，进一步优化查询性能
3. **功能扩展**：根据业务需求，继续扩展相关的业务查询方法
4. **测试完善**：为新的Repository类编写完整的单元测试和集成测试

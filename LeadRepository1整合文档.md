# LeadRepository1 整合文档

## 概述

`LeadRepository1` 是一个整合了原有三个Repository类功能的统一数据访问层，继承自 `GenericRepository`，提供了对Lead相关所有实体的完整CRUD操作和业务查询功能。

## 设计目标

- **统一管理**：将Lead、LeadReferralFeePayment、LeadApplication三个实体的数据访问逻辑整合到一个类中
- **简化依赖**：减少Service层和API层需要注入的Repository数量
- **提升维护性**：统一的接口设计，便于后续功能扩展和维护
- **保持兼容**：保留原有方法签名，确保现有代码无需修改

## 类结构

```python
class LeadRepository1(GenericRepository):
    # Lead实体相关方法
    # LeadReferralFeePayment实体相关方法
    # LeadApplication实体相关方法
```

## 方法来源映射表

| LeadRepository1方法 | 原始来源 | 迁移类型 | 说明 |
|---|---|---|---|
| **Lead实体基础CRUD** |
| `get_lead_by_id()` | `GenericRepository.get_by_id()` | 新增封装 | 封装通用方法，指定Lead实体 |
| `get_lead_by_id_safely()` | `GenericRepository.get_by_id_safely()` | 新增封装 | 封装通用方法，指定Lead实体 |
| `create_lead()` | `GenericRepository.create()` | 新增封装 | 封装通用方法，指定Lead实体 |
| `update_lead()` | `GenericRepository.update()` | 新增封装 | 封装通用方法，指定Lead实体 |
| `delete_lead()` | `GenericRepository.delete()` | 新增封装 | 封装通用方法，指定Lead实体 |
| `delete_lead_by_id()` | `GenericRepository.delete_by_id()` | 新增封装 | 封装通用方法，指定Lead实体 |
| **Lead实体业务查询** |
| `get_by_id_with_rich_info()` | `LeadRepository.get_by_id_with_rich_info()` | 直接迁移 | 保持原有逻辑不变 |
| `get_by_broker_id()` | `LeadRepository.get_by_broker_id()` | 优化迁移 | 优化为链式调用 |
| `get_by_query_filters()` | `LeadRepository.get_by_query_filters()` | 优化迁移 | 优化为链式调用和推导式 |
| `get_all_lead_info()` | `LeadRepository.get_all_lead_info()` | 直接迁移 | 保持原有逻辑不变 |
| `update_leads_by_expiration()` | `LeadRepository.update_leads_by_expiration()` | 直接迁移 | 保持原有逻辑不变 |
| **推荐费支付基础CRUD** |
| `get_referral_fee_payment_by_id()` | `GenericRepository.get_by_id()` | 新增封装 | 封装通用方法，指定LeadReferralFeePayment实体 |
| `get_referral_fee_payment_by_id_safely()` | `GenericRepository.get_by_id_safely()` | 新增封装 | 封装通用方法，指定LeadReferralFeePayment实体 |
| `create_referral_fee_payment()` | `GenericRepository.create()` | 新增封装 | 封装通用方法，指定LeadReferralFeePayment实体 |
| `update_referral_fee_payment()` | `GenericRepository.update()` | 新增封装 | 封装通用方法，指定LeadReferralFeePayment实体 |
| `delete_referral_fee_payment()` | `GenericRepository.delete()` | 新增封装 | 封装通用方法，指定LeadReferralFeePayment实体 |
| `delete_referral_fee_payment_by_id()` | `GenericRepository.delete_by_id()` | 新增封装 | 封装通用方法，指定LeadReferralFeePayment实体 |
| **推荐费支付业务查询** |
| `get_referral_fee_payment_by_lead()` | `LeadReferralFeePaymentRepository.get_referral_fee_payment_by_lead()` | 直接迁移 | 保持原有逻辑不变 |
| `get_total_lead_referral_fee()` | `LeadReferralFeePaymentRepository.get_total_lead_referral_fee()` | 优化迁移 | 优化为链式调用 |
| `get_total_lead_count()` | `LeadReferralFeePaymentRepository.get_total_lead_count()` | 直接迁移 | 保持原有逻辑不变 |
| `get_referral_fee_payments_by_lead_ids()` | `LeadReferralFeePaymentRepository.get_by_lead_ids()` | 重命名迁移 | 方法名更加语义化 |
| **申请关联基础CRUD** |
| `get_lead_application_by_id()` | `GenericRepository.get_by_id()` | 新增封装 | 封装通用方法，指定LeadApplication实体 |
| `get_lead_application_by_id_safely()` | `GenericRepository.get_by_id_safely()` | 新增封装 | 封装通用方法，指定LeadApplication实体 |
| `create_lead_application()` | `GenericRepository.create()` | 新增封装 | 封装通用方法，指定LeadApplication实体 |
| `update_lead_application()` | `GenericRepository.update()` | 新增封装 | 封装通用方法，指定LeadApplication实体 |
| `delete_lead_application()` | `GenericRepository.delete()` | 新增封装 | 封装通用方法，指定LeadApplication实体 |
| `delete_lead_application_by_id()` | `GenericRepository.delete_by_id()` | 新增封装 | 封装通用方法，指定LeadApplication实体 |
| **申请关联业务查询** |
| `get_lead_application_by_application_id()` | `LeadApplicationRepository.get_by_application_id()` | 直接迁移 | 保持原有逻辑不变 |
| `get_lead_applications_by_application_ids()` | `LeadApplicationRepository.get_by_application_ids()` | 直接迁移 | 保持原有逻辑不变 |

## 功能模块

### 1. Lead实体操作

#### 基础CRUD操作
- `get_lead_by_id(id: int) -> Lead`：根据ID获取Lead实体
  - **来源**：`GenericRepository.get_by_id()` - 新增封装方法
- `get_lead_by_id_safely(id: int) -> Lead | None`：安全获取Lead实体，不存在时返回None
  - **来源**：`GenericRepository.get_by_id_safely()` - 新增封装方法
- `create_lead(lead: Lead) -> Lead`：创建新的Lead实体
  - **来源**：`GenericRepository.create()` - 新增封装方法
- `update_lead(lead: Lead) -> Lead`：更新Lead实体
  - **来源**：`GenericRepository.update()` - 新增封装方法
- `delete_lead(lead: Lead) -> None`：删除Lead实体
  - **来源**：`GenericRepository.delete()` - 新增封装方法
- `delete_lead_by_id(id: int) -> None`：根据ID删除Lead实体
  - **来源**：`GenericRepository.delete_by_id()` - 新增封装方法

#### 业务查询方法
- `get_by_id_with_rich_info(id: int) -> LeadRichInfoComposite`：获取包含关联信息的Lead详情
  - **来源**：`LeadRepository.get_by_id_with_rich_info()` - 直接迁移
- `get_by_broker_id(broker_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> list[Lead]`：根据代理人ID获取Lead列表
  - **来源**：`LeadRepository.get_by_broker_id()` - 直接迁移，优化为链式调用
- `get_by_query_filters(broker: Broker, filters: LeadQueryFilters, offset: int = 0, limit: int = DEFAULT_LIMIT) -> tuple[int, list[LeadRichInfoComposite]]`：根据查询条件获取Lead列表和总数
  - **来源**：`LeadRepository.get_by_query_filters()` - 直接迁移，优化为链式调用和推导式
- `get_all_lead_info() -> list[LeadRichInfoComposite]`：获取所有Lead的详细信息
  - **来源**：`LeadRepository.get_all_lead_info()` - 直接迁移
- `update_leads_by_expiration(expired_datetime: datetime) -> int`：批量更新过期Lead状态
  - **来源**：`LeadRepository.update_leads_by_expiration()` - 直接迁移

### 2. LeadReferralFeePayment实体操作

#### 基础CRUD操作
- `get_referral_fee_payment_by_id(id: int) -> LeadReferralFeePayment`：根据ID获取推荐费支付记录
  - **来源**：`GenericRepository.get_by_id()` - 新增封装方法
- `get_referral_fee_payment_by_id_safely(id: int) -> LeadReferralFeePayment | None`：安全获取推荐费支付记录
  - **来源**：`GenericRepository.get_by_id_safely()` - 新增封装方法
- `create_referral_fee_payment(payment: LeadReferralFeePayment) -> LeadReferralFeePayment`：创建推荐费支付记录
  - **来源**：`GenericRepository.create()` - 新增封装方法
- `update_referral_fee_payment(payment: LeadReferralFeePayment) -> LeadReferralFeePayment`：更新推荐费支付记录
  - **来源**：`GenericRepository.update()` - 新增封装方法
- `delete_referral_fee_payment(payment: LeadReferralFeePayment) -> None`：删除推荐费支付记录
  - **来源**：`GenericRepository.delete()` - 新增封装方法
- `delete_referral_fee_payment_by_id(id: int) -> None`：根据ID删除推荐费支付记录
  - **来源**：`GenericRepository.delete_by_id()` - 新增封装方法

#### 业务查询方法
- `get_referral_fee_payment_by_lead(lead_id: int) -> LeadReferralFeePayment | None`：根据Lead ID获取推荐费支付记录
  - **来源**：`LeadReferralFeePaymentRepository.get_referral_fee_payment_by_lead()` - 直接迁移
- `get_total_lead_referral_fee(broker: Broker) -> float`：获取代理人的总推荐费金额
  - **来源**：`LeadReferralFeePaymentRepository.get_total_lead_referral_fee()` - 直接迁移，优化为链式调用
- `get_total_lead_count(broker: Broker) -> int`：获取代理人的Lead总数
  - **来源**：`LeadReferralFeePaymentRepository.get_total_lead_count()` - 直接迁移
- `get_referral_fee_payments_by_lead_ids(lead_ids: list[int]) -> list[LeadReferralFeePayment]`：根据Lead ID列表批量获取推荐费支付记录
  - **来源**：`LeadReferralFeePaymentRepository.get_by_lead_ids()` - 迁移并重命名方法

### 3. LeadApplication实体操作

#### 基础CRUD操作
- `get_lead_application_by_id(id: int) -> LeadApplication`：根据ID获取Lead申请关联记录
  - **来源**：`GenericRepository.get_by_id()` - 新增封装方法
- `get_lead_application_by_id_safely(id: int) -> LeadApplication | None`：安全获取Lead申请关联记录
  - **来源**：`GenericRepository.get_by_id_safely()` - 新增封装方法
- `create_lead_application(application: LeadApplication) -> LeadApplication`：创建Lead申请关联记录
  - **来源**：`GenericRepository.create()` - 新增封装方法
- `update_lead_application(application: LeadApplication) -> LeadApplication`：更新Lead申请关联记录
  - **来源**：`GenericRepository.update()` - 新增封装方法
- `delete_lead_application(application: LeadApplication) -> None`：删除Lead申请关联记录
  - **来源**：`GenericRepository.delete()` - 新增封装方法
- `delete_lead_application_by_id(id: int) -> None`：根据ID删除Lead申请关联记录
  - **来源**：`GenericRepository.delete_by_id()` - 新增封装方法

#### 业务查询方法
- `get_lead_application_by_application_id(application_id: int) -> LeadApplication | None`：根据申请ID获取Lead申请关联记录
  - **来源**：`LeadApplicationRepository.get_by_application_id()` - 直接迁移
- `get_lead_applications_by_application_ids(application_ids: list[int]) -> list[LeadApplication]`：根据申请ID列表批量获取Lead申请关联记录
  - **来源**：`LeadApplicationRepository.get_by_application_ids()` - 直接迁移

## 使用示例

### Service层使用
```python
class LeadService(BaseService):
    def __init__(self, lead_repository: LeadRepository1):
        self.lead_repository = lead_repository

    def get_lead_detail(self, lead_id: int) -> LeadRichInfoComposite:
        return self.lead_repository.get_by_id_with_rich_info(lead_id)

    def create_lead_with_payment(self, lead: Lead, payment: LeadReferralFeePayment) -> tuple[Lead, LeadReferralFeePayment]:
        created_lead = self.lead_repository.create_lead(lead)
        payment.lead_id = created_lead.id
        created_payment = self.lead_repository.create_referral_fee_payment(payment)
        return created_lead, created_payment
```

### API层使用
```python
@router.get("/leads/{lead_id}")
async def get_lead_detail(
    lead_id: int,
    lead_repository: Annotated[LeadRepository1, Depends()]
) -> BaseResponse[LeadRichInfoComposite]:
    lead_detail = lead_repository.get_by_id_with_rich_info(lead_id)
    return BaseResponse.ok(lead_detail)
```

## 原始Repository类方法清单

### LeadRepository (继承BaseRepository[Lead])
```python
# 继承的基础CRUD方法
- create(obj: Lead) -> Lead
- update(obj: Lead) -> Lead
- get_by_id(id: int) -> Lead
- get_by_id_safely(id: int) -> Lead | None
- delete(obj: Lead) -> None
- delete_by_id(id: int) -> None

# 自定义业务方法
- get_by_id_with_rich_info(id: int) -> LeadRichInfoComposite
- get_by_broker_id(broker_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> list[Lead]
- get_by_query_filters(broker: Broker, filters: LeadQueryFilters, offset: int = 0, limit: int = DEFAULT_LIMIT) -> tuple[int, list[LeadRichInfoComposite]]
- update_leads_by_expiration(expired_datetime: datetime) -> int
- get_all_lead_info() -> list[LeadRichInfoComposite]
```

### LeadReferralFeePaymentRepository (继承BaseRepository[LeadReferralFeePayment])
```python
# 继承的基础CRUD方法
- create(obj: LeadReferralFeePayment) -> LeadReferralFeePayment
- update(obj: LeadReferralFeePayment) -> LeadReferralFeePayment
- get_by_id(id: int) -> LeadReferralFeePayment
- get_by_id_safely(id: int) -> LeadReferralFeePayment | None
- delete(obj: LeadReferralFeePayment) -> None
- delete_by_id(id: int) -> None

# 自定义业务方法
- get_referral_fee_payment_by_lead(lead_id: int) -> LeadReferralFeePayment | None
- get_total_lead_referral_fee(broker: Broker) -> float
- get_total_lead_count(broker: Broker) -> int
- get_by_lead_ids(lead_ids: list[int]) -> list[LeadReferralFeePayment]
```

### LeadApplicationRepository (继承BaseRepository[LeadApplication])
```python
# 继承的基础CRUD方法
- create(obj: LeadApplication) -> LeadApplication
- update(obj: LeadApplication) -> LeadApplication
- get_by_id(id: int) -> LeadApplication
- get_by_id_safely(id: int) -> LeadApplication | None
- delete(obj: LeadApplication) -> None
- delete_by_id(id: int) -> None

# 自定义业务方法
- get_by_application_id(application_id: int) -> LeadApplication | None
- get_by_application_ids(application_ids: list[int]) -> list[LeadApplication]
```

## 迁移指南

### 详细迁移对照表

#### Lead实体方法迁移
| 原有调用 | 新调用 | 说明 |
|---|---|---|
| `lead_repo.get_by_id(id)` | `lead_repository.get_lead_by_id(id)` | 基础CRUD |
| `lead_repo.get_by_id_safely(id)` | `lead_repository.get_lead_by_id_safely(id)` | 基础CRUD |
| `lead_repo.create(lead)` | `lead_repository.create_lead(lead)` | 基础CRUD |
| `lead_repo.update(lead)` | `lead_repository.update_lead(lead)` | 基础CRUD |
| `lead_repo.delete(lead)` | `lead_repository.delete_lead(lead)` | 基础CRUD |
| `lead_repo.delete_by_id(id)` | `lead_repository.delete_lead_by_id(id)` | 基础CRUD |
| `lead_repo.get_by_id_with_rich_info(id)` | `lead_repository.get_by_id_with_rich_info(id)` | 业务查询，无变化 |
| `lead_repo.get_by_broker_id(broker_id, offset, limit)` | `lead_repository.get_by_broker_id(broker_id, offset, limit)` | 业务查询，无变化 |
| `lead_repo.get_by_query_filters(broker, filters, offset, limit)` | `lead_repository.get_by_query_filters(broker, filters, offset, limit)` | 业务查询，无变化 |
| `lead_repo.get_all_lead_info()` | `lead_repository.get_all_lead_info()` | 业务查询，无变化 |
| `lead_repo.update_leads_by_expiration(datetime)` | `lead_repository.update_leads_by_expiration(datetime)` | 业务查询，无变化 |

#### 推荐费支付方法迁移
| 原有调用 | 新调用 | 说明 |
|---|---|---|
| `payment_repo.get_by_id(id)` | `lead_repository.get_referral_fee_payment_by_id(id)` | 基础CRUD |
| `payment_repo.get_by_id_safely(id)` | `lead_repository.get_referral_fee_payment_by_id_safely(id)` | 基础CRUD |
| `payment_repo.create(payment)` | `lead_repository.create_referral_fee_payment(payment)` | 基础CRUD |
| `payment_repo.update(payment)` | `lead_repository.update_referral_fee_payment(payment)` | 基础CRUD |
| `payment_repo.delete(payment)` | `lead_repository.delete_referral_fee_payment(payment)` | 基础CRUD |
| `payment_repo.delete_by_id(id)` | `lead_repository.delete_referral_fee_payment_by_id(id)` | 基础CRUD |
| `payment_repo.get_referral_fee_payment_by_lead(lead_id)` | `lead_repository.get_referral_fee_payment_by_lead(lead_id)` | 业务查询，无变化 |
| `payment_repo.get_total_lead_referral_fee(broker)` | `lead_repository.get_total_lead_referral_fee(broker)` | 业务查询，无变化 |
| `payment_repo.get_total_lead_count(broker)` | `lead_repository.get_total_lead_count(broker)` | 业务查询，无变化 |
| `payment_repo.get_by_lead_ids(lead_ids)` | `lead_repository.get_referral_fee_payments_by_lead_ids(lead_ids)` | 方法名更语义化 |

#### 申请关联方法迁移
| 原有调用 | 新调用 | 说明 |
|---|---|---|
| `application_repo.get_by_id(id)` | `lead_repository.get_lead_application_by_id(id)` | 基础CRUD |
| `application_repo.get_by_id_safely(id)` | `lead_repository.get_lead_application_by_id_safely(id)` | 基础CRUD |
| `application_repo.create(application)` | `lead_repository.create_lead_application(application)` | 基础CRUD |
| `application_repo.update(application)` | `lead_repository.update_lead_application(application)` | 基础CRUD |
| `application_repo.delete(application)` | `lead_repository.delete_lead_application(application)` | 基础CRUD |
| `application_repo.delete_by_id(id)` | `lead_repository.delete_lead_application_by_id(id)` | 基础CRUD |
| `application_repo.get_by_application_id(app_id)` | `lead_repository.get_lead_application_by_application_id(app_id)` | 业务查询，无变化 |
| `application_repo.get_by_application_ids(app_ids)` | `lead_repository.get_lead_applications_by_application_ids(app_ids)` | 业务查询，无变化 |

### 从原有Repository迁移

1. **替换依赖注入**
   ```python
   # 原有方式
   def __init__(
       self,
       lead_repo: LeadRepository,
       payment_repo: LeadReferralFeePaymentRepository,
       application_repo: LeadApplicationRepository
   ):

   # 新方式
   def __init__(self, lead_repository: LeadRepository1):
   ```

2. **方法调用更新**
   ```python
   # 原有方式
   lead = lead_repo.get_by_id(lead_id)
   payment = payment_repo.get_referral_fee_payment_by_lead(lead_id)

   # 新方式
   lead = lead_repository.get_lead_by_id(lead_id)
   payment = lead_repository.get_referral_fee_payment_by_lead(lead_id)
   ```

## 优势

1. **减少依赖复杂度**：Service层只需注入一个Repository而不是三个
2. **统一事务管理**：所有相关操作在同一个Repository中，便于事务控制
3. **提升开发效率**：减少样板代码，统一的接口设计
4. **便于维护**：相关功能集中管理，便于后续扩展和维护
5. **保持向后兼容**：保留原有方法签名，现有代码无需大幅修改

## 注意事项

1. **方法命名**：为了区分不同实体的操作，方法名添加了实体前缀（如`get_lead_by_id`）
2. **原有类保留**：原有的三个Repository类仍然保留，确保现有代码的兼容性
3. **链式调用**：遵循团队编码规范，使用链式调用优化SQL语句构建
4. **推导式优化**：使用列表推导式替代循环操作，提升代码简洁性

## 后续计划

1. **逐步迁移**：建议逐步将现有Service层迁移到使用`LeadRepository1`
2. **性能优化**：根据实际使用情况，进一步优化查询性能
3. **功能扩展**：根据业务需求，继续扩展相关的业务查询方法
4. **测试完善**：为新的Repository类编写完整的单元测试和集成测试

# 概述

加拿大房产保险服务。


------


# 写在前面

对于框架的选择，我们将从SpringBoot全家桶的角度去比较，它最好包含但不限于以下一些能力：

* 缓存
* 认证与授权
* 错误处理
* 请求与响应的DTO自动转换
* 日志
* 监控/统计
* 数据库集成
* 国际化
* 链路跟踪
* 简易测试
* 服务熔断/降级


------


# 项目布局

以下，引导使用者进一步了解此项目中各个组成部分的设计目的及其功能性。


## 子目录
通常，一个标准后端RESTful API项目都由如下几个部分组成：

* Model（内部业务模型）
* DTO（跨系统数据交换、传输）
* Service（业务服务内部接口声明及实现）
* RESTful API（HTTP层实现）


## 编程语言

本项目以 **Python** 作为主力语言。


## 工程过程支持

主要是针对IDE、Python的构建过程所插装的一些能力，如下所示：

* 在项目目录下顺序执行下列命令序列，用以完成项目开发环境的初始化
  ```shell
  conda env create
  conda activate comintern_bethune_ve
  poetry install --all-groups
  ```
* **Git Hooks** 集成，通过执行下列命令安装 **Husky** ，并完成初始化
  ```shell
  yarn install
  ```
* 每次执行git提交动作时，会自动执行静态检查，或手动执行下列命令完成静态检查
  ```shell
  conda activate comintern_bethune_ve
  pre-commit run -a
  ```


------


# 项目定制指南

以下列举一些在项目定制过程中的一些要点或者可能遇到的问题。


## IDE支持

* 提前确保所用IDE具有Python支持
* 使用IntelliJ IDEA时，需要额外安装如下必要插件：`.ignore`、`EditorConfig`（IDEA默认自带，但需要启用）
* 在IntelliJ IDEA中可以直接通过 **Open** 的方式导入一个Python项目，而无需使用IntelliJ的特定Go插件
* 模板中包含[EditorConfig](https://editorconfig.org/)配置，所以可以要求项目开发者安装对应IDE的相关插件，来保证文本格式的基本一致性
* 在`.run`目录下，是一些常用的IntelliJ IDEA的 **Run Configuration**
* 在`.vscod`目录下，是一些常用的VS Code的配置

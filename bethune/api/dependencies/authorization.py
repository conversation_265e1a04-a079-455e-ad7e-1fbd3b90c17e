from typing import Annotated

from fastapi import Depends
from fastapi.security import SecurityScopes
from fastapi.security.oauth2 import OAuth2PasswordBearer

from bethune.api.dto.auth import TokenData
from bethune.api.error import UnauthenticatedError
from bethune.api.error import UnauthorizedError
from bethune.db.redis import get_redis
from bethune.model import User
from bethune.model import UserStatus
from bethune.service.core.email import EmailService
from bethune.service.core.factory import CoreServiceFactory
from bethune.service.system import ServiceFactory
from bethune.settings import settings
from bethune.util import concat_path

# from bethune.api.dto.user import User


oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{concat_path(settings.ROOT_PATH, settings.API_PREFIX, settings.API_VERSION)}/system/auth/token",
    auto_error=False,
)


async def extract_token_data(token: Annotated[str, Depends(oauth2_scheme)]) -> TokenData:
    if not token:
        raise UnauthenticatedError(err_msg="no authenticated")
    return TokenData.decode(token)


async def token_to_user(token: Annotated[str, Depends(oauth2_scheme)]) -> User:
    if not token:
        raise UnauthenticatedError(err_msg="no authenticated")
    user = await ServiceFactory.create_auth_service(ServiceFactory.create_user_service()).get_current_user(token)
    if user.status != UserStatus.ACTIVE:
        raise UnauthorizedError(err_msg="user is not active", detail={"id": user.id})

    return user


async def user_allowed_scopes(user: Annotated[User, Depends(token_to_user)]) -> set[str]:
    user_service = ServiceFactory.create_user_service()
    role_service = ServiceFactory.create_role_service()
    assigned_role_id = user_service.get_all_role_id(user.id)
    permission_codes = set()
    for role_id in assigned_role_id:
        permission_codes.update(role_service.get_all_permission_code(role_id))
    return permission_codes


async def authenticated_user(
    security_scopes: SecurityScopes,
    user: Annotated[User, Depends(token_to_user)],
    token: Annotated[str, Depends(oauth2_scheme)],
) -> User:
    if token is None:
        raise UnauthenticatedError(err_msg="no authenticated")
    # permissions defined in security scopes
    allowed_scopes = await user_allowed_scopes(user)
    # permissions defined in token
    token_data = TokenData.decode(token)
    if token_data.scopes:
        allowed_scopes = allowed_scopes.intersection(str.split(token_data.scopes, ";"))
    if not allowed_scopes.issuperset(security_scopes.scopes):
        raise UnauthorizedError(err_msg="无权访问", detail={"scopes": security_scopes.scopes})

    return user


def share_link_cache_key(serial_number: str):
    return f"bethune:insurance_application:share_link:{serial_number}"


async def extract_serial_number(token_data: Annotated[TokenData, Depends(extract_token_data)]) -> str | None:
    if not token_data.data_scopes:
        return None
    return token_data.data_scopes.get("insurance_application_serial_number")


async def check_not_shared_link(
    serial_number: str | None = Depends(extract_serial_number),
):
    if serial_number is not None:
        raise UnauthenticatedError("no authenticated")


async def check_has_cached_application_id(
    serial_number: str | None = Depends(extract_serial_number),
):
    if serial_number is None:
        return None
    cached_application_id = await get_redis().get(share_link_cache_key(serial_number))
    if not cached_application_id:
        raise UnauthorizedError("No permission to access this application")
    return str(cached_application_id, "UTF-8")


async def check_shared_link_permission(
    id: int,
    cached_application_id: str | None = Depends(check_has_cached_application_id),
):
    if cached_application_id is not None and cached_application_id != str(id):
        raise UnauthorizedError("No permission to access this application")


async def check_shared_link_permission_with_ref_code(
    ref_code: str,
    serial_number: str | None = Depends(extract_serial_number),
):
    if serial_number is None:
        return
    cached_application_id = await get_redis().get(share_link_cache_key(serial_number))
    if not cached_application_id:
        raise UnauthorizedError("No permission to access this application")
    # TODO : ref_code check


class BaseServiceContext:

    def __init__(self):
        self._current_user = None
        self._email_sender = None
        self._permissions = set()

    @property
    def current_user(self) -> User:
        return self._current_user

    @property
    def email_sender(self) -> EmailService:
        if not self._email_sender:
            return CoreServiceFactory.create_email_service()
        return self._email_sender

    @property
    def permissions(self) -> set[str]:
        return self._permissions

    @classmethod
    def create(
        cls,
        user: Annotated[User, Depends(authenticated_user)],
        permissions: Annotated[set[str], Depends(user_allowed_scopes)],
    ) -> "BaseServiceContext":
        instance = cls()
        instance._current_user = user
        instance._permissions = permissions
        return instance

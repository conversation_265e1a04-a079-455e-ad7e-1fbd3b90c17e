from typing import Any
from typing import TypeVar

from fastapi import Request

T = TypeVar("T")


class JsonExtractor:
    """公共的 JSON 数据提取器"""

    def __init__(self, json_data: dict[str, Any]):
        self.json_data = json_data

    def extract(self, model_class: type[T], mappings: dict[str, str]) -> T:
        """根据映射关系提取数据并转化为目标模型"""
        extracted_data = {}
        for model_field, json_path in mappings.items():
            value = self.get_value_by_path(json_path)
            if value is not None:
                extracted_data[model_field] = value
        return model_class(**extracted_data)

    def get_value_by_path(self, path: str) -> Any:
        """根据 JSON 路径获取对应的值，支持多级嵌套"""
        keys = path.split(".")
        value = self.json_data
        for key in keys:
            value = value.get(key, None)
            if value is None:
                break
        return value


async def get_json_extractor(request: Request) -> JsonExtractor:
    """从 Request 中解析 JSON 并初始化 JsonExtractor"""
    json_data: dict[str, Any] = await request.json()
    return JsonExtractor(json_data)

from datetime import datetime

import jwt
from jwt import PyJWTError
from pydantic import BaseModel
from pydantic import EmailStr
from pydantic import Field

from bethune.api.dto.base import LoginTypeEnum
from bethune.api.dto.base import VerificationCodeTypeEnum
from bethune.api.error import UnauthenticatedError
from bethune.logging import logger
from bethune.settings import settings


class TokenData(BaseModel):
    sub: str
    iat: datetime
    exp: datetime
    jti: str
    scopes: str | None = None
    data_scopes: dict[str, str] | None = None

    @classmethod
    def decode(cls, token: str) -> "TokenData":
        try:
            payload = jwt.decode(token, settings.AUTH_SECRET_KEY, algorithms=[settings.AUTH_ALGORITHM])
            return cls.model_validate(payload)
        except jwt.ExpiredSignatureError:
            raise UnauthenticatedError(err_msg="Token has expired")
        except PyJWTError as e:
            logger.error("JWT decode error", exc_info=e)
            raise UnauthenticatedError(err_msg="Invalid token")

    def encode(self) -> str:
        return jwt.encode(self.model_dump(), settings.AUTH_SECRET_KEY, algorithm=settings.AUTH_ALGORITHM)


class Token(BaseModel):
    access_token: str
    token_type: str = "Bearer"


class EmailVerification(BaseModel):
    email: EmailStr


class VerifyVerificationEmailCode(BaseModel):
    email: EmailStr
    verification_code: str


class SendEmailVerificationRequest(EmailVerification):
    pass


class VerificationRequest(EmailVerification):
    type: VerificationCodeTypeEnum = Field(VerificationCodeTypeEnum.GENERAL, description="验证码类型")


class SendEmailVerificationResponse(EmailVerification):
    pass


class ValidateEmailVerificationRequest(EmailVerification):
    verification_code: str


class ValidateEmailVerificationResponse(EmailVerification):
    validated: bool


class OAuth2PasswordRequestJSON(BaseModel):
    username: str
    password: str


class LoginRequestDTO(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    type: LoginTypeEnum = Field(LoginTypeEnum.SYSTEM, description="登录类型，默认SYSTEM")

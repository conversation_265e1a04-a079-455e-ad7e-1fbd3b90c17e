from datetime import date
from datetime import datetime
from enum import StrEnum

from pydantic import BaseModel
from pydantic import Field
from pydantic import field_serializer

from bethune.error import BusinessError
from bethune.util import get_text


class GenderEnum(StrEnum):
    MALE = "MALE"
    FEMALE = "FEMALE"
    OTHER = "OTHER"


class InsuranceType(StrEnum):
    """保险类型"""

    """房屋险"""
    HOUSE_INSURANCE = "HOUSE_INSURANCE"
    """租客险"""
    RENTERS_INSURANCE = "RENTERS_INSURANCE"
    """车险"""
    AUTO_INSURANCE = "AUTO_INSURANCE"
    """寿险"""
    LIFE_INSURANCE = "LIFE_INSURANCE"


class BusinessType(StrEnum):
    """业务类型"""

    POLICY_RENEWAL = "POLICY_RENEWAL"  # 续保
    PREMIUM_CALCULATED = "PREMIUM_CALCULATED"  # 保费计算
    POLICY_REJECT = "POLICY_REJECT"  # 退回
    POLICY_ISSUED = "POLICY_ISSUED"  # 保单承保
    OTHER = "OTHER"  # 其它


class BaseResponse[T](BaseModel):
    code: int
    message: str
    data: T | None = None
    metadata: dict | None = None

    @classmethod
    def ok(
        cls,
        data: T,
        code: int = 1000,
        message: str = "success",
        metadata: dict | None = None,
    ):
        return cls(code=code, message=get_text(message), data=data, metadata=metadata)


class BusinessErrorResponse(BaseResponse[dict]):

    @classmethod
    def fail(cls, error: BusinessError):
        return cls(
            code=error.code,
            message=error.message,
            data=None if error.detail is None else error.detail,
            metadata=error.metadata,
        )


class PageParams(BaseModel):
    # 分页查询参数
    page_no: int = Field(1, gt=0)  # 默认第一页
    page_size: int = Field(20, ge=1, le=100)

    def limit(self):
        return self.page_size

    def offset(self):
        return (self.page_no - 1) * self.page_size


class Pagination[T](PageParams):
    # 分页查询结果
    items: list[T]
    total: int

    @classmethod
    def from_items(cls, items: list[T], total: int, page_no: int, page_size: int):
        return cls(items=items, total=total, page_no=page_no, page_size=page_size)


class AuditMixin(BaseModel):
    created_at: datetime | None = None
    updated_at: datetime | None = None

    @field_serializer("created_at", "updated_at")
    def convert_to_date(v: datetime | None) -> date | None:  # noqa: N805
        return v.date() if v else None


class ExtendedAuditMixin(AuditMixin):
    is_deleted: bool = False
    deleted_at: datetime | None = None

    @field_serializer("created_at", "updated_at", "deleted_at")
    def convert_to_date(v: datetime | None) -> date | None:  # noqa: N805
        return v.date() if v else None


class WithStatus(BaseModel):
    status: StrEnum


class WithId(BaseModel):
    id: int


class InsuranceConsultationSource(StrEnum):
    """线索咨询来源"""

    ARTICLE = "ARTICLE"  # 文章
    OFFLINE_PROMOTION_MATERIAL = "OFFLINE_PROMOTION_MATERIAL"  # 线下海报
    MEDIA_PROMOTION_MATERIAL = "MEDIA_PROMOTION_MATERIAL"  # 线下海报


class ConsultationStatus(StrEnum):
    PENDING = "PENDING"  # 待处理
    COMPLETED = "COMPLETED"  # 完成


class LoginTypeEnum(StrEnum):
    SYSTEM = "SYSTEM"
    MOBILE = "MOBILE"
    PC = "PC"


class ResourceTypeEnum(StrEnum):
    LOGO = "LOGO"
    AVATAR = "AVATAR"


class VerificationCodeTypeEnum(StrEnum):
    REGISTRATION = "REGISTRATION"  # 注册/试用申请
    RESET_PASSWORD = "RESET_PASSWORD"  # 重置密码
    GENERAL = "GENERAL"  # 普通验证


class WithReferenceNumber(BaseModel):
    ref_code: str


class UploadRequest(BaseModel):
    type: ResourceTypeEnum


class ImageUploadRequest(UploadRequest):
    data: str  # base64编码的图片数据

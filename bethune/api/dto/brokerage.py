from pydantic import BaseModel
from pydantic import computed_field

from bethune.api.dto.base import AuditMixin
from bethune.api.dto.base import WithId
from bethune.model.brokerage import Brokerage as BrokerageModel
from bethune.model.brokerage import BrokerageTrialApplication as BrokerageTrialApplicationModel
from bethune.settings import settings


class BrokerageBase(BaseModel):
    name: str | None = None
    province: str | None = None
    city: str | None = None
    address: str | None = None
    postal_code: str | None = None
    contact_name: str | None = None
    contact_email: str | None = None
    contact_phone: str | None = None
    logo: str | None = None
    website: str | None = None
    description: str | None = None


class BrokerageResponse(BrokerageBase, WithId, AuditMixin):

    @computed_field
    def logo_url(self) -> str | None:
        return (
            f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.LOGOS_FOLDER}/{self.logo}"
            if self.logo
            else None
        )

    @classmethod
    def from_model(cls, model: BrokerageModel):
        return cls(**model.model_dump())


class BrokerageCreatingRequest(BrokerageBase):
    def to_model(self) -> BrokerageModel:
        return BrokerageModel(**self.model_dump(exclude_unset=True, exclude_none=True))


class BrokerageUpdatingRequest(BaseModel):
    name: str | None = None
    province: str | None = None
    city: str | None = None
    address: str | None = None
    postal_code: str | None = None
    contact_name: str | None = None
    contact_email: str | None = None
    contact_phone: str | None = None
    website: str | None = None
    description: str | None = None
    logo_url: str | None = None

    def to_model(self, id: int) -> BrokerageModel:
        data = self.model_dump(exclude_unset=True, exclude_none=True, exclude={"id", "logo_url"})

        if self.logo_url:
            url_path = f"/{settings.UPLOADS_FOLDER}/{settings.LOGOS_FOLDER}/"
            if url_path in self.logo_url:
                data["logo"] = self.logo_url.split(url_path)[-1]

        return BrokerageModel(**data | {"id": id})


class BrokerageTrialApplicationBase(BaseModel):
    name: str
    contact_name: str
    contact_email: str
    contact_phone: str


class BrokerageTrialApplicationResponse(BrokerageTrialApplicationBase, WithId, AuditMixin):
    @classmethod
    def from_model(cls, model: BrokerageTrialApplicationModel):
        return cls(**model.model_dump())


class BrokerageTrialApplicationCreatingRequest(BrokerageTrialApplicationBase):
    verification_code: str

    def to_model(self) -> BrokerageTrialApplicationModel:
        return BrokerageTrialApplicationModel(**self.model_dump(exclude={"verification_code"}))

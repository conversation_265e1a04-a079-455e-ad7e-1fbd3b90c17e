from pydantic import BaseModel
from pydantic import computed_field
from pydantic import EmailStr
from pydantic import Field

from bethune.api.dto import PageParams
from bethune.api.dto.base import AuditMixin
from bethune.api.dto.base import WithId
from bethune.api.dto.brokerage import BrokerageResponse
from bethune.api.dto.dict.brokerage import BrokerageUserRoleEnum
from bethune.model import User as UserModel
from bethune.model import UserStatus
from bethune.model.broker import Broker as BrokerModel
from bethune.model.brokerage_user import Brokerage as BrokerageModel
from bethune.model.brokerage_user import BrokerageUser as BrokerageUserModel
from bethune.model.brokerage_user import BrokerageUserQueryFilters
from bethune.model.brokerage_user import BrokerageUserRichInfoComposite as BrokerageUserRichInfoCompositeModel
from bethune.model.system import ROLE_BROKER_NAME
from bethune.model.system import ROLE_BROKER_SUPPORT_NAME
from bethune.model.system import ROLE_BROKERAGE_ADMIN_NAME

_ROLE_NAME_TO_USER_ROLE_MAPPINGS = {
    ROLE_BROKER_SUPPORT_NAME: BrokerageUserRoleEnum.SUPPORT,
    ROLE_BROKERAGE_ADMIN_NAME: BrokerageUserRoleEnum.ADMIN,
    ROLE_BROKER_NAME: BrokerageUserRoleEnum.BROKER,
}

_USER_ROLE_TO_ROLE_NAME_MAPPINGS = {v: k for k, v in _ROLE_NAME_TO_USER_ROLE_MAPPINGS.items()}


def _email(user: UserModel) -> str | EmailStr:
    return user.email


def _user_role(user: UserModel) -> BrokerageUserRoleEnum:
    return next(
        (
            _ROLE_NAME_TO_USER_ROLE_MAPPINGS[role.name]
            for role in user.roles
            if role.name in _ROLE_NAME_TO_USER_ROLE_MAPPINGS.keys()
        ),
        BrokerageUserRoleEnum.UNKNOWN,
    )


def _enabled(user: UserModel) -> bool:
    return user.status == UserStatus.ACTIVE


class BrokerageUserBase(BaseModel):
    pass


class BrokerageUserResponse(BrokerageUserBase, WithId, AuditMixin):
    user_id: int
    brokerage_id: int
    name: str
    phone: str

    brokerage_: BrokerageModel = Field(exclude=True)
    user_: UserModel = Field(exclude=True)
    broker_: BrokerModel | None = Field(default=None, exclude=True)

    @computed_field
    def email(self) -> str | EmailStr:
        return _email(self.user_)

    @computed_field
    def permissions(self) -> set[str]:
        """
        名称维持与Broker相同
        :return: 该用户所有角色的权限去重合集
        """
        return self.user_.all_permissions

    @computed_field
    def enabled(self) -> bool:
        return _enabled(self.user_)

    @computed_field
    def user_role(self) -> BrokerageUserRoleEnum:
        return _user_role(self.user_)

    @computed_field
    def brokerage(self) -> BrokerageResponse:
        return BrokerageResponse.from_model(self.brokerage_)

    @classmethod
    def from_model(cls, model: BrokerageUserModel, broker: BrokerModel | None = None):
        return cls(**model.model_dump() | {"user_": model.user, "brokerage_": model.brokerage, "broker_": broker})

    @computed_field
    def province(self) -> str | None:
        if self.broker_:
            return self.broker_.province
        return None

    @computed_field
    def city(self) -> str | None:
        if self.broker_:
            return self.broker_.city
        return None

    @computed_field
    def address(self) -> str | None:
        if self.broker_:
            return self.broker_.address
        return None

    @computed_field
    def postal_code(self) -> str | None:
        if self.broker_:
            return self.broker_.postal_code
        return None


class BrokerageUserCreatingRequest(BrokerageUserBase):
    name: str
    email: str
    password: str
    phone: str
    province: str | None = None
    city: str | None = None
    address: str | None = None
    postal_code: str | None = None
    user_role: BrokerageUserRoleEnum = BrokerageUserRoleEnum.SUPPORT

    def to_model(self, user: UserModel, brokerage: BrokerageModel) -> BrokerageUserModel:
        return BrokerageUserModel(
            **self.model_dump(
                exclude_unset=True, exclude_none=True, exclude={"email", "password", "postal_code", "user_role"}
            )
            | {
                "user_id": user.id,
                "brokerage_id": brokerage.id,
            }
        )


class BrokerageUserUpdatingRequest(BrokerageUserBase):
    name: str | None = None
    province: str | None = None
    city: str | None = None
    address: str | None = None
    postal_code: str | None = None
    phone: str | None = None
    enabled: bool | None = None
    password: str | None = None

    def to_model(self, id: int) -> BrokerageUserModel:
        return BrokerageUserModel(
            **self.model_dump(exclude_unset=True, exclude_none=True)
            | {
                "id": id,
            }
        )


class BrokerageUserListQueryParams(PageParams):
    name: str | None = None
    email: str | None = None
    user_role: BrokerageUserRoleEnum

    def to_filters(self, brokerage_id: int) -> BrokerageUserQueryFilters:
        return BrokerageUserQueryFilters(
            **self.model_dump(exclude={"page_no", "page_size", "user_role"}, exclude_unset=True),
            brokerage_id=brokerage_id,
            role=_USER_ROLE_TO_ROLE_NAME_MAPPINGS[self.user_role],
        )


class BrokerageUserSummaryInListResponse(WithId, AuditMixin):
    name: str
    phone: str

    brokerage_user_: BrokerageUserRichInfoCompositeModel = Field(exclude=True)

    @computed_field
    def email(self) -> str | EmailStr:
        return _email(self.brokerage_user_.user)

    @computed_field
    def user_role(self) -> BrokerageUserRoleEnum:
        return _user_role(self.brokerage_user_.user)

    @computed_field
    def enabled(self) -> bool:
        return _enabled(self.brokerage_user_.user)

    @computed_field
    def broker_id(self) -> int | None:
        return self.brokerage_user_.broker_id

    @classmethod
    def from_model(cls, model: BrokerageUserRichInfoCompositeModel) -> "BrokerageUserSummaryInListResponse":
        return cls(**model.model_dump() | {"brokerage_user_": model})

    @classmethod
    def from_models(cls, models: list[BrokerageUserModel]) -> list["BrokerageUserSummaryInListResponse"]:
        return [cls.from_model(brokerage_user) for brokerage_user in models]


class BrokerageUserChangePasswordRequestBase(BaseModel):
    origin_password: str
    new_password: str


class BrokerageUserMeChangePasswordRequest(BrokerageUserChangePasswordRequestBase):
    pass


class BrokerageUserBasicChangePasswordRequest(BrokerageUserChangePasswordRequestBase):
    email: str


class BrokerageUserResetPasswordRequest(BaseModel):
    email: str
    new_password: str
    verification_code: str


class BrokerageUserBatchEnabledUpdatingRequest(BaseModel):
    """
    批量更新人员启用状态请求
    """

    ids: set[int]
    enabled: bool

from datetime import date

from pydantic import BaseModel
from pydantic import computed_field
from pydantic import EmailStr
from pydantic import field_validator
from pydantic import model_validator

from bethune.api.dto import PageParams
from bethune.api.dto.base import AuditMixin
from bethune.api.dto.base import GenderEnum
from bethune.model import Customer as CustomerModel
from bethune.util.date import get_current_date as current_date


def _replace_year_safely(from_date: date, year: int) -> date:
    """Replace the year of a date object safely."""
    try:
        return from_date.replace(year=year)
    except ValueError:
        # Handle leap year case
        return from_date.replace(year=year, day=from_date.day - 1)


class TagsHelper:
    @staticmethod
    def set_to_str(tags: set[str] | None) -> str:
        return f'|{"|".join(sorted(tags))}|' if tags else ""

    @staticmethod
    def str_to_set(tags: str | None) -> set[str]:
        return set(filter(None, tags.split("|"))) if tags else set()

    @staticmethod
    def validate(tags: set[str] | None) -> set[str] | None:
        if tags and any("|" in tag for tag in tags):
            raise ValueError("Tags should not contain '|' character")
        return tags


class CustomerBase(BaseModel):
    name: str
    gender: GenderEnum | None = None
    address: str | None = None
    email: str
    province: str | None = None
    city: str | None = None
    postal_code: str | None = None
    phone: str | None = None
    birthday: date | None = None
    move_in_date: date | None = None
    tags: set[str] | None = None
    wechat: str | None = None
    memo: str | None = None
    referer_name: str | None = None
    referer_phone: str | None = None
    referer_email: EmailStr | None = None

    @field_validator("tags")
    @classmethod
    def tags_validator(cls, tags: set[str] | None):
        return TagsHelper.validate(tags)


class Customer(CustomerBase):
    id: int
    broker_id: int

    @classmethod
    def from_model(cls, model: CustomerModel):
        return cls(
            **model.model_dump(exclude={"tags"}),
            tags=TagsHelper.str_to_set(model.tags),
            referer_name=model.referer.name if model.referer else None,
            referer_phone=model.referer.phone if model.referer else None,
            referer_email=model.referer.user.email if model.referer else None,
        )


class CustomerCreate(CustomerBase):
    broker_id: int | None = None
    insurance_consultation_id: int | None = None

    def to_model(self):
        return CustomerModel(
            **self.model_dump(exclude_unset=True, exclude={"email", "tags", "insurance_consultation_id"}),
            email=self.email.strip().lower(),
            tags=TagsHelper.set_to_str(self.tags),
        )


class CustomerUpdate(BaseModel):

    name: str | None = None
    gender: GenderEnum | None = None
    province: str | None = None
    city: str | None = None
    postal_code: str | None = None
    address: str | None = None
    email: EmailStr | None = None
    phone: str | None = None
    birthday: date | None = None
    move_in_date: date | None = None
    tags: set[str] | None = None
    wechat: str | None = None
    memo: str | None = None

    @field_validator("tags")
    @classmethod
    def tags_validator(cls, tags: set[str] | None) -> set[str] | None:
        return TagsHelper.validate(tags)

    def to_model(self, id: int):
        model = CustomerModel(
            **self.model_dump(exclude={"broker_id", "tags", "email"}, exclude_unset=True),
            email=self.email.strip().lower() if self.email else self.email,
        )
        model.id = id
        if "tags" in self.model_fields_set:
            model.tags = TagsHelper.set_to_str(self.tags)
        return model


class CustomerListQueryParams(PageParams):
    name: str | None = None
    gender: GenderEnum | None = None
    phone: str | None = None
    province: str | None = None
    city: str | None = None
    address: str | None = None
    birthday_in_days: int | None = None
    expire_in_days: int | None = None
    keyword: str | None = None
    tag: str | None = None

    @model_validator(mode="after")
    def check_mutux_fields(self) -> "CustomerListQueryParams":
        if self.expire_in_days and self.model_fields_set.difference({"expire_in_days", "page_no", "page_size"}):
            raise ValueError("[expire_in_days] can not be used with other fields")
        return self


class CustomerList(CustomerBase, AuditMixin):
    id: int
    broker_id: int
    end_date: date | None = None

    @computed_field
    def days_until_birthday(self) -> int | None:
        if self.birthday is None:
            return None
        today = current_date()
        next_birthday = _replace_year_safely(self.birthday, today.year)
        if next_birthday < today:
            next_birthday = _replace_year_safely(self.birthday, today.year + 1)
        return (next_birthday - today).days

    @computed_field
    def days_until_expired(self) -> int | None:
        if self.end_date is None:
            return None
        if self.end_date < current_date():
            return 0
        return (self.end_date - current_date()).days

    @classmethod
    def from_model(cls, model: CustomerModel, end_date: date | None = None) -> "CustomerList":
        return cls(
            **model.model_dump(exclude={"tags"}),
            tags=TagsHelper.str_to_set(model.tags),
            end_date=end_date,
            referer_name=model.referer.name if model.referer else None,
            referer_phone=model.referer.phone if model.referer else None,
            referer_email=model.referer.user.email if model.referer else None,
        )

    @classmethod
    def from_models(cls, models: list[CustomerModel] | list[tuple[CustomerModel, date]]) -> list["CustomerList"]:
        if not models:
            return []
        if isinstance(models[0], CustomerModel):
            return [cls.from_model(model) for model in models]  # type: ignore
        return [cls.from_model(model[0], model[1]) for model in models]

from typing import Any

from pydantic import BaseModel
from pydantic import computed_field
from pydantic import ConfigDict
from pydantic import Field

from bethune.util import get_text


class Item(BaseModel):
    code: str
    description_: str = Field(exclude=True)

    @computed_field
    def description(self) -> str:
        return get_text(self.description_)

    model_config = ConfigDict(
        frozen=True,
        extra="allow",
        from_attributes=True,
    )


class BaseLookup:
    items: list[Item] = []

    class Config:
        from_attributes = True

    @classmethod
    def add_item(cls, item: Item):
        cls.items.append(item)

    @classmethod
    def lookup(cls, key: str, key_field: str, prefabricated_items=None) -> Item | None:
        candidate_items = prefabricated_items or cls.items
        return next((item for item in candidate_items if getattr(item, key_field) == key), None)

    @classmethod
    def initialize_from_dict(cls, data_list: list[dict[str, Any]], model: type[Item]):
        for data in data_list:
            item = model(**data)
            cls.add_item(item)

from enum import StrEnum

from .city.bc import CityLookup as BCCityLookup
from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item


class ProvinceEnum(StrEnum):
    ab = "AB"
    bc = "BC"
    mb = "MB"
    nb = "NB"
    nl = "NL"
    nt = "NT"
    ns = "NS"
    nu = "NU"
    on = "ON"
    pei = "PEI"
    qc = "QC"
    sk = "SK"
    yk = "YK"
    fed = "FED"


class ProvinceLookup(BaseLookup):
    items: list[Item] = []


ProvinceLookup.initialize_from_dict(
    [
        {
            "code": ProvinceEnum.fed,
            "description_": "ProvinceEnum_fed",
            "province_cities": {ProvinceEnum.bc: BCCityLookup.items},
        },
        # {"code": ProvinceEnum.ab, "description_": "Alberta"},
        {"code": ProvinceEnum.bc, "description_": "ProvinceEnum_bc", "cities": BCCityLookup.items},
        # {"code": ProvinceEnum.mb, "description_": "ProvinceEnum_mb"},
        # {"code": ProvinceEnum.nb, "description_": "ProvinceEnum_nb"},
        # {"code": ProvinceEnum.nl, "description_": "ProvinceEnum_nl},
        # {"code": ProvinceEnum.nt, "description_": "ProvinceEnum_nt"},
        # {"code": ProvinceEnum.ns, "description_": "ProvinceEnum_ns"},
        # {"code": ProvinceEnum.nu, "description_": "ProvinceEnum_nu"},
        # {"code": ProvinceEnum.on, "description_": "ProvinceEnum_on"},
        # {"code": ProvinceEnum.pei, "description_": "ProvinceEnum_pei"},
        # {"code": ProvinceEnum.qc, "description_": "ProvinceEnum_qc"},
        # {"code": ProvinceEnum.sk, "description_": "ProvinceEnum_sk"},
        # {"code": ProvinceEnum.yk, "description_": "ProvinceEnum_yk"},
    ],
    Item,
)

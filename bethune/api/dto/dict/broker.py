from enum import StrEnum

from bethune.api.dto.base import InsuranceConsultationSource
from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item
from bethune.model.base import InsuranceType
from bethune.model.user_feedback import FeedbackTypeEnum


class PublicFieldEnum(StrEnum):
    name = "NAME"
    email = "EMAIL"
    phone = "PHONE"


class PublicFieldLookup(BaseLookup):
    items: list[Item] = []


PublicFieldLookup.initialize_from_dict(
    [
        {"code": PublicFieldEnum.name, "description_": "Name"},
        {"code": PublicFieldEnum.email, "description_": "Email"},
        {"code": PublicFieldEnum.phone, "description_": "Phone"},
    ],
    Item,
)


class FeedbackTypeLookup(BaseLookup):
    items: list[Item] = []


FeedbackTypeLookup.initialize_from_dict(
    [
        {"code": FeedbackTypeEnum.FEATURE_SUGGESTION, "description_": "Feature Suggestion"},
        {"code": FeedbackTypeEnum.USAGE_ISSUE, "description_": "Usage Issue"},
        {"code": FeedbackTypeEnum.INTERFACE_OPTIMIZATION, "description_": "Interface Optimization"},
        {"code": FeedbackTypeEnum.CONTENT_ERROR, "description_": "Content Error"},
        {"code": FeedbackTypeEnum.OTHER, "description_": "Other"},
    ],
    Item,
)


class InsuranceTypeLookup(BaseLookup):
    items: list[Item] = []


InsuranceTypeLookup.initialize_from_dict(
    [
        {"code": InsuranceType.HOUSE_INSURANCE, "description_": "House"},
        {"code": InsuranceType.RENTERS_INSURANCE, "description_": "Renters"},
        {"code": InsuranceType.AUTO_INSURANCE, "description_": "Auto"},
        {"code": InsuranceType.LIFE_INSURANCE, "description_": "Life"},
    ],
    Item,
)


class InsuranceConsultationSourceLookup(BaseLookup):
    items: list[Item] = []


InsuranceConsultationSourceLookup.initialize_from_dict(
    [
        {"code": InsuranceConsultationSource.ARTICLE, "description_": "Article"},
        {"code": InsuranceConsultationSource.OFFLINE_PROMOTION_MATERIAL, "description_": "Offline Promotion Material"},
        {"code": InsuranceConsultationSource.MEDIA_PROMOTION_MATERIAL, "description_": "Media Promotion Material"},
    ],
    Item,
)

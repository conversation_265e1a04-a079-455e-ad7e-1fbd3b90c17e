# 经纪行用户角色
from enum import StrEnum

from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item


class BrokerageUserRoleEnum(StrEnum):
    BROKER = "BROKER"
    SUPPORT = "SUPPORT"
    ADMIN = "ADMIN"
    UNKNOWN = "UNKNOWN"


class BrokerageUserRoleLookup(BaseLookup):
    items: list[Item] = []


BrokerageUserRoleLookup.initialize_from_dict(
    [
        # {"code": BrokerageUserRoleEnum.BROKER, "description_": "BrokerageUserRoleEnum_broker"},
        {"code": BrokerageUserRoleEnum.SUPPORT, "description_": "BrokerageUserRoleEnum_support"},
    ],
    Item,
)

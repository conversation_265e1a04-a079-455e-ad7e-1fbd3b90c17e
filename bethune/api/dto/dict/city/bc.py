from enum import StrEnum

from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item


class CityEnum(StrEnum):
    vi = "VI"
    va = "VA"
    ke = "KE"
    pg = "PG"
    ab = "AB"
    ka = "KA"
    ri = "RI"
    su = "SU"
    bu = "BU"
    nv = "NV"
    wv = "WV"
    wr = "WR"
    la = "LA"
    de = "DE"
    nw = "NW"
    co = "CO"
    pm = "PM"
    na = "NA"
    pe = "PE"
    ve = "VE"


class CityLookup(BaseLookup):
    items: list[Item] = []


CityLookup.initialize_from_dict(
    [
        {"code": CityEnum.ab, "description_": "BCCityEnum_ab"},
        {"code": CityEnum.bu, "description_": "BCCityEnum_bu"},
        {"code": CityEnum.co, "description_": "BCCityEnum_co"},
        {"code": CityEnum.de, "description_": "BCCityEnum_de"},
        {"code": CityEnum.ka, "description_": "BCCityEnum_ka"},
        {"code": CityEnum.ke, "description_": "BCCityEnum_ke"},
        {"code": CityEnum.la, "description_": "BCCityEnum_la"},
        {"code": CityEnum.na, "description_": "BCCityEnum_na"},
        {"code": CityEnum.nv, "description_": "BCCityEnum_nv"},
        {"code": CityEnum.nw, "description_": "BCCityEnum_nw"},
        {"code": CityEnum.pe, "description_": "BCCityEnum_pe"},
        {"code": CityEnum.pg, "description_": "BCCityEnum_pg"},
        {"code": CityEnum.pm, "description_": "BCCityEnum_pm"},
        {"code": CityEnum.ri, "description_": "BCCityEnum_ri"},
        {"code": CityEnum.su, "description_": "BCCityEnum_su"},
        {"code": CityEnum.va, "description_": "BCCityEnum_va"},
        {"code": CityEnum.ve, "description_": "BCCityEnum_ve"},
        {"code": CityEnum.vi, "description_": "BCCityEnum_vi"},
        {"code": CityEnum.wr, "description_": "BCCityEnum_wr"},
        {"code": CityEnum.wv, "description_": "BCCityEnum_wv"},
    ],
    Item,
)

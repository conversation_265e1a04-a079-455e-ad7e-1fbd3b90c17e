from enum import StrEnum

from bethune.api.dto.base import GenderEnum
from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item


class GenderLookup(BaseLookup):
    items: list[Item] = []


GenderLookup.initialize_from_dict(
    [
        {"code": GenderEnum.MALE, "description_": "GenderEnum_male"},
        {"code": GenderEnum.FEMALE, "description_": "GenderEnum_female"},
        {"code": GenderEnum.OTHER, "description_": "Common_other"},
    ],
    Item,
)


# 索赔次数
class NumberOfClaimsEnum(StrEnum):
    zero = "zero"
    one = "one"
    twoplus = "twoplus"


class NumberOfClaimsLookup(BaseLookup):
    items: list[Item] = []


NumberOfClaimsLookup.initialize_from_dict(
    [
        {"code": NumberOfClaimsEnum.zero, "description_": "NumberOfClaimsEnum_zero"},
        {"code": NumberOfClaimsEnum.one, "description_": "NumberOfClaimsEnum_one"},
        {"code": NumberOfClaimsEnum.twoplus, "description_": "NumberOfClaimsEnum_twoplus"},
    ],
    Item,
)


class MaritalStatusEnum(StrEnum):
    divorced = "DIVORCED"
    common_law = "COMMON_LAW"
    married = "MARRIED"
    separated = "SEPARATED"
    single = "SINGLE"
    widowed = "WIDOWED"


class MaritalStatusLookup(BaseLookup):
    items: list[Item] = []


MaritalStatusLookup.initialize_from_dict(
    [
        {"code": MaritalStatusEnum.divorced, "description_": "MaritalStatusEnum_Divorced", "deemed_single": True},
        # {"code": MaritalStatusEnum.common_law, "description_": "MaritalStatusEnum_LivingCommonLaw", "deemed_single": False},
        {"code": MaritalStatusEnum.married, "description_": "MaritalStatusEnum_Married", "deemed_single": False},
        # {"code": MaritalStatusEnum.separated, "description_": "MaritalStatusEnum_Separated", "deemed_single": True},
        {"code": MaritalStatusEnum.single, "description_": "MaritalStatusEnum_Single", "deemed_single": True},
        # {"code": MaritalStatusEnum.widowed, "description_": "MaritalStatusEnum_Widowed", "deemed_single": True},
    ],
    Item,
)

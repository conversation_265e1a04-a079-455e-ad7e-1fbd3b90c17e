from datetime import datetime
from enum import StrEnum
from typing import Any
from zoneinfo import ZoneInfo

from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item
from bethune.util import wiith


# 房屋类型
class HouseTypeEnum(StrEnum):
    fdh = "FDH"
    occ = "OCC"
    rental = "RENTAL"


class HouseTypeLookup(BaseLookup):
    items: list[Item] = []


HouseTypeLookup.initialize_from_dict(
    [
        {"code": HouseTypeEnum.fdh, "description_": "Freehold Detached House"},
        {"code": HouseTypeEnum.occ, "description_": "Owner-Occupied Condo"},
        {"code": HouseTypeEnum.rental, "description_": "Rental"},
    ],
    Item,
)


# 占用状态
class OccupancyStatusEnum(StrEnum):
    oc = "OC"
    to = "TO"
    vu = "VU"
    sr = "SR"
    imf = "IMF"
    other = "OTHER"


class OccupancyStatusLookup(BaseLookup):
    items: list[Item] = []


OccupancyStatusLookup.initialize_from_dict(
    [
        {"code": OccupancyStatusEnum.oc, "description_": "Owner-Occupied"},
        {"code": OccupancyStatusEnum.to, "description_": "Tenant-Occupied"},
        {"code": OccupancyStatusEnum.vu, "description_": "Vacation Use"},
        {"code": OccupancyStatusEnum.sr, "description_": "Secondary Residence"},
        {"code": OccupancyStatusEnum.vu, "description_": "Immediate Family"},
        {"code": OccupancyStatusEnum.other, "description_": "Other"},
    ],
    Item,
)


# 主要热源
class PrimaryHeatingSourceEnum(StrEnum):
    ngf = "NGF"
    eb = "EB"
    fireplace = "FIREPLACE"
    ph = "PH"


class PrimaryHeatingSourceLookup(BaseLookup):
    items: list[Item] = []


PrimaryHeatingSourceLookup.initialize_from_dict(
    [
        {"code": PrimaryHeatingSourceEnum.ngf, "description_": "Natural Gas Furnace"},
        {"code": PrimaryHeatingSourceEnum.eb, "description_": "Electric Baseboard"},
        {"code": PrimaryHeatingSourceEnum.fireplace, "description_": "Fireplace"},
        {"code": PrimaryHeatingSourceEnum.ph, "description_": "Portable Heater"},
    ],
    Item,
)


# 电线类型
class WiringTypeEnum(StrEnum):
    al = "AL"
    co = "CO"
    kt = "KT"
    custom = "CUSTOM"


class WiringTypeLookup(BaseLookup):
    items: list[Item] = []


WiringTypeLookup.initialize_from_dict(
    [
        {"code": WiringTypeEnum.al, "description_": "Aluminum Wiring"},
        {"code": WiringTypeEnum.co, "description_": "Copper Wiring"},
        {"code": WiringTypeEnum.kt, "description_": "Knob-and-Tube"},
        {"code": WiringTypeEnum.custom, "description_": "Custom"},
    ],
    Item,
)


# 屋顶材料
class RoofingMaterialEnum(StrEnum):
    assh = "ASSH"
    ct = "CT"
    cs = "CS"
    ms = "MS"
    elastomeric = "ELASTOMERIC"
    rubber = "RUBBER"
    st = "ST"
    tg = "TG"
    wsha = "WSHA"
    wshi = "WSHI"
    other = "OTHER"


class RoofingMaterialLookup(BaseLookup):
    items: list[Item] = []


RoofingMaterialLookup.initialize_from_dict(
    [
        {"code": RoofingMaterialEnum.assh, "description_": "Asphalt Shingles"},
        {"code": RoofingMaterialEnum.ct, "description_": "Clay Tiles"},
        {"code": RoofingMaterialEnum.cs, "description_": "Corrugated Steel"},
        {"code": RoofingMaterialEnum.ms, "description_": "Metal Shingles"},
        {"code": RoofingMaterialEnum.elastomeric, "description_": "Elastomeric"},
        {"code": RoofingMaterialEnum.rubber, "description_": "Rubber"},
        {"code": RoofingMaterialEnum.st, "description_": "Slate Tiles"},
        {"code": RoofingMaterialEnum.tg, "description_": "Tar and Gravel"},
        {"code": RoofingMaterialEnum.wsha, "description_": "Wood Shakes"},
        {"code": RoofingMaterialEnum.wshi, "description_": "Wood Shingles"},
        {"code": RoofingMaterialEnum.other, "description_": "Other"},
    ],
    Item,
)


# 电压
class VoltageEnum(StrEnum):
    amp200 = "AMP200"
    amp100 = "AMP100"
    amp50 = "AMP50"


class VoltageLookup(BaseLookup):
    items: list[Item] = []


VoltageLookup.initialize_from_dict(
    [
        {"code": VoltageEnum.amp200, "description_": "200 AMP"},
        {"code": VoltageEnum.amp100, "description_": "100 AMP"},
        {"code": VoltageEnum.amp50, "description_": "50 AMP"},
    ],
    Item,
)


# 水管类型
class PlumbingPipeTypeEnum(StrEnum):
    co = "CO"
    ci = "CI"
    ga = "GA"
    abs = "ABS"
    pex = "PEX"
    pvc = "PVC"
    mca = "MCA"
    mcp = "MCP"


class PlumbingPipeTypeLookup(BaseLookup):
    items: list[Item] = []


PlumbingPipeTypeLookup.initialize_from_dict(
    [
        {"code": PlumbingPipeTypeEnum.co, "description_": "Copper"},
        {"code": PlumbingPipeTypeEnum.ci, "description_": "Cast Iron"},
        {"code": PlumbingPipeTypeEnum.ga, "description_": "Galvanized"},
        {"code": PlumbingPipeTypeEnum.abs, "description_": "ABS"},
        {"code": PlumbingPipeTypeEnum.pex, "description_": "PEX"},
        {"code": PlumbingPipeTypeEnum.pvc, "description_": "PVC"},
        {"code": PlumbingPipeTypeEnum.mca, "description_": "Mixed - Copper/ABS"},
        {"code": PlumbingPipeTypeEnum.mcp, "description_": "Mixed - Copper/PVC"},
    ],
    Item,
)


# 建筑材料
class BuildingMaterialEnum(StrEnum):
    fp = "FP"
    wf = "WF"
    log = "LOG"
    custom = "CUSTOM"


class BuildingMaterialLookup(BaseLookup):
    items: list[Item] = []


BuildingMaterialLookup.initialize_from_dict(
    [
        {"code": BuildingMaterialEnum.fp, "description_": "Fireproof"},
        {"code": BuildingMaterialEnum.wf, "description_": "Wood Frame"},
        {"code": BuildingMaterialEnum.log, "description_": "Log"},
        {"code": BuildingMaterialEnum.custom, "description_": "Custom"},
    ],
    Item,
)


# 结构
class StructureEnum(StrEnum):
    high = "HIGH"
    hcb = "HCB"
    low = "LOW"
    lcb = "LCB"
    townhouse = "TRIPLEX"
    duplex = "DUPLEX"
    triplex = "TRIPLEX"


class StructureLookup(BaseLookup):
    items: list[Item] = []


StructureLookup.initialize_from_dict(
    [
        {"code": StructureEnum.high, "description_": "High-rise"},
        {"code": StructureEnum.hcb, "description_": "High-rise with Commercial Units below"},
        {"code": StructureEnum.low, "description_": "Low-rise"},
        {"code": StructureEnum.lcb, "description_": "Low-rise with Commercial Units below"},
        {"code": StructureEnum.townhouse, "description_": "Townhouse"},
        {"code": StructureEnum.duplex, "description_": "Duplex"},
        {"code": StructureEnum.triplex, "description_": "Triplex"},
    ],
    Item,
)


# 结构2
class StructureEnum2(StrEnum):
    bs = "BS"
    hdt = "HDT"
    low = "LOW"
    high = "HIGH"


class StructureLookup2(BaseLookup):
    items: list[Item] = []


StructureLookup2.initialize_from_dict(
    [
        {"code": StructureEnum2.bs, "description_": "StructureEnum2_bs"},
        {"code": StructureEnum2.hdt, "description_": "StructureEnum2_hdt"},
        {"code": StructureEnum2.low, "description_": "StructureEnum2_low"},
        {"code": StructureEnum2.high, "description_": "StructureEnum2_high"},
    ],
    Item,
)


# 供暖材料
class HeatingFuelEnum(StrEnum):
    cfng = "CFNG"
    cfp = "CFP"
    eb = "EB"
    fireplace = "FIREPLACE"
    sfh = "SFH"
    sh = "SH"
    custom = "CUSTOM"


class HeatingFuelLookup(BaseLookup):
    items: list[Item] = []


HeatingFuelLookup.initialize_from_dict(
    [
        {"code": HeatingFuelEnum.cfng, "description_": "Central Furnace Natural Gas"},
        {"code": HeatingFuelEnum.cfp, "description_": "Central Furnace Propane"},
        {"code": HeatingFuelEnum.eb, "description_": "Electric Baseboard"},
        {"code": HeatingFuelEnum.fireplace, "description_": "Fireplace"},
        {"code": HeatingFuelEnum.sfh, "description_": "Solid Fuel Heater"},
        {"code": HeatingFuelEnum.sh, "description_": "Space Heater"},
        {"code": HeatingFuelEnum.custom, "description_": "Custom"},
    ],
    Item,
)


# 防盗报警器
class SecurityAlarmEnum(StrEnum):
    fc = "FC"
    pc = "PC"
    none = "NONE"


class SecurityAlarmLookup(BaseLookup):
    items: list[Item] = []


SecurityAlarmLookup.initialize_from_dict(
    [
        {"code": SecurityAlarmEnum.fc, "description_": "Fully Covered"},
        {"code": SecurityAlarmEnum.pc, "description_": "Partially Covered"},
        {"code": SecurityAlarmEnum.none, "description_": "None"},
    ],
    Item,
)


def _items_with_custom(raw_items: list[Any], additional_items: list[Any] = []) -> list[Item]:
    return (
        [Item(code=str(item), description_=str(item)) for item in raw_items]
        + additional_items
        + [Item(code="CUSTOM", description_="Custom")]
    )


def _recent_years() -> list[int]:
    return wiith(
        datetime.now(ZoneInfo("America/Vancouver")).date().year,
        lambda current_year: [current_year - i for i in range(4)],
    )


# 通用数量
class GeneralQuantityLookup:

    @classmethod
    def items(cls) -> list[Item]:
        return _items_with_custom(list(range(1, 5)))


# 通用年份
class GeneralYearLookup:

    @classmethod
    def items(cls) -> list[Item]:
        return _items_with_custom(_recent_years())


# 通用年份（带着Never选项）
class GeneralYearWithNeverOptionLookup:

    @classmethod
    def items(cls) -> list[Item]:
        return _items_with_custom(
            _recent_years(),
            [Item(code="NONE", description_="None")],
        )


# 投保保额
class SumInsuredLookup:

    @classmethod
    def items(cls) -> list[Item]:
        return [
            Item(code="10000", description_="10000", recommended=False),
            Item(code="20000", description_="20000", recommended=True),
            Item(code="30000", description_="30000", recommended=False),
            Item(code="40000", description_="40000", recommended=False),
            Item(code="50000", description_="50000", recommended=False),
            Item(code="CUSTOM", description_="Custom", recommended=False),
        ]
        # return _items_with_custom([10000, 20000, 30000, 40000, 50000])

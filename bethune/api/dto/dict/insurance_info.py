from datetime import datetime
from enum import StrEnum
from typing import Any
from zoneinfo import ZoneInfo

from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item
from bethune.util import wiith


# ============================================================================
# 保险信息相关定义 (Insurance Info)
# ============================================================================


class InsuranceLifeTypeEnum(StrEnum):
    FAMILY_FINANCIAL_PLANNING = "FAMILY_FINANCIAL_PLANNING"
    RETIREMENT_TAX_PLANNING = "RETIREMENT_TAX_PLANNING"
    ASSET_INHERITANCE_PLANNING = "ASSET_INHERITANCE_PLANNING"
    SME_FINANCIAL_TAX_PLANNING = "SME_FINANCIAL_TAX_PLANNING"


class InsuranceLifeTypeLookup(BaseLookup):
    items: list[Item] = []


InsuranceLifeTypeLookup.initialize_from_dict(
    [
        {
            "code": InsuranceLifeTypeEnum.FAMILY_FINANCIAL_PLANNING,
            "description_": "LifeTypeEnum_FamilyFinancialPlanning",
        },
        {"code": InsuranceLifeTypeEnum.RETIREMENT_TAX_PLANNING, "description_": "LifeTypeEnum_RetirementTaxPlanning"},
        {
            "code": InsuranceLifeTypeEnum.ASSET_INHERITANCE_PLANNING,
            "description_": "LifeTypeEnum_AssetInheritancePlanning",
        },
        {
            "code": InsuranceLifeTypeEnum.SME_FINANCIAL_TAX_PLANNING,
            "description_": "LifeTypeEnum_SmeFinancialTaxPlanning",
        },
    ],
    Item,
)


# ============================================================================
# 车险相关定义 (Vehicle Insurance)
# ============================================================================


# 司机熟练度
class VehicleDriverProficiencyEnum(StrEnum):
    MAIN = "MAIN"
    OCCASIONAL = "OCCASIONAL"
    NO = "No"


class VehicleDriverProficiencyLookup(BaseLookup):
    items: list[Item] = []


VehicleDriverProficiencyLookup.initialize_from_dict(
    [
        {"code": VehicleDriverProficiencyEnum.MAIN, "description_": "DriverProficiencyEnum_Main"},
        {"code": VehicleDriverProficiencyEnum.OCCASIONAL, "description_": "DriverProficiencyEnum_Occasional"},
        {"code": VehicleDriverProficiencyEnum.NO, "description_": "Common_No"},
    ],
    Item,
)


# 驾照类型
class VehicleDriverLicenseTypeEnum(StrEnum):
    CLASS7 = "CLASS7"
    NEWBIE = "NEWBIE"
    CLASS5 = "CLASS5"
    CUSTOM = "CUSTOM"


class VehicleDriverLicenseTypeLookup(BaseLookup):
    items: list[Item] = []


VehicleDriverLicenseTypeLookup.initialize_from_dict(
    [
        {"code": VehicleDriverLicenseTypeEnum.CLASS7, "description_": "DriverLicenseTypeEnum_Class7"},
        {"code": VehicleDriverLicenseTypeEnum.NEWBIE, "description_": "DriverLicenseTypeEnum_Newbie"},
        {"code": VehicleDriverLicenseTypeEnum.CLASS5, "description_": "DriverLicenseTypeEnum_Class5"},
        {"code": VehicleDriverLicenseTypeEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 驾照状态
class VehicleDriverLicenseStatusEnum(StrEnum):
    ACTIVE = "ACTIVE"
    SUSPENDED = "SUSPENDED"
    CUSTOM = "CUSTOM"


class VehicleDriverLicenseStatusLookup(BaseLookup):
    items: list[Item] = []


VehicleDriverLicenseStatusLookup.initialize_from_dict(
    [
        {"code": VehicleDriverLicenseStatusEnum.ACTIVE, "description_": "Common_Active"},
        {"code": VehicleDriverLicenseStatusEnum.SUSPENDED, "description_": "Common_Suspended"},
        {"code": VehicleDriverLicenseStatusEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 司机关系
class VehicleDriverRelationshipEnum(StrEnum):
    SPOUSE = "SPOUSE"
    CHILD = "CHILD"
    PARENT = "PARENT"
    ROOMMATE = "ROOMMATE"
    EMPLOYEE = "EMPLOYEE"
    CUSTOM = "CUSTOM"


class VehicleDriverRelationshipLookup(BaseLookup):
    items: list[Item] = []


VehicleDriverRelationshipLookup.initialize_from_dict(
    [
        {"code": VehicleDriverRelationshipEnum.SPOUSE, "description_": "DriverRelationshipEnum_Spouse"},
        {"code": VehicleDriverRelationshipEnum.CHILD, "description_": "DriverRelationshipEnum_Child"},
        {"code": VehicleDriverRelationshipEnum.PARENT, "description_": "DriverRelationshipEnum_Parent"},
        {"code": VehicleDriverRelationshipEnum.ROOMMATE, "description_": "DriverRelationshipEnum_Roommate"},
        {"code": VehicleDriverRelationshipEnum.EMPLOYEE, "description_": "DriverRelationshipEnum_Employee"},
        {"code": VehicleDriverRelationshipEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 车辆拥有权状态
class VehicleOwnershipStatusEnum(StrEnum):
    LEASE = "LEASE"
    FINANCE = "FINANCE"
    FULLY_OWNED = "FULLY_OWNED"


class VehicleOwnershipStatusLookup(BaseLookup):
    items: list[Item] = []


VehicleOwnershipStatusLookup.initialize_from_dict(
    [
        {"code": VehicleOwnershipStatusEnum.LEASE, "description_": "VehicleOwnershipStatusEnum_Lease"},
        {"code": VehicleOwnershipStatusEnum.FINANCE, "description_": "VehicleOwnershipStatusEnum_Finance"},
        {"code": VehicleOwnershipStatusEnum.FULLY_OWNED, "description_": "VehicleOwnershipStatusEnum_FullyOwned"},
    ],
    Item,
)


# 车辆年度里程数
class VehicleYearlyMileageEnum(StrEnum):
    LESS_2500 = "LESS_2500"
    LESS_5000 = "LESS_5000"
    LESS_7500 = "LESS_7500"
    LESS_10000 = "LESS_10000"
    LESS_15000 = "LESS_15000"
    LESS_20000 = "LESS_20000"
    LESS_25000 = "LESS_25000"
    LESS_30000 = "LESS_30000"
    LESS_40000 = "LESS_40000"
    GREATER_40000 = "GREATER_40000"


class VehicleYearlyMileageLookup(BaseLookup):
    items: list[Item] = []


VehicleYearlyMileageLookup.initialize_from_dict(
    [
        {"code": VehicleYearlyMileageEnum.LESS_2500, "description_": "VehicleYearlyMileageEnum_Less2500"},
        {"code": VehicleYearlyMileageEnum.LESS_5000, "description_": "VehicleYearlyMileageEnum_Less5000"},
        {"code": VehicleYearlyMileageEnum.LESS_7500, "description_": "VehicleYearlyMileageEnum_Less7500"},
        {"code": VehicleYearlyMileageEnum.LESS_10000, "description_": "VehicleYearlyMileageEnum_Less10000"},
        {"code": VehicleYearlyMileageEnum.LESS_15000, "description_": "VehicleYearlyMileageEnum_Less15000"},
        {"code": VehicleYearlyMileageEnum.LESS_20000, "description_": "VehicleYearlyMileageEnum_Less20000"},
        {"code": VehicleYearlyMileageEnum.LESS_25000, "description_": "VehicleYearlyMileageEnum_Less25000"},
        {"code": VehicleYearlyMileageEnum.LESS_30000, "description_": "VehicleYearlyMileageEnum_Less30000"},
        {"code": VehicleYearlyMileageEnum.LESS_40000, "description_": "VehicleYearlyMileageEnum_Less40000"},
        {"code": VehicleYearlyMileageEnum.GREATER_40000, "description_": "VehicleYearlyMileageEnum_Greater40000"},
    ],
    Item,
)


# ============================================================================
# 房屋险相关定义 (House Insurance)
# ============================================================================


# 房屋类型
class HouseTypeEnum(StrEnum):
    FDH = "FDH"
    OCC = "OCC"
    RENTAL = "RENTAL"


class HouseTypeLookup(BaseLookup):
    items: list[Item] = []


HouseTypeLookup.initialize_from_dict(
    [
        {"code": HouseTypeEnum.FDH, "description_": "Freehold Detached House"},
        {"code": HouseTypeEnum.OCC, "description_": "Owner-Occupied Condo"},
        {"code": HouseTypeEnum.RENTAL, "description_": "Rental"},
    ],
    Item,
)


# 占用状态
class HouseOccupancyStatusEnum(StrEnum):
    OC = "OC"
    TO = "TO"
    VU = "VU"
    SR = "SR"
    IMF = "IMF"
    OTHER = "OTHER"


class HouseOccupancyStatusLookup(BaseLookup):
    items: list[Item] = []


HouseOccupancyStatusLookup.initialize_from_dict(
    [
        {"code": HouseOccupancyStatusEnum.OC, "description_": "Owner-Occupied"},
        {"code": HouseOccupancyStatusEnum.TO, "description_": "Tenant-Occupied"},
        {"code": HouseOccupancyStatusEnum.VU, "description_": "Vacation Use"},
        {"code": HouseOccupancyStatusEnum.SR, "description_": "Secondary Residence"},
        {"code": HouseOccupancyStatusEnum.IMF, "description_": "Immediate Family"},
        {"code": HouseOccupancyStatusEnum.OTHER, "description_": "Other"},
    ],
    Item,
)


# 主要热源
class HousePrimaryHeatingSourceEnum(StrEnum):
    NGF = "NGF"
    EB = "EB"
    FIREPLACE = "FIREPLACE"
    PH = "PH"


class HousePrimaryHeatingSourceLookup(BaseLookup):
    items: list[Item] = []


HousePrimaryHeatingSourceLookup.initialize_from_dict(
    [
        {"code": HousePrimaryHeatingSourceEnum.NGF, "description_": "Natural Gas Furnace"},
        {"code": HousePrimaryHeatingSourceEnum.EB, "description_": "Electric Baseboard"},
        {"code": HousePrimaryHeatingSourceEnum.FIREPLACE, "description_": "Fireplace"},
        {"code": HousePrimaryHeatingSourceEnum.PH, "description_": "Portable Heater"},
    ],
    Item,
)


# 电线类型
class HouseWiringTypeEnum(StrEnum):
    AL = "AL"
    CO = "CO"
    KT = "KT"
    CUSTOM = "CUSTOM"


class HouseWiringTypeLookup(BaseLookup):
    items: list[Item] = []


HouseWiringTypeLookup.initialize_from_dict(
    [
        {"code": HouseWiringTypeEnum.AL, "description_": "Aluminum Wiring"},
        {"code": HouseWiringTypeEnum.CO, "description_": "Copper Wiring"},
        {"code": HouseWiringTypeEnum.KT, "description_": "Knob-and-Tube"},
        {"code": HouseWiringTypeEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 屋顶材料
class HouseRoofingMaterialEnum(StrEnum):
    ASSH = "ASSH"
    CT = "CT"
    CS = "CS"
    MS = "MS"
    ELASTOMERIC = "ELASTOMERIC"
    RUBBER = "RUBBER"
    ST = "ST"
    TG = "TG"
    WSHA = "WSHA"
    WSHI = "WSHI"
    OTHER = "OTHER"


class HouseRoofingMaterialLookup(BaseLookup):
    items: list[Item] = []


HouseRoofingMaterialLookup.initialize_from_dict(
    [
        {"code": HouseRoofingMaterialEnum.ASSH, "description_": "Asphalt Shingles"},
        {"code": HouseRoofingMaterialEnum.CT, "description_": "Clay Tiles"},
        {"code": HouseRoofingMaterialEnum.CS, "description_": "Corrugated Steel"},
        {"code": HouseRoofingMaterialEnum.MS, "description_": "Metal Shingles"},
        {"code": HouseRoofingMaterialEnum.ELASTOMERIC, "description_": "Elastomeric"},
        {"code": HouseRoofingMaterialEnum.RUBBER, "description_": "Rubber"},
        {"code": HouseRoofingMaterialEnum.ST, "description_": "Slate Tiles"},
        {"code": HouseRoofingMaterialEnum.TG, "description_": "Tar and Gravel"},
        {"code": HouseRoofingMaterialEnum.WSHA, "description_": "Wood Shakes"},
        {"code": HouseRoofingMaterialEnum.WSHI, "description_": "Wood Shingles"},
        {"code": HouseRoofingMaterialEnum.OTHER, "description_": "Other"},
    ],
    Item,
)


# 电压
class HouseVoltageEnum(StrEnum):
    AMP200 = "AMP200"
    AMP100 = "AMP100"
    AMP50 = "AMP50"


class HouseVoltageLookup(BaseLookup):
    items: list[Item] = []


HouseVoltageLookup.initialize_from_dict(
    [
        {"code": HouseVoltageEnum.AMP200, "description_": "200 AMP"},
        {"code": HouseVoltageEnum.AMP100, "description_": "100 AMP"},
        {"code": HouseVoltageEnum.AMP50, "description_": "50 AMP"},
    ],
    Item,
)


# 水管类型
class HousePlumbingPipeTypeEnum(StrEnum):
    CO = "CO"
    CI = "CI"
    GA = "GA"
    ABS = "ABS"
    PEX = "PEX"
    PVC = "PVC"
    MCA = "MCA"
    MCP = "MCP"


class HousePlumbingPipeTypeLookup(BaseLookup):
    items: list[Item] = []


HousePlumbingPipeTypeLookup.initialize_from_dict(
    [
        {"code": HousePlumbingPipeTypeEnum.CO, "description_": "Copper"},
        {"code": HousePlumbingPipeTypeEnum.CI, "description_": "Cast Iron"},
        {"code": HousePlumbingPipeTypeEnum.GA, "description_": "Galvanized"},
        {"code": HousePlumbingPipeTypeEnum.ABS, "description_": "ABS"},
        {"code": HousePlumbingPipeTypeEnum.PEX, "description_": "PEX"},
        {"code": HousePlumbingPipeTypeEnum.PVC, "description_": "PVC"},
        {"code": HousePlumbingPipeTypeEnum.MCA, "description_": "Mixed - Copper/ABS"},
        {"code": HousePlumbingPipeTypeEnum.MCP, "description_": "Mixed - Copper/PVC"},
    ],
    Item,
)


# 建筑材料
class HouseBuildingMaterialEnum(StrEnum):
    FP = "FP"
    WF = "WF"
    LOG = "LOG"
    CUSTOM = "CUSTOM"


class HouseBuildingMaterialLookup(BaseLookup):
    items: list[Item] = []


HouseBuildingMaterialLookup.initialize_from_dict(
    [
        {"code": HouseBuildingMaterialEnum.FP, "description_": "Fireproof"},
        {"code": HouseBuildingMaterialEnum.WF, "description_": "Wood Frame"},
        {"code": HouseBuildingMaterialEnum.LOG, "description_": "Log"},
        {"code": HouseBuildingMaterialEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 结构
class HouseStructureEnum(StrEnum):
    HIGH = "HIGH"
    HCB = "HCB"
    LOW = "LOW"
    LCB = "LCB"
    TOWNHOUSE = "TOWNHOUSE"
    DUPLEX = "DUPLEX"
    TRIPLEX = "TRIPLEX"


class HouseStructureLookup(BaseLookup):
    items: list[Item] = []


HouseStructureLookup.initialize_from_dict(
    [
        {"code": HouseStructureEnum.HIGH, "description_": "High-rise"},
        {"code": HouseStructureEnum.HCB, "description_": "High-rise with Commercial Units below"},
        {"code": HouseStructureEnum.LOW, "description_": "Low-rise"},
        {"code": HouseStructureEnum.LCB, "description_": "Low-rise with Commercial Units below"},
        {"code": HouseStructureEnum.TOWNHOUSE, "description_": "Townhouse"},
        {"code": HouseStructureEnum.DUPLEX, "description_": "Duplex"},
        {"code": HouseStructureEnum.TRIPLEX, "description_": "Triplex"},
    ],
    Item,
)


class HouseStructure2Enum(StrEnum):
    BS = "BS"
    HDT = "HDT"
    LOW = "LOW"
    HIGH = "HIGH"


class HouseStructure2Lookup(BaseLookup):
    items: list[Item] = []


HouseStructure2Lookup.initialize_from_dict(
    [
        {"code": HouseStructure2Enum.BS, "description_": "StructureEnum2_bs"},
        {"code": HouseStructure2Enum.HDT, "description_": "StructureEnum2_hdt"},
        {"code": HouseStructure2Enum.LOW, "description_": "StructureEnum2_low"},
        {"code": HouseStructure2Enum.HIGH, "description_": "StructureEnum2_high"},
    ],
    Item,
)


# 供暖材料
class HouseHeatingFuelEnum(StrEnum):
    CFNG = "CFNG"
    CFP = "CFP"
    EB = "EB"
    FIREPLACE = "FIREPLACE"
    SFH = "SFH"
    SH = "SH"
    CUSTOM = "CUSTOM"


class HouseHeatingFuelLookup(BaseLookup):
    items: list[Item] = []


HouseHeatingFuelLookup.initialize_from_dict(
    [
        {"code": HouseHeatingFuelEnum.CFNG, "description_": "Central Furnace Natural Gas"},
        {"code": HouseHeatingFuelEnum.CFP, "description_": "Central Furnace Propane"},
        {"code": HouseHeatingFuelEnum.EB, "description_": "Electric Baseboard"},
        {"code": HouseHeatingFuelEnum.FIREPLACE, "description_": "Fireplace"},
        {"code": HouseHeatingFuelEnum.SFH, "description_": "Solid Fuel Heater"},
        {"code": HouseHeatingFuelEnum.SH, "description_": "Space Heater"},
        {"code": HouseHeatingFuelEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 防盗报警器
class HouseSecurityAlarmEnum(StrEnum):
    FC = "FC"
    PC = "PC"
    NONE = "NONE"


class HouseSecurityAlarmLookup(BaseLookup):
    items: list[Item] = []


HouseSecurityAlarmLookup.initialize_from_dict(
    [
        {"code": HouseSecurityAlarmEnum.FC, "description_": "Fully Covered"},
        {"code": HouseSecurityAlarmEnum.PC, "description_": "Partially Covered"},
        {"code": HouseSecurityAlarmEnum.NONE, "description_": "None"},
    ],
    Item,
)


# ============================================================================
# 房屋险通用工具函数和查找类
# ============================================================================


def _house_items_with_custom(raw_items: list[Any], additional_items: list[Any] = []) -> list[Item]:
    return (
        [Item(code=str(item), description_=str(item)) for item in raw_items]
        + additional_items
        + [Item(code="CUSTOM", description_="Custom")]
    )


def _house_recent_years() -> list[int]:
    return wiith(
        datetime.now(ZoneInfo("America/Vancouver")).date().year,
        lambda current_year: [current_year - i for i in range(4)],
    )


class HouseGeneralQuantityLookup:

    @classmethod
    def items(cls) -> list[Item]:
        return _house_items_with_custom(list(range(1, 5)))


class HouseGeneralYearLookup:

    @classmethod
    def items(cls) -> list[Item]:
        return _house_items_with_custom(_house_recent_years())


class HouseGeneralYearWithNeverOptionLookup:

    @classmethod
    def items(cls) -> list[Item]:
        """获取通用年份（带None选项）选项列表"""
        return _house_items_with_custom(
            _house_recent_years(),
            [Item(code="NONE", description_="None")],
        )


# 投保保额
class HouseSumInsuredLookup:

    @classmethod
    def items(cls) -> list[Item]:
        return [
            Item(code="10000", description_="10000", recommended=False),
            Item(code="20000", description_="20000", recommended=True),
            Item(code="30000", description_="30000", recommended=False),
            Item(code="40000", description_="40000", recommended=False),
            Item(code="50000", description_="50000", recommended=False),
            Item(code="CUSTOM", description_="Custom", recommended=False),
        ]

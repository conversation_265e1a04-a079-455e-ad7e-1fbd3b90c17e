from enum import StrEnum

from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item


class LanguageEnum(StrEnum):
    en = "en"
    fr = "fr"
    zh = "zh"


class SupportedLanguageLookup(BaseLookup):
    items: list[Item] = []


SupportedLanguageLookup.initialize_from_dict(
    [
        {"code": LanguageEnum.zh, "description_": "Chinese"},
        {"code": LanguageEnum.en, "description_": "English"},
        {"code": LanguageEnum.fr, "description_": "French"},
    ],
    Item,
)

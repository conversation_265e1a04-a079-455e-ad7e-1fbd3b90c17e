from enum import StrEnum

from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict import Item


# 司机熟练度
class DriverProficiencyEnum(StrEnum):
    MAIN = "MAIN"
    OCCASIONAL = "OCCASIONAL"
    NO = "No"


class DriverProficiencyLookup(BaseLookup):
    items: list[Item] = []


DriverProficiencyLookup.initialize_from_dict(
    [
        {"code": DriverProficiencyEnum.MAIN, "description_": "DriverProficiencyEnum_Main"},
        {"code": DriverProficiencyEnum.OCCASIONAL, "description_": "DriverProficiencyEnum_Occasional"},
        {"code": DriverProficiencyEnum.NO, "description_": "Common_No"},
    ],
    Item,
)


# 驾照类型
class DriverLicenseTypeEnum(StrEnum):
    CLASS7 = "CLASS7"
    NEWBIE = "NEWBIE"
    CLASS5 = "CLASS5"
    CUSTOM = "CUSTOM"


class DriverLicenseTypeLookup(BaseLookup):
    items: list[Item] = []


DriverLicenseTypeLookup.initialize_from_dict(
    [
        {"code": DriverLicenseTypeEnum.CLASS7, "description_": "DriverLicenseTypeEnum_Class7"},
        {"code": DriverLicenseTypeEnum.NEWBIE, "description_": "DriverLicenseTypeEnum_Newbie"},
        {"code": DriverLicenseTypeEnum.CLASS5, "description_": "DriverLicenseTypeEnum_Class5"},
        {"code": DriverLicenseTypeEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 驾照无效原因
class DriverLicenseStatusEnum(StrEnum):
    ACTIVE = "ACTIVE"
    SUSPENDED = "SUSPENDED"
    CUSTOM = "CUSTOM"


class DriverLicenseStatusLookup(BaseLookup):
    items: list[Item] = []


DriverLicenseStatusLookup.initialize_from_dict(
    [
        {"code": DriverLicenseStatusEnum.ACTIVE, "description_": "Common_Active"},
        {"code": DriverLicenseStatusEnum.SUSPENDED, "description_": "Common_Suspended"},
        {"code": DriverLicenseStatusEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 司机间关系
class DriverRelationshipEnum(StrEnum):
    SPOUSE = "SPOUSE"
    CHILD = "CHILD"
    PARENT = "PARENT"
    ROOMMATE = "ROOMMATE"
    EMPLOYEE = "EMPLOYEE"
    CUSTOM = "CUSTOM"


class DriverRelationshipLookup(BaseLookup):
    items: list[Item] = []


DriverRelationshipLookup.initialize_from_dict(
    [
        {"code": DriverRelationshipEnum.SPOUSE, "description_": "DriverRelationshipEnum_Spouse"},
        {"code": DriverRelationshipEnum.CHILD, "description_": "DriverRelationshipEnum_Child"},
        {"code": DriverRelationshipEnum.PARENT, "description_": "DriverRelationshipEnum_Parent"},
        {"code": DriverRelationshipEnum.ROOMMATE, "description_": "DriverRelationshipEnum_Roommate"},
        {"code": DriverRelationshipEnum.EMPLOYEE, "description_": "DriverRelationshipEnum_Employee"},
        {"code": DriverRelationshipEnum.CUSTOM, "description_": "Custom"},
    ],
    Item,
)


# 车辆拥有权状态
class VehicleOwnershipStatusEnum(StrEnum):
    LEASE = "LEASE"
    FINANCE = "FINANCE"
    FULLY_OWNED = "FULLY_OWNED"


class VehicleOwnershipStatusLookup(BaseLookup):
    items: list[Item] = []


VehicleOwnershipStatusLookup.initialize_from_dict(
    [
        {"code": VehicleOwnershipStatusEnum.LEASE, "description_": "VehicleOwnershipStatusEnum_Lease"},
        {"code": VehicleOwnershipStatusEnum.FINANCE, "description_": "VehicleOwnershipStatusEnum_Finance"},
        {"code": VehicleOwnershipStatusEnum.FULLY_OWNED, "description_": "VehicleOwnershipStatusEnum_FullyOwned"},
    ],
    Item,
)


# 车辆年度里程数
class VehicleYearlyMileageEnum(StrEnum):
    LESS_2500 = "LESS_2500"
    LESS_5000 = "LESS_5000"
    LESS_7500 = "LESS_7500"
    LESS_10000 = "LESS_10000"
    LESS_15000 = "LESS_15000"
    LESS_20000 = "LESS_20000"
    LESS_25000 = "LESS_25000"
    LESS_30000 = "LESS_30000"
    LESS_40000 = "LESS_40000"
    GREATER_40000 = "GREATER_40000"


class VehicleYearlyMileageLookup(BaseLookup):
    items: list[Item] = []


VehicleYearlyMileageLookup.initialize_from_dict(
    [
        {"code": VehicleYearlyMileageEnum.LESS_2500, "description_": "VehicleYearlyMileageEnum_Less2500"},
        {"code": VehicleYearlyMileageEnum.LESS_5000, "description_": "VehicleYearlyMileageEnum_Less5000"},
        {"code": VehicleYearlyMileageEnum.LESS_7500, "description_": "VehicleYearlyMileageEnum_Less7500"},
        {"code": VehicleYearlyMileageEnum.LESS_10000, "description_": "VehicleYearlyMileageEnum_Less10000"},
        {"code": VehicleYearlyMileageEnum.LESS_15000, "description_": "VehicleYearlyMileageEnum_Less15000"},
        {"code": VehicleYearlyMileageEnum.LESS_20000, "description_": "VehicleYearlyMileageEnum_Less20000"},
        {"code": VehicleYearlyMileageEnum.LESS_25000, "description_": "VehicleYearlyMileageEnum_Less25000"},
        {"code": VehicleYearlyMileageEnum.LESS_30000, "description_": "VehicleYearlyMileageEnum_Less30000"},
        {"code": VehicleYearlyMileageEnum.LESS_40000, "description_": "VehicleYearlyMileageEnum_Less40000"},
        {"code": VehicleYearlyMileageEnum.GREATER_40000, "description_": "VehicleYearlyMileageEnum_Greater40000"},
    ],
    Item,
)

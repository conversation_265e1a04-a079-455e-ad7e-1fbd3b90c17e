import uuid
from datetime import date
from datetime import datetime
from decimal import Decimal
from enum import StrEnum

from pydantic import BaseModel
from pydantic import ConfigDict
from pydantic import Field

from bethune.api.dto.base import ExtendedAuditMixin
from bethune.api.dto.base import GenderEnum
from bethune.api.dto.base import InsuranceType
from bethune.api.dto.base import PageParams
from bethune.api.dto.base import WithId
from bethune.api.dto.base import WithReferenceNumber
from bethune.api.dto.customer import Customer
from bethune.model import InsuranceApplication as InsuranceApplicationModel
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.service.core import ReferenceTypeEnum
from bethune.service.core.ref_code import ReferenceCodeService


# from bethune.model import Customer as CustomerModel


class InsuranceStatusEnum(StrEnum):
    PENDING_QUOTE = "PENDING_QUOTE"  # 待算费
    QUOTING = "QUOTING"  # 算费中
    PENDING_UNDERWRITTEN = "PENDING_UNDERWRITTEN"  # 待承保
    UNDERWRITTEN = "UNDERWRITTEN"  # 已承保


class InsuranceApplicationStatusGroup(StrEnum):
    PENDING = "PENDING"  # 待处理
    QUTOEED = "QUOTEED"  # 已报价
    UNDERWRITTEN = "UNDERWRITTEN"  # 已承保


def to_broker_view_statuses(
    status_group: InsuranceApplicationStatusGroup | None,
) -> set[InsuranceApplicationStatus] | None:
    if status_group is None:
        return None
    if status_group == InsuranceApplicationStatusGroup.PENDING:
        return {
            InsuranceApplicationStatus.PENDING_QUOTE,
            InsuranceApplicationStatus.QUOTING,
            InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        }
    if status_group == InsuranceApplicationStatusGroup.UNDERWRITTEN:
        return {InsuranceApplicationStatus.UNDERWRITTEN}
    return set()


def to_brokerage_view_statuses(
    status_group: InsuranceApplicationStatusGroup | None,
) -> set[InsuranceApplicationStatus]:
    if status_group is None:
        return {
            InsuranceApplicationStatus.QUOTING,
            InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
            InsuranceApplicationStatus.UNDERWRITTEN,
        }
    if status_group == InsuranceApplicationStatusGroup.PENDING:
        return {InsuranceApplicationStatus.QUOTING}
    if status_group == InsuranceApplicationStatusGroup.QUTOEED:
        return {InsuranceApplicationStatus.PENDING_UNDERWRITTEN}
    return {
        InsuranceApplicationStatus.UNDERWRITTEN,
    }


class InsuranceApplicationBase(BaseModel):
    province: str | None = None
    city: str | None = None
    postal_code: str | None = None
    address: str | None = None
    customer_name: str | None = None
    customer_gender: GenderEnum | None = None
    serial_number: str | None = None
    email: str | None = None
    phone: str | None = None
    birthday: date | None = None
    insurance_type: InsuranceType = InsuranceType.HOUSE_INSURANCE
    status: InsuranceStatusEnum = InsuranceStatusEnum.PENDING_UNDERWRITTEN
    is_first_apply: bool = False
    has_rejection_record: bool = False
    expected_start_date: date | None = None
    policy_no: str | None = None
    premium: Decimal | None = Field(default=None, json_schema_extra={"type": "number", "format": "float"})
    start_date: date | None = None
    end_date: date | None = None

    model_config = ConfigDict(json_encoders={Decimal: lambda x: float(x)})  # 将 Decimal 序列化为 float


class InsuranceApplicationCreate(InsuranceApplicationBase):
    broker_id: int | None = None
    brokerage_id: int | None = None
    customer_id: int | None = None
    move_in_date: date | None = None
    insurance_consultation_id: int | None = None

    def to_model(self, ref_code_service: ReferenceCodeService):
        self.serial_number = uuid.uuid4().hex
        return InsuranceApplicationModel(
            **self.model_dump(exclude_unset=True, exclude={"move_in_date", "email", "insurance_consultation_id"}),
            email=self.email.strip().lower() if self.email else self.email,
            ref_code=ref_code_service.gen_code(ReferenceTypeEnum.INSURANCE_APPLICATION),
        )


class InsuranceApplicationQuery(PageParams):
    broker_id: int | None = None
    customer_id: int | None = None
    customer_name: str | None = None
    email: str | None = None
    phone: str | None = None
    province: str | None = None
    city: str | None = None
    address: str | None = None
    operator_id: int | None = None
    created_in_days: int | None = None
    ref_code: str | None = None
    insurance_type: InsuranceType | None = None
    status: InsuranceStatusEnum | None = None
    status_group: InsuranceApplicationStatusGroup | None = None


class InsuranceApplicationUpdate(BaseModel):
    province: str | None = None
    city: str | None = None
    postal_code: str | None = None
    address: str | None = None
    customer_name: str | None = None
    customer_gender: GenderEnum | None = None
    email: str | None = None
    phone: str | None = None
    birthday: date | None = None
    is_first_apply: bool | None = None
    has_rejection_record: bool | None = None
    expected_start_date: date | None = None

    model_config = ConfigDict(json_encoders={Decimal: lambda x: float(x)})  # 将 Decimal 序列化为 float

    def to_model(self, id: int):
        model = InsuranceApplicationModel(
            **self.model_dump(
                exclude_unset=True,
                exclude={
                    "broker_id",
                    "customer_id",
                    "brokerage_id",
                    "serial_number",
                    "email",
                    "insurance_type",
                },
            ),
            email=self.email.strip().lower() if self.email else self.email,
        )
        model.id = id
        return model


class InsuranceApplication(InsuranceApplicationBase, WithId, WithReferenceNumber, ExtendedAuditMixin):
    broker_id: int
    broker_name: str | None = None
    customer_id: int
    customer: Customer | None = None
    brokerage_id: int | None = None
    operator_id: int | None = None
    operator_name: str | None = None
    request_quote_at: datetime | None = None
    quote_at: datetime | None = None
    underwrite_at: datetime | None = None
    memo: str | None = None
    is_lead_application: bool = False
    pending_lead_payment: bool = False

    @classmethod
    def from_model(cls, model: InsuranceApplicationModel):
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[InsuranceApplicationModel]):
        return [cls.from_model(model) for model in models]


class InsuranceApplicationList(InsuranceApplicationBase, WithId, WithReferenceNumber, ExtendedAuditMixin):
    broker_id: int
    broker_name: str | None = None
    customer_id: int
    customer: Customer | None = None
    brokerage_id: int | None = None
    operator_id: int | None = None
    operator_name: str | None = None
    request_quote_at: datetime | None = None
    quote_at: datetime | None = None
    underwrite_at: datetime | None = None
    memo: str | None = None
    is_lead_application: bool = False
    pending_lead_payment: bool = False

    @classmethod
    def from_model(
        cls,
        model: InsuranceApplicationModel,
    ):
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[InsuranceApplicationModel]):
        return [cls.from_model(model) for model in models]


class ExportPdfRequest(BaseModel):
    source_url_path: str
    file_name: str | None = None
    wait_delay: int | None = None
    wait_expression: str | None = None


class ShareLinkRequest(BaseModel):
    invite_by_email: bool = False


class UnderwrittenRequest(BaseModel):
    policy_no: str | None = None
    premium: Decimal
    start_date: date
    end_date: date
    memo: str | None = None

    model_config = ConfigDict(json_encoders={Decimal: lambda x: float(x)})  # 将 Decimal 序列化为 float


class QuoteRequest(BaseModel):
    premium: Decimal
    memo: str | None = None

    model_config = ConfigDict(json_encoders={Decimal: lambda x: float(x)})  # 将 Decimal 序列化为 float


class RejectionRequest(BaseModel):
    memo: str | None = None


class EdocType(StrEnum):
    QUOTE = "quote"  # 报价单
    POLICY = "policy"  # 保单

from bethune.model.base import BaseModel
from bethune.model.insurance_company import InsuranceCompany as InsuranceCompanyModel


class InsuranceCompanyBase(BaseModel):
    name: str
    code: str


class InsuranceCompany(InsuranceCompanyBase):
    id: int

    @classmethod
    def from_model(cls, model: InsuranceCompanyModel):
        return cls(**model.model_dump())


class InsuranceCompanyList(InsuranceCompanyBase):
    id: int

    @classmethod
    def from_model(cls, model: InsuranceCompanyModel):
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[InsuranceCompanyModel]):
        return [cls.from_model(model) for model in models]

from datetime import date
from decimal import Decimal
from enum import StrEnum

from pydantic import BaseModel
from pydantic import ConfigDict
from pydantic import EmailStr
from pydantic import Field

from bethune.api.dto import PageParams
from bethune.api.dto.base import AuditMixin
from bethune.api.dto.base import GenderEnum
from bethune.api.dto.base import InsuranceType
from bethune.model import InsurancePolicy as InsurancePolicyModel


class InsurancePolicySourceType(StrEnum):
    ONLINE = "ONLINE"  # 在线承保
    IMPORTED = "IMPORTED"  # 批量导入
    MANUAL = "MANUAL"  # 手动添加


class InsurancePolicyBase(BaseModel):
    customer_name: str | None = None
    customer_gender: GenderEnum | None = None
    email: EmailStr | None = None
    phone: str | None = None
    province: str | None = None
    city: str | None = None
    address: str | None = None
    postal_code: str | None = None
    policy_no: str | None = None
    premium: float | None = None
    start_date: date
    end_date: date


class InsurancePolicyCreate(InsurancePolicyBase):
    broker_id: int | None = None
    customer_id: int
    insurance_type: InsuranceType
    source_type: str = InsurancePolicySourceType.MANUAL
    application_id: int | None = None
    brokerage_id: int | None = None

    def to_model(self):
        return InsurancePolicyModel(
            **self.model_dump(exclude={"email"}, exclude_unset=True), email=self.email.strip().lower()
        )


class InsurancePolicyUpdate(InsurancePolicyBase):

    def to_model(self, id: int):
        model = InsurancePolicyModel(**self.model_dump(exclude_unset=True))
        model.id = id
        return model


class InsurancePolicy(InsurancePolicyCreate, AuditMixin):
    id: int
    ref_code: str | None = None

    @classmethod
    def from_model(cls, model: InsurancePolicyModel):
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[InsurancePolicyModel]):
        return [cls.from_model(model) for model in models]


class InsurancePolicyQueryParams(PageParams):
    broker_id: int | None = None
    customer_id: int | None = None
    brokerage_id: int | None = None
    name: str | None = None
    email: str | None = None
    phone: str | None = None
    province: str | None = None
    city: str | None = None
    address: str | None = None
    insurance_type: InsuranceType | None = None
    source_type: InsurancePolicySourceType | None = None
    policy_no: str | None = None
    ref_code: str | None = None
    year: int | None = None
    keyword: str | None = None
    min_premium: Decimal | None = Field(default=None, json_schema_extra={"type": "number", "format": "float"})
    max_premium: Decimal | None = Field(default=None, json_schema_extra={"type": "number", "format": "float"})

    model_config = ConfigDict(json_encoders={Decimal: lambda x: float(x)})  # 将 Decimal 序列化为 float

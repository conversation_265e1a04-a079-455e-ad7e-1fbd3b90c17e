import uuid
from datetime import date
from decimal import Decimal
from enum import StrEnum
from typing import Any

from pydantic import BaseModel
from pydantic import computed_field
from pydantic import EmailStr
from pydantic import Field

from bethune.api.dto import PageParams
from bethune.api.dto.base import AuditMixin
from bethune.api.dto.base import GenderEnum
from bethune.api.dto.base import InsuranceType
from bethune.api.dto.base import WithId
from bethune.api.dto.base import WithReferenceNumber
from bethune.api.dto.base import WithStatus
from bethune.api.dto.dict import BaseLookup
from bethune.api.dto.dict.area import ProvinceLookup
from bethune.model import Broker as BrokerModel
from bethune.model import Lead as LeadModel
from bethune.model.base import InsuranceType as InsuranceTypeModel
from bethune.model.lead import LeadRichInfoComposite as LeadRichInfoCompositeModel
from bethune.model.lead import LeadStatusEnum as ModelLeadStatusEnum
from bethune.service.core import ReferenceTypeEnum
from bethune.service.core.ref_code import ReferenceCodeService
from bethune.util import get_text
from bethune.util import wiith
from bethune.util.captcha import desensitize_lead_model_dump
from bethune.util.captcha import hide_broker_non_public_fields


class LeadListQueryGroupEnum(StrEnum):
    ALL = "ALL"
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"


class LeadBase(BaseModel):
    insurance_type: InsuranceType = InsuranceType.HOUSE_INSURANCE
    customer_id: int | None = None
    customer_province: str
    customer_city: str
    customer_address: str | None = ""
    customer_postal_code: str | None = None
    customer_name: str
    customer_gender: GenderEnum | None = None
    customer_birthday: date | None = None
    customer_email: str
    customer_phone: str | None = None
    assign_to: int | None = None
    additional_info: str | None = None
    is_anonymous: bool | None = False
    serial_number: str | None = None

    @computed_field
    def customer_province_description(self) -> str:
        return wiith(
            ProvinceLookup.lookup(self.customer_province, "code"),
            lambda province_info: province_info.description if province_info else get_text("Other"),
        )

    @computed_field
    def customer_city_description(self) -> str:
        return wiith(
            ProvinceLookup.lookup(self.customer_province, "code"),
            lambda province_info: (
                wiith(
                    BaseLookup.lookup(self.customer_city, "code", province_info.model_extra["cities"]),
                    lambda city_info: city_info.description if city_info else get_text("Other"),
                )
                if province_info
                else get_text("Common_other")
            ),
        )


class WithAdditionalInfo(AuditMixin, WithStatus):
    created_by_name: str | None = None
    created_by_phone: str | None = None
    assign_to_name: str | None = None
    application_id: int | None = None
    premium: Decimal | None = None
    referral_fee_type: StrEnum | None = None
    referral_fee_value: float | None = None
    lead_referral_fee_in_cents: int | None = None
    brokerage_name: str | None = None

    @computed_field
    def lead_referral_fee(self) -> float | None:
        return (self.lead_referral_fee_in_cents / 100.0) if self.lead_referral_fee_in_cents is not None else None


class LeadCreatingRequest(LeadBase):

    insurance_info: dict[str, Any] | None = None

    def to_model(
        self,
        created_by: int,
        ref_code_service: ReferenceCodeService,
    ) -> LeadModel:
        self.serial_number = uuid.uuid4().hex

        return LeadModel(
            **self.model_dump(exclude_unset=True, exclude_none=True, exclude={"insurance_info"})
            | {
                "created_by": created_by,
                "status": ModelLeadStatusEnum.DRAFT,
                "ref_code": ref_code_service.gen_code(ReferenceTypeEnum.LEAD),
            }
        )


class LeadUpdatingRequest(BaseModel):
    customer_id: int | None = None
    customer_province: str | None = None
    customer_city: str | None = None
    customer_address: str | None = None
    customer_postal_code: str | None = None
    customer_name: str | None = None
    customer_gender: GenderEnum | None = None
    customer_birthday: date | None = None
    customer_email: EmailStr | None = None
    customer_phone: str | None = None
    additional_info: str | None = None
    insurance_info: dict[str, Any] | None = None

    def to_model(self, id: int, serial_number: str) -> LeadModel:
        new_instance = LeadModel(
            **self.model_dump(
                exclude_unset=True,
                exclude_none=True,
                exclude={
                    "insurance_info",
                    "insurance_type",
                    "created_by",
                    "assign_to",
                    "referral_fee_type",
                    "referral_fee_value",
                },
            )
            | {
                "id": id,
            }
        )

        if serial_number == "":
            new_instance.serial_number = uuid.uuid4().hex

        return new_instance


class LeadAssignRequest(BaseModel):
    assign_to_id: int
    is_anonymous: bool | None = False


class LeadResponse(LeadBase, WithId, WithReferenceNumber, WithAdditionalInfo):
    assigned_to_broker_: BrokerModel | None = Field(default=None, exclude=True)
    insurance_info: dict[str, Any] | None = Field(default=None)

    @computed_field
    def assign_to_broker(self) -> dict[str, Any] | None:
        return (
            hide_broker_non_public_fields(
                self.assigned_to_broker_.model_dump() | {"email": self.assigned_to_broker_.user.email},
                set(
                    self.assigned_to_broker_.profile.public_fields.split("|")
                    if self.assigned_to_broker_.profile.public_fields
                    else ""
                ),
            )
            if self.assigned_to_broker_
            else None
        )

    @classmethod
    def from_model(cls, model: LeadModel, broker: BrokerModel) -> "LeadResponse":
        return cls(**desensitize_lead_model_dump(model, broker))


class LeadListQueryParams(PageParams):
    customer_id: int | None = None
    status_group: LeadListQueryGroupEnum | None = LeadListQueryGroupEnum.ALL


class LeadRichInfoListResponse(LeadResponse):
    @classmethod
    def from_model(cls, model: LeadRichInfoCompositeModel, broker: BrokerModel) -> "LeadRichInfoListResponse":
        return cls(**desensitize_lead_model_dump(model, broker), assigned_to_broker_=model.assign_to_broker)

    @classmethod
    def from_models(
        cls, models: list[LeadRichInfoCompositeModel], broker: BrokerModel
    ) -> list["LeadRichInfoListResponse"]:
        return [cls.from_model(lead, broker) for lead in models]


class CandidateBrokerListForCreatingLeadQueryParams(PageParams):
    lead_id: int | None = None
    insurance_type: InsuranceTypeModel | None = None
    customer_province: str | None = None
    customer_city: str | None = None
    keyword: str | None = None

from pydantic import BaseModel

from bethune.api.dto.base import AuditMixin
from bethune.model.system import Permission as PermissionModel


class Permission(BaseModel):
    id: int
    code: str
    name: str
    description: str | None = None

    @classmethod
    def from_model(cls, model: PermissionModel) -> "Permission":
        return cls(**model.model_dump())

    def to_model(self) -> PermissionModel:
        return PermissionModel(**self.model_dump(exclude_unset=True))


class PermissionList(Permission, AuditMixin):

    @classmethod
    def from_model(cls, model: PermissionModel) -> "PermissionList":
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[PermissionModel]) -> list["PermissionList"]:
        return [cls.from_model(model) for model in models]

from pydantic import BaseModel

from bethune.api.dto.base import AuditMixin
from bethune.model.system import Role as RoleModel


class RoleBase(BaseModel):
    name: str
    description: str | None = None


class Role(RoleBase):
    id: int

    @classmethod
    def from_model(cls, model: RoleModel) -> "Role":
        return cls(**model.model_dump())


class RoleCreate(RoleBase):

    def to_model(self) -> Role:
        return RoleModel(**self.model_dump(exclude_unset=True))


class RoleUpdate(RoleBase):

    def to_model(self, id: int) -> RoleModel:
        model = RoleModel(**self.model_dump(exclude_unset=True))
        model.id = id
        return model


class RoleList(RoleBase, AuditMixin):
    id: int

    @classmethod
    def from_model(cls, model: RoleModel) -> "RoleList":
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[RoleModel]) -> list["RoleList"]:
        return [cls.from_model(model) for model in models]

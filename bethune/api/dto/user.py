from pydantic import BaseModel

from bethune.api.dto.base import AuditMixin
from bethune.model.system import User as UserModel
from bethune.model.system import UserStatus
from bethune.model.system import UserType
from bethune.util import hash_password


class UserBase(BaseModel):
    email: str
    mobile: str | None = None
    name: str | None = None
    avatar: str | None = None
    user_type: UserType | None = None
    status: UserStatus | None = None
    language: str | None = None
    referer_id: int | None = None


class User(UserBase):
    id: int

    @classmethod
    def from_model(cls, model: UserModel) -> "User":
        return cls(**model.model_dump())


class UserCreate(UserBase):
    password: str

    def to_model(self) -> UserModel:
        return UserModel(
            **self.model_dump(exclude={"email", "password", "status"}, exclude_unset=True),
            email=self.email.strip().lower(),
            password=hash_password(self.password),
            status=UserStatus.ACTIVE,
        )


class UserUpdate(UserBase):

    def to_model(self, id: int) -> UserModel:
        model = UserModel(**self.model_dump(exclude={"email"}, exclude_unset=True), email=self.email.strip().lower())
        model.id = id
        return model


class UserList(UserBase, AuditMixin):
    id: int

    @classmethod
    def from_model(cls, model: UserModel) -> "UserList":
        return cls(**model.model_dump(exclude={"password"}))

    @classmethod
    def from_models(cls, models: list[UserModel]) -> list["UserList"]:
        return [cls.from_model(model) for model in models]

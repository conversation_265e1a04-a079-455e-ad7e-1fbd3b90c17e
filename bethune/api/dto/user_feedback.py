from pydantic import BaseModel

from bethune.model.user_feedback import FeedbackType<PERSON>num
from bethune.model.user_feedback import UserFeedback as UserFeedbackModel


class UserFeedbackBase(BaseModel):
    contact: str
    """联系方式"""
    feedback_type: FeedbackTypeEnum
    """反馈类型"""
    content: str
    """反馈内容"""


class UserFeedbackCreate(UserFeedbackBase):

    def to_model(self, user_id: int) -> UserFeedbackModel:
        return UserFeedbackModel(
            **self.model_dump(exclude_unset=True),
            user_id=user_id,
        )


class UserFeedback(UserFeedbackBase):
    id: int
    user_id: int

    @classmethod
    def from_model(cls, model: UserFeedbackModel) -> "UserFeedback":
        return cls(model.model_dump(exclude_unset=True))

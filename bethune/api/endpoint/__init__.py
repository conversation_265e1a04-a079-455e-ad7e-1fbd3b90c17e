from fastapi import APIRouter

from .auth import auth_router
from .dict import dict_router
from .insurance import insurance_router
from .insurance.insurance_application.short_link import api_router as short_link_router
from .manage import manage_router
from .resource import resource_router
from .system import system_router
from bethune.api.endpoint.system.report import api_router as report_router
from bethune.settings import settings
from bethune.util import concat_path

api_router = APIRouter(prefix=concat_path(settings.API_PREFIX, settings.API_VERSION))

api_router.include_router(auth_router)
api_router.include_router(resource_router)
api_router.include_router(system_router)
api_router.include_router(insurance_router)
api_router.include_router(dict_router)

# 短链接api
api_router.include_router(short_link_router)

# 管理api，可以用于检查liveness、readiness等等
api_router.include_router(manage_router)

api_router.include_router(report_router)

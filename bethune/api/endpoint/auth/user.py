from enum import StrEnum
from typing import Annotated
from typing import Union

from fastapi import APIRouter
from fastapi import Security

from bethune.api.dependencies.authorization import check_not_shared_link
from bethune.api.dto import BaseResponse
from bethune.api.dto.broker import Broker
from bethune.api.dto.brokerage_user import BrokerageUserResponse
from bethune.api.dto.user import User
from bethune.api.endpoint.insurance.service_context import ServiceContext as InsuranceServiceContext

api_router = APIRouter(tags=["auth"])


class UserInfoTypeEnum(StrEnum):
    PC = "PC"
    MOBILE = "MOBILE"
    SYSTEM = "SYSTEM"


@api_router.get(
    "/user_info",
    summary="Get current user information by type",
    response_model=BaseResponse[Union[Broker, User, BrokerageUserResponse]],
    dependencies=[Security(check_not_shared_link)],
)
async def get_current_user_info(
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[Broker | User | BrokerageUserResponse]:
    user_id = sc.current_user.id
    if sc.brokerage_user_service.get_by_user_id(user_id, True):  # type: ignore
        if not sc.broker_service.get_by_user_id(user_id, True):  # type: ignore
            return BaseResponse[BrokerageUserResponse].ok(BrokerageUserResponse.from_model(sc.current_brokerage_user))
    if sc.broker_service.get_by_user_id(user_id, True):  # type: ignore
        return BaseResponse[Broker].ok(
            Broker.from_model(
                sc.current_broker,
                sc.permissions,
            )  # type: ignore
        )
    return BaseResponse[User].ok(sc.current_user)

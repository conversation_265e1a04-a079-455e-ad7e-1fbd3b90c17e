from typing import Annotated

from fastapi import APIRouter
from fastapi import BackgroundTasks
from fastapi import Depends

from bethune.api.dto.auth import VerificationRequest
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import VerificationCodeTypeEnum
from bethune.api.endpoint.core.service_context import ServiceContext as CoreServiceContext
from bethune.api.endpoint.system.service_context import ServiceContext as SystemServiceContext

api_router = APIRouter(tags=["auth"])


@api_router.post("/verification_code")
async def send_verification_email(
    core_sc: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
    verification_request: VerificationRequest,
    background_tasks: BackgroundTasks,
):
    if verification_request.type == VerificationCodeTypeEnum.REGISTRATION:
        await sc_system.user_service.check_user_exists(str(verification_request.email))
        background_tasks.add_task(
            core_sc.verification_code_service.send_verification_email, str(verification_request.email), 4, False
        )
    if verification_request.type == VerificationCodeTypeEnum.RESET_PASSWORD:
        if await sc_system.user_service.check_user_exists_no_error(str(verification_request.email)):
            background_tasks.add_task(
                core_sc.verification_code_service.send_verification_email, str(verification_request.email), 4, True
            )
    if verification_request.type == VerificationCodeTypeEnum.GENERAL:
        await sc_system.user_service.check_user_exists(str(verification_request.email))
        background_tasks.add_task(
            core_sc.verification_code_service.send_verification_email, str(verification_request.email), 4, False
        )

    return BaseResponse.ok(verification_request.email)

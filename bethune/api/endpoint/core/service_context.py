from bethune.api.dependencies.authorization import BaseServiceContext
from bethune.service.core import EmailService
from bethune.service.core import VerificationCodeService
from bethune.service.core.factory import CoreServiceFactory
from bethune.service.core.ref_code import ReferenceCodeService


class ServiceContext(BaseServiceContext):

    def __init__(self):
        super().__init__()
        self._verification_code_service = None
        self._email_service = None
        self._reference_code_service = None

    @property
    def verification_code_service(self) -> VerificationCodeService:
        if not self._verification_code_service:
            self._verification_code_service = CoreServiceFactory.create_verification_code_service()
        return self._verification_code_service

    @property
    def email_service(self) -> EmailService:
        if not self._email_service:
            self._email_service = CoreServiceFactory.create_email_service()
        return self._email_service

    @property
    def reference_code_service(self) -> ReferenceCodeService:
        if not self._reference_code_service:
            self._reference_code_service = CoreServiceFactory.create_reference_code_service()
        return self._reference_code_service

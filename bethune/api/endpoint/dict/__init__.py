from fastapi import APIRouter

from .area import api_router as area_router
from .broker import api_router as broker_router
from .brokerage import api_router as brokerage_router
from .common import api_router as common_router
from .house_info import api_router as house_info_router
from .insurance_info import api_router as insurance_info
from .supported_language import api_router as supported_lang_router
from .vehicle_info import api_router as vehicle_info_router


dict_router = APIRouter(prefix="/dict")

dict_router.include_router(common_router)
dict_router.include_router(area_router)
dict_router.include_router(supported_lang_router)
dict_router.include_router(house_info_router)
dict_router.include_router(vehicle_info_router)
dict_router.include_router(insurance_info)
dict_router.include_router(broker_router)
dict_router.include_router(brokerage_router)

from fastapi import APIRouter

from ...dto.dict.area import Item
from ...dto.dict.area import ProvinceLookup
from bethune.api.dto import BaseResponse
from bethune.util import wiith

api_router = APIRouter(prefix="", tags=["dict", "areas"])


@api_router.get(
    "/province",
    response_model=BaseResponse[list[Item]],
    description="it returns provinces of Canada",
)
async def get_provinces(
    include_federal: bool | None = False,
):
    return BaseResponse.ok(
        data=wiith(ProvinceLookup.items, lambda items: items if include_federal else items[1:]),
    )

from fastapi import APIRouter
from fastapi import Query

from ...dto.dict.area import Item
from ...dto.dict.broker import FeedbackTypeLookup
from ...dto.dict.broker import InsuranceConsultationSourceLookup
from ...dto.dict.broker import InsuranceTypeLookup
from ...dto.dict.broker import PublicFieldLookup
from bethune.api.dto import BaseResponse

api_router = APIRouter(prefix="/broker", tags=["dict", "broker", "config"])

_CATEGORIES = {
    "public-field": PublicFieldLookup.items,
    "feedback-type": FeedbackTypeLookup.items,
    "insurance-type": InsuranceTypeLookup.items,
    "insurance-consultation-source": InsuranceConsultationSourceLookup.items,
}


@api_router.get(
    "",
    response_model=BaseResponse[dict[str, list[Item]]],
    description="it returns all/selected broker config info",
)
async def get_broker_dictionaries(
    selected_categories: str | None = Query(
        default="",
        description="Comma-separated list of categories to filter broker config info. Leave empty to get all config.",
        example="public-field",
        enum=set(_CATEGORIES.keys()),  # 支持的值列表
    ),
):
    if not selected_categories:
        # all
        categories = _CATEGORIES
    else:
        categories = {key: _CATEGORIES[key.lower().strip()] for key in selected_categories.split(",")}
    return BaseResponse.ok(data=categories)

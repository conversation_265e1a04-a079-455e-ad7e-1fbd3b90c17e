from fastapi import APIRouter
from fastapi import Query

from ...dto.dict.area import Item
from ...dto.dict.brokerage import BrokerageUserRoleLookup
from bethune.api.dto import BaseResponse

api_router = APIRouter(prefix="/brokerage", tags=["dict", "brokerage"])

_CATEGORIES = {
    "user-role": BrokerageUserRoleLookup.items,
}


@api_router.get(
    "",
    response_model=BaseResponse[dict[str, list[Item]]],
    description="it returns all/selected brokerage config info",
)
async def get_brokerage_dictionaries(
    selected_categories: str | None = Query(
        default="",
        description="Comma-separated list of categories to filter brokerage config info. Leave empty to get all config.",
        example="user-role",
        enum=set(_CATEGORIES.keys()),  # 支持的值列表
    ),
):
    if not selected_categories:
        # all
        categories = _CATEGORIES
    else:
        categories = {key: _CATEGORIES[key.lower().strip()] for key in selected_categories.split(",")}
    return BaseResponse.ok(data=categories)

from fastapi import APIRouter
from fastapi import Query

from ...dto.dict.area import Item
from ...dto.dict.common import GenderLookup
from ...dto.dict.common import NumberOfClaimsLookup
from bethune.api.dto import BaseResponse

api_router = APIRouter(prefix="/common", tags=["dict", "common"])

_CATEGORIES = {
    "gender": GenderLookup.items,
    "number-of-claims": NumberOfClaimsLookup.items,
}


@api_router.get(
    "",
    response_model=BaseResponse[dict[str, list[Item]]],
    description="it returns all/selected common info",
)
async def get_common_info(
    selected_categories: str | None = Query(
        default="",
        description="Comma-separated list of categories to filter house info. Leave empty to get all categories.",
        example="gender",
        enum=set(_CATEGORIES.keys()),  # 支持的值列表
    ),
):
    if not selected_categories:
        # all
        categories = _CATEGORIES
    else:
        categories = {key: _CATEGORIES[key.lower().strip()] for key in selected_categories.split(",")}
    return BaseResponse.ok(data=categories)

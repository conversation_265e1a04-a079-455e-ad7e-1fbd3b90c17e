from fastapi import APIRouter
from fastapi import Query

from ...dto.dict import Item
from ...dto.dict.house_info import BuildingMaterialLookup
from ...dto.dict.house_info import GeneralQuantityLookup
from ...dto.dict.house_info import GeneralYearLookup
from ...dto.dict.house_info import GeneralYearWithNeverOptionLookup
from ...dto.dict.house_info import HeatingFuelLookup
from ...dto.dict.house_info import HouseTypeLookup
from ...dto.dict.house_info import OccupancyStatusLookup
from ...dto.dict.house_info import PlumbingPipeTypeLookup
from ...dto.dict.house_info import PrimaryHeatingSourceLookup
from ...dto.dict.house_info import RoofingMaterialLookup
from ...dto.dict.house_info import SecurityAlarmLookup
from ...dto.dict.house_info import StructureLookup
from ...dto.dict.house_info import StructureLookup2
from ...dto.dict.house_info import SumInsuredLookup
from ...dto.dict.house_info import VoltageLookup
from ...dto.dict.house_info import WiringTypeLookup
from bethune.api.dto import BaseResponse

api_router = APIRouter(prefix="/house-info", tags=["dict", "house"])

_CATEGORIES = {
    "house_type": HouseTypeLookup.items,
    "occupancy_status": OccupancyStatusLookup.items,
    "primary_heating_source": PrimaryHeatingSourceLookup.items,
    "wiring_type": WiringTypeLookup.items,
    "roofing_material": RoofingMaterialLookup.items,
    "voltage": VoltageLookup.items,
    "plumbing_pipe_type": PlumbingPipeTypeLookup.items,
    "building_material": BuildingMaterialLookup.items,
    "structure": StructureLookup.items,
    "structure2": StructureLookup2.items,
    "heating_fuel": HeatingFuelLookup.items,
    "security_alarm": SecurityAlarmLookup.items,
    "general_quantity": GeneralQuantityLookup.items(),
    "general_year": GeneralYearLookup.items(),
    "general_year_with_none_option": GeneralYearWithNeverOptionLookup.items(),
    "sum_insured": SumInsuredLookup.items(),
}


@api_router.get(
    "",
    response_model=BaseResponse[dict[str, list[Item]]],
    description="it returns all/selected house info",
)
async def get_house_info(
    selected_categories: str | None = Query(
        default="",
        description="Comma-separated list of categories to filter house info. Leave empty to get all categories.",
        example="house_type,wiring_type",
        enum=set(_CATEGORIES.keys()),  # 支持的值列表
    ),
):
    if not selected_categories:
        # all
        categories = _CATEGORIES
    else:
        categories = {key: _CATEGORIES[key.lower().strip()] for key in selected_categories.split(",")}
    return BaseResponse.ok(data=categories)

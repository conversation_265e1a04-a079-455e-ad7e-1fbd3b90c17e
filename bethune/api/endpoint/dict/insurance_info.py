from fastapi import APIRouter

from ...dto.dict import Item
from ...dto.dict.insurance_info import HouseBuildingMaterialLookup
from ...dto.dict.insurance_info import HouseGeneralQuantityLookup
from ...dto.dict.insurance_info import HouseGeneralYearLookup
from ...dto.dict.insurance_info import HouseGeneralYearWithNeverOptionLookup
from ...dto.dict.insurance_info import HouseHeatingFuelLookup
from ...dto.dict.insurance_info import HouseOccupancyStatusLookup
from ...dto.dict.insurance_info import HousePlumbingPipeTypeLookup
from ...dto.dict.insurance_info import HousePrimaryHeatingSourceLookup
from ...dto.dict.insurance_info import HouseRoofingMaterialLookup
from ...dto.dict.insurance_info import HouseSecurityAlarmLookup
from ...dto.dict.insurance_info import HouseStructure2Lookup
from ...dto.dict.insurance_info import HouseStructureLookup
from ...dto.dict.insurance_info import HouseSumInsuredLookup
from ...dto.dict.insurance_info import HouseTypeLookup
from ...dto.dict.insurance_info import HouseVoltageLookup
from ...dto.dict.insurance_info import HouseWiringTypeLookup
from ...dto.dict.insurance_info import InsuranceLifeTypeLookup
from ...dto.dict.insurance_info import VehicleDriverLicenseStatusLookup
from ...dto.dict.insurance_info import VehicleDriverLicenseTypeLookup
from ...dto.dict.insurance_info import VehicleDriverProficiencyLookup
from ...dto.dict.insurance_info import VehicleDriverRelationshipLookup
from ...dto.dict.insurance_info import VehicleOwnershipStatusLookup
from ...dto.dict.insurance_info import VehicleYearlyMileageLookup
from bethune.api.dto import BaseResponse

# 保险信息相关导入
# 车险相关导入
# 房屋险相关导入

api_router = APIRouter(prefix="/insurance-info", tags=["dict", "insurance"])

_INSURANCE_CATEGORIES = {
    "life_type": InsuranceLifeTypeLookup.items,
}

_VEHICLE_CATEGORIES = {
    "driver_proficiency": VehicleDriverProficiencyLookup.items,
    "driver_license_type": VehicleDriverLicenseTypeLookup.items,
    "driver_license_status": VehicleDriverLicenseStatusLookup.items,
    "driver_relationship": VehicleDriverRelationshipLookup.items,
    "vehicle_ownership_status": VehicleOwnershipStatusLookup.items,
    "vehicle_yearly_mileage": VehicleYearlyMileageLookup.items,
}

_HOUSE_CATEGORIES = {
    "house_type": HouseTypeLookup.items,
    "occupancy_status": HouseOccupancyStatusLookup.items,
    "primary_heating_source": HousePrimaryHeatingSourceLookup.items,
    "wiring_type": HouseWiringTypeLookup.items,
    "roofing_material": HouseRoofingMaterialLookup.items,
    "voltage": HouseVoltageLookup.items,
    "plumbing_pipe_type": HousePlumbingPipeTypeLookup.items,
    "building_material": HouseBuildingMaterialLookup.items,
    "structure": HouseStructureLookup.items,
    "structure2": HouseStructure2Lookup.items,
    "heating_fuel": HouseHeatingFuelLookup.items,
    "security_alarm": HouseSecurityAlarmLookup.items,
    "general_quantity": HouseGeneralQuantityLookup.items(),
    "general_year": HouseGeneralYearLookup.items(),
    "general_year_with_none_option": HouseGeneralYearWithNeverOptionLookup.items(),
    "sum_insured": HouseSumInsuredLookup.items(),
}

_CATEGORIES = {
    **_INSURANCE_CATEGORIES,
    **_VEHICLE_CATEGORIES,
    **_HOUSE_CATEGORIES,
}


@api_router.get("", response_model=BaseResponse[dict[str, list[Item]]])
async def get_insurance_info():
    return BaseResponse.ok(data=_CATEGORIES)

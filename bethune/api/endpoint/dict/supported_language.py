from fastapi import APIRouter

from ...dto.dict.area import Item
from ...dto.dict.supported_language import SupportedLanguageLookup
from bethune.api.dto import BaseResponse

api_router = APIRouter(prefix="", tags=["dict", "languages"])


@api_router.get(
    "/supported-language",
    response_model=BaseResponse[list[Item]],
    description="it returns all supported languages",
)
async def get_supported_languages():
    return BaseResponse.ok(data=SupportedLanguageLookup.items)

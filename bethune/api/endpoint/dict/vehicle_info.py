from fastapi import APIRouter
from fastapi import Query

from ...dto.dict import Item
from ...dto.dict.vehicle_info import DriverLicenseStatusLookup
from ...dto.dict.vehicle_info import DriverLicenseTypeLookup
from ...dto.dict.vehicle_info import DriverProficiencyLookup
from ...dto.dict.vehicle_info import DriverRelationshipLookup
from ...dto.dict.vehicle_info import VehicleOwnershipStatusLookup
from ...dto.dict.vehicle_info import VehicleYearlyMileageLookup
from bethune.api.dto import BaseResponse

api_router = APIRouter(prefix="/vehicle-info", tags=["dict", "vehicle"])

_CATEGORIES = {
    "driver_proficiency": DriverProficiencyLookup.items,
    "driver_license_type": DriverLicenseTypeLookup.items,
    "driver_license_status": DriverLicenseStatusLookup.items,
    "driver_relationship": DriverRelationshipLookup.items,
    "vehicle_ownership_status": VehicleOwnershipStatusLookup.items,
    "vehicle_yearly_mileage": VehicleYearlyMileageLookup.items,
}


@api_router.get(
    "",
    response_model=BaseResponse[dict[str, list[Item]]],
    description="it returns all/selected vehicle info",
)
async def get_vehicle_info(
    selected_categories: str | None = Query(
        default="",
        description="Comma-separated list of categories to filter vehicle info. Leave empty to get all categories.",
        example="driver_proficiency,vehicle_ownership_status",
        enum=set(_CATEGORIES.keys()),  # 支持的值列表
    ),
):
    if not selected_categories:
        # all
        categories = _CATEGORIES
    else:
        categories = {key: _CATEGORIES[key.lower().strip()] for key in selected_categories.split(",")}
    return BaseResponse.ok(data=categories)

from fastapi import APIRouter

from .broker import api_router as broker_router
from .brokerage import api_router as brokerage_router
from .insurance_application import api_router as insurance_application_router
from .insurance_company import api_router as insurance_company_router
from .lead import api_router as lead_router

insurance_router = APIRouter(prefix="/insurance")

insurance_router.include_router(broker_router)
insurance_router.include_router(brokerage_router)
insurance_router.include_router(insurance_company_router)
insurance_router.include_router(lead_router)
insurance_router.include_router(insurance_application_router)

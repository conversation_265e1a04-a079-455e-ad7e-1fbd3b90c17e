from fastapi import APIRouter

from .broker import api_router as broker_router
from .customer import api_router as customer_router
from .insurance_policy import api_router as insurance_policy_router
from .reminder import api_router as reminder_router
from bethune.api.endpoint.insurance.insurance_application import (
    api_router_for_broker as insurance_application_router,
)

api_router = APIRouter(prefix="/broker")

api_router.include_router(broker_router)
api_router.include_router(customer_router)
api_router.include_router(insurance_application_router)
api_router.include_router(insurance_policy_router)
api_router.include_router(reminder_router)

from datetime import date
from pathlib import Path
from typing import Annotated
from typing import Any

from fastapi import APIRouter
from fastapi import Query
from fastapi import Security
from fastapi import UploadFile
from fastapi.params import File
from fastapi.responses import FileResponse
from pydantic import BaseModel
from pydantic import Field

from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import Pagination
from bethune.api.dto.broker import InsuranceConsultationCustomer
from bethune.api.dto.customer import Customer
from bethune.api.dto.customer import CustomerCreate
from bethune.api.dto.customer import CustomerList
from bethune.api.dto.customer import CustomerListQueryParams
from bethune.api.dto.customer import CustomerUpdate
from bethune.api.endpoint.insurance.broker.import_helper import iter_rows_from_upload_file
from bethune.api.endpoint.insurance.service_context import ServiceContext
from bethune.api.error import UnauthorizedError
from bethune.model.customer import Customer as CustomerModel
from bethune.model.customer import CustomerQueryFilters
from bethune.settings import settings

api_router = APIRouter(prefix="/customer", tags=["customer"])


def check_and_get_my_customer(sc: ServiceContext, customer_id: int) -> CustomerModel:
    customer = sc.customer_service.get_by_id(customer_id)
    # check if this customer belongs to current broker
    if customer.broker_id != sc.current_broker.id:
        raise UnauthorizedError(detail={"customer_id": customer_id, "broker_id": sc.current_broker.id})
    return customer


@api_router.post("", summary="create customer", response_model=BaseResponse[Customer])
async def create(
    customer: CustomerCreate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:customer:create"]),
) -> BaseResponse[Customer]:
    customer.broker_id = sc.current_broker.id
    created_customer = sc.customer_service.create(customer.to_model())
    customer_view = Customer.from_model(created_customer)
    await sc.broker_service.add_tags(
        customer_view.broker_id,
        customer_view.tags,
    )

    if customer.insurance_consultation_id:
        sc.insurance_consultation_customer_service.create(
            InsuranceConsultationCustomer(
                customer_id=created_customer.id,
                insurance_consultation=customer.insurance_consultation_id,
            ).to_model()
        )

    return BaseResponse.ok(customer_view)


@api_router.get("/{id:int}", summary="get customer by id", response_model=BaseResponse[Customer])
async def get(
    id: int,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:customer:view"]),
) -> BaseResponse[Customer]:
    customer = check_and_get_my_customer(sc, id)
    return BaseResponse.ok(Customer.from_model(customer))


@api_router.get("", summary="get customers", response_model=BaseResponse[Pagination[CustomerList]])
async def query(
    query_params: Annotated[CustomerListQueryParams, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:customer:view"]),
) -> BaseResponse[Pagination[CustomerList]]:
    broker_id: int = sc.current_broker.id  # type: ignore
    if query_params.expire_in_days:
        total, customers = sc.customer_service.get_customers_with_expiring_end_date(
            broker_id=broker_id,
            expired_in_days=query_params.expire_in_days,
            broker_is_qualified=sc.current_broker.profile.qualification.is_qualified,
            offset=query_params.offset(),
            limit=query_params.limit(),
        )
    else:
        filters = CustomerQueryFilters(
            **query_params.model_dump(exclude={"expire_in_days", "page_no", "page_size"}, exclude_unset=True),
            broker_id=sc.current_broker.id,
        )
        total, customers = sc.customer_service.get_customers(
            filters,
            query_params.offset(),
            query_params.limit(),
        )
    data = Pagination[CustomerList].from_items(
        CustomerList.from_models(customers),
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.put(
    "/{id}",
    summary="update customer by id",
    response_model=BaseResponse[Customer],
)
async def update(
    id: int,
    customer: CustomerUpdate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:customer:update"]),
) -> BaseResponse[Customer]:
    # check if customer exists
    check_and_get_my_customer(sc, id)
    updated_customer = sc.customer_service.update(customer.to_model(id))
    customer_view = Customer.from_model(updated_customer)
    await sc.broker_service.add_tags(
        customer_view.broker_id,
        customer_view.tags,
    )
    return BaseResponse.ok(customer_view)


@api_router.delete(
    "/{id}",
    summary="delete customer by id",
    response_model=BaseResponse[Customer],
)
async def delete(
    id: int,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:customer:delete"]),
) -> BaseResponse[Customer]:
    check_and_get_my_customer(sc, id)
    return BaseResponse.ok(Customer.from_model(sc.customer_service.mark_as_deleted(id)))


@api_router.get("/import-template-file", summary="get customer import template file")
async def get_customer_import_template_file(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:customer:view"]),
) -> FileResponse:
    import_template_file = Path(settings.DATA_DIR, "templates", "customer_import_template.xlsx")
    headers = {"Content-Disposition": 'attachment; filename="customer_import_template.xlsx"'}
    return FileResponse(
        import_template_file,
        headers=headers,
    )


def normalize_data(row: dict[str, Any]) -> dict[str, Any]:
    """
    Normalize the data from the row:
    1. Remove None values.
    2. Strip whitespace from string values.
    """
    return {
        key: value if not isinstance(value, str) else value.strip() for key, value in row.items() if value is not None
    }


@api_router.post(
    "/import",
    summary="批量导入客户",
    response_model=BaseResponse[int],
)
async def import_customers(
    file: UploadFile = File(...),
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:customer:create"]),
) -> BaseResponse[int]:
    """
    批量导入客户
    """
    counter = 0

    class ImportRow(BaseModel):
        name: Annotated[str, Field(alias="Name(*)", max_length=150)]
        email: Annotated[str, Field(alias="Email(*)", max_length=150)]
        phone: Annotated[str, Field(alias="Phone Number", default="", max_length=20)]
        birthday: Annotated[date | None, Field(alias="Date of Birth", default=None)]
        province: Annotated[str, Field(alias="Province", default="", max_length=100)]
        city: Annotated[str, Field(alias="City", default="", max_length=100)]
        address: Annotated[str, Field(alias="Address", default="", max_length=200)]
        postal_code: Annotated[str, Field(alias="Postal Code", default="", max_length=20)]
        wechat: Annotated[str, Field(alias="Wechat", default="", max_length=100)]
        tags: Annotated[str, Field(alias="Tags", default="", max_length=100)]
        memo: Annotated[str, Field(alias="Memo", default="", max_length=500)]

    async for row in iter_rows_from_upload_file(
        file,
        expected_headers=[field.alias for field in ImportRow.model_fields.values()],
    ):
        import_row = ImportRow(**row)
        example = CustomerModel(
            broker_id=sc.current_broker.id,
            name=import_row.name,
            email=import_row.email.lower(),
        )
        customer = sc.customer_service.get_by_example(example, 0, 1)
        if customer:
            continue
        customer = CustomerCreate(
            **import_row.model_dump(exclude={"email", "tags"}),
            email=import_row.email.lower(),
            tags=set(import_row.tags.split(",")) if import_row.tags else set(),
            broker_id=sc.current_broker.id,
        )
        customer = sc.customer_service.create(customer.to_model())
        counter += 1
    return BaseResponse.ok(counter)

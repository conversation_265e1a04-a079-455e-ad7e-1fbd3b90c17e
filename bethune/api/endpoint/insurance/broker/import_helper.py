import asyncio
from collections.abc import AsyncGenerator
from io import Bytes<PERSON>
from typing import Any

from fastapi import UploadFile

from bethune.error import DataValidationError
from bethune.util.excel import iter_excel_rows


async def iter_rows_from_upload_file(
    file: UploadFile,
    expected_headers: list[str],
) -> AsyncGenerator[dict[str, Any], None]:
    """
    Asynchronous generator function to iterate over rows in an uploaded Excel file.

    :param file: The uploaded Excel file.
    :return: An asynchronous generator that yields rows from the specified sheet.
    """
    if file.content_type != "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        raise DataValidationError("Invalid file type. Please upload an Excel file.")
    contents = await file.read()
    if not contents:
        raise DataValidationError("File is empty")
    stream = BytesIO(contents)
    rows = iter_excel_rows(stream, skip_header=False)
    headers = next(rows)
    if set(headers) != set(expected_headers):
        raise DataValidationError(f"Invalid file format. Expected headers: {expected_headers}, but got: {headers}")
    for row in rows:
        # 让出控制权，避免阻塞
        await asyncio.sleep(0)
        yield {x: y for x, y in zip(headers, row)}  # type: ignore

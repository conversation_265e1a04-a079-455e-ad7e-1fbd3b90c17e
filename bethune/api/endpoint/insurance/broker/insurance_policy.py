from datetime import date
from typing import Annotated
from typing import Any

from fastapi import APIRouter
from fastapi import Security
from fastapi import UploadFile
from fastapi.params import Depends
from fastapi.params import File
from fastapi.params import Path
from fastapi.params import Query
from fastapi.responses import FileResponse
from pydantic import BaseModel
from pydantic import EmailStr
from pydantic import Field
from pydantic import ValidationError

from .import_helper import iter_rows_from_upload_file
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import Pagination
from bethune.api.dto.insurance_application import EdocType
from bethune.api.dto.insurance_policy import InsurancePolicy
from bethune.api.dto.insurance_policy import InsurancePolicyCreate
from bethune.api.dto.insurance_policy import InsurancePolicyQueryParams
from bethune.api.dto.insurance_policy import InsurancePolicyUpdate
from bethune.api.endpoint.core.service_context import (
    ServiceContext as CoreServiceContext,
)
from bethune.api.endpoint.insurance.edoc import EdocFileHelper
from bethune.api.endpoint.insurance.service_context import ServiceContext
from bethune.api.error import UnauthorizedError
from bethune.error.errors import DataValidationError
from bethune.model.customer import Customer as CustomerModel
from bethune.model.insurance import InsurancePolicy as InsurancePolicyModel
from bethune.model.insurance import InsurancePolicyQueryFilters
from bethune.model.insurance import InsurancePolicySourceType
from bethune.model.insurance import InsuranceType
from bethune.service.core.ref_code import ReferenceTypeEnum


api_router = APIRouter(prefix="/insurance-policy", tags=["policy"])


def _raise_unauthorized_error(id):
    raise UnauthorizedError(
        err_msg="You are not authorized to access this insurance policy.",
        detail={
            "id": id,
        },
    )


def _check_and_get_insurance_policy(
    id: Annotated[int, Path(description="insurance policy id")],
    sc: ServiceContext = Security(ServiceContext.create),
) -> InsurancePolicyModel:
    """
    Get the insurance policy service from the service context.
    """
    policy = sc.insurance_policy_service.get_by_id(id)
    if sc.current_user.is_broker_role:
        if policy.broker_id != sc.current_broker.id:
            _raise_unauthorized_error(id)
    elif sc.current_user.is_brokerage_role or sc.current_user.is_brokerage_admin_role:
        if policy.brokerage_id != sc.current_broker.brokerage_id:
            _raise_unauthorized_error(id)
    return policy


def _get_ref_code(
    insurance_policy: InsurancePolicyModel = Depends(_check_and_get_insurance_policy),
) -> str:
    """
    Get the reference code for the insurance policy.
    """
    return insurance_policy.ref_code  # type: ignore


@api_router.post("")
async def create(
    policy: InsurancePolicyCreate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:create"]),
    core_sc: CoreServiceContext = Security(CoreServiceContext.create),
) -> BaseResponse[InsurancePolicy]:
    """
    Create insurance policy.
    """
    policy.broker_id = sc.current_broker.id  # type: ignore
    policy.brokerage_id = sc.current_broker.brokerage_id  # type: ignore
    customer: CustomerModel = sc.customer_service.get_by_id(policy.customer_id)
    if not customer:
        raise UnauthorizedError(
            err_msg="Customer not found.",
            detail={
                "customer_id": policy.customer_id,
            },
        )
    if customer.broker_id != sc.current_broker.id:
        raise UnauthorizedError(
            err_msg="You are not authorized to access this customer.",
            detail={
                "customer_id": policy.customer_id,
            },
        )
    # in case only customer_id is provided, fill in the rest of the fields
    policy.email = policy.email or customer.email
    policy.customer_name = policy.customer_name or customer.name
    policy.customer_gender = policy.customer_gender or customer.gender  # type: ignore
    policy.phone = policy.phone or customer.phone or ""
    policy.province = policy.province or ""
    policy.city = policy.city or ""
    policy.address = policy.address or ""
    policy.postal_code = policy.postal_code or ""
    policy.premium = policy.premium or 0.0
    policy.source_type = InsurancePolicySourceType.MANUAL

    model = policy.to_model()
    model.ref_code = core_sc.reference_code_service.gen_code(ReferenceTypeEnum.INSURANCE_POLICY)
    model = sc.insurance_policy_service.create(model)
    return BaseResponse.ok(InsurancePolicy.from_model(model))


@api_router.get("/{id:int}")
async def get(
    _: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:view"]),
    insurance_policy: InsurancePolicy = Depends(_check_and_get_insurance_policy),
) -> BaseResponse[InsurancePolicy]:
    """
    Get insurance policy by ID.
    """
    return BaseResponse.ok(InsurancePolicy.from_model(insurance_policy))


@api_router.put("/{id:int}")
async def update(
    existed_policy: Annotated[InsurancePolicyModel, Depends(_check_and_get_insurance_policy)],
    policy: InsurancePolicyUpdate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:update"]),
) -> BaseResponse[InsurancePolicy]:
    """
    Update insurance policy by ID.
    """
    updated_policy = sc.insurance_policy_service.update(policy.to_model(existed_policy.id))  # type: ignore
    return BaseResponse.ok(InsurancePolicy.from_model(updated_policy))


@api_router.delete("/{id:int}")
async def delete(
    id: Annotated[int, Path(description="Insurance policy ID")],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:delete"]),
    _: InsurancePolicyModel = Depends(_check_and_get_insurance_policy),
) -> BaseResponse[InsurancePolicy]:
    """
    Delete insurance policy by ID.
    """
    model = sc.insurance_policy_service.mark_as_deleted(id)
    return BaseResponse.ok(InsurancePolicy.from_model(model))


@api_router.get("")
async def get_insurance_policies(
    query_params: Annotated[InsurancePolicyQueryParams, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:view"]),
) -> BaseResponse[Pagination[InsurancePolicy]]:
    """
    Get insurance policies by broker ID and optional filters.
    """
    filters = InsurancePolicyQueryFilters(
        **query_params.model_dump(exclude={"page_no", "page_size", "email"}, exclude_unset=True),
        email=(query_params.email.strip().lower() if query_params.email else query_params.email),
        broker_id=sc.current_broker.id,
    )
    total, insurance_policies = sc.insurance_policy_service.get_by_query_filters(
        filters,
        query_params.offset(),
        query_params.limit(),
    )
    data = Pagination[InsurancePolicy].from_items(
        InsurancePolicy.from_models(insurance_policies),
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)


def normalize_data(row: dict[str, Any]) -> dict[str, Any]:
    """
    Normalize the data from the row:
    1. Remove None values.
    2. Strip whitespace from string values.
    """
    return {
        key: value if not isinstance(value, str) else value.strip() for key, value in row.items() if value is not None
    }


@api_router.post("/import")
async def upload_insurance_policy(
    file: UploadFile = File(...),
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:create"]),
    core_sc: CoreServiceContext = Security(CoreServiceContext.create),
) -> BaseResponse[dict[str, int]]:
    """
    Upload insurance policy.
    """

    class ImportRow(BaseModel):
        """
        Model for a row in the insurance policy import file.
        """

        customer_name: Annotated[str, Field(alias="Name(*)", max_length=150)]
        email: Annotated[EmailStr, Field(alias="Email(*)", max_length=150)]
        phone: Annotated[str, Field(alias="Phone Number", default="", max_length=20)]
        province: Annotated[str, Field(alias="Province", default="", max_length=100)]
        city: Annotated[str, Field(alias="City", default="", max_length=100)]
        address: Annotated[str, Field(alias="Address", default="", max_length=200)]
        postal_code: Annotated[str, Field(alias="Postal Code", default="", max_length=20)]
        insurance_type: Annotated[InsuranceType, Field(alias="Insurance Type(*)")]
        policy_no: Annotated[str | None, Field(alias="Policy No", default=None, max_length=100)]
        premium: Annotated[float, Field(alias="Premium", default=0.0, ge=0, le=99999999.99)]
        start_date: Annotated[date, Field(alias="Effective Date(*)")]
        end_date: Annotated[date, Field(alias="Expire Date(*)")]

    counter = 0
    async for row in iter_rows_from_upload_file(
        file,
        expected_headers=[field.alias for field in ImportRow.model_fields.values()],
    ):
        try:
            import_row = ImportRow(**normalize_data(row))
        except ValidationError as e:
            err_msg = [f"Column: {err['loc'][0]}, Message: {err['msg']}, Type: {err['type']}" for err in e.errors()]
            raise DataValidationError(
                message=f"Data validation error, detail info: row:{counter + 2}, {', '.join(err_msg)}",
            )
        customer_example = CustomerModel(
            broker_id=sc.current_broker.id,
            name=import_row.customer_name,
            email=import_row.email.lower(),
        )
        customer = sc.customer_service.get_by_example(customer_example, 0, 1)
        if not customer:
            customer_example.phone = import_row.phone
            customer_example.province = import_row.province
            customer_example.city = import_row.city
            customer_example.address = import_row.address
            customer_example.postal_code = import_row.postal_code
            customer = sc.customer_service.create(customer_example)
        else:
            customer = customer[0]
        insurance_policy = InsurancePolicyModel(
            customer_id=customer.id,
            broker_id=sc.current_broker.id,
            brokerage_id=sc.current_broker.brokerage_id,
            source_type=InsurancePolicySourceType.IMPORTED,
            ref_code=core_sc.reference_code_service.gen_code(ReferenceTypeEnum.INSURANCE_POLICY),
            email=import_row.email.lower(),
            **import_row.model_dump(exclude={"email"}),
        )
        sc.insurance_policy_service.create(insurance_policy)
        counter += 1
    return BaseResponse.ok(
        data={"count": counter},
        message=f"Successfully imported {counter} insurance policies.",
    )


@api_router.post(
    "/{id:int}/e-docs/{edoc_type}",
    summary="upload e-doc file for insurance policy",
)
async def upload_edoc(
    ref_code: Annotated[str, Depends(_get_ref_code)],
    edoc_type: Annotated[EdocType, Path(description="e-doc file type")],
    file: Annotated[UploadFile, File(description="e-doc file")],
    _: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:update"]),
) -> BaseResponse[dict[str, str]]:
    upload_file = await EdocFileHelper.save(ref_code, edoc_type, file)
    return BaseResponse.ok(upload_file)


@api_router.get(
    "/{id:int}/e-docs/files",
)
async def get_edocs(
    ref_code: Annotated[str, Depends(_get_ref_code)],
    _: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:view"]),
) -> BaseResponse[dict[EdocType, list[str]]]:
    edoc_files = EdocFileHelper.list_files(ref_code)
    return BaseResponse.ok(edoc_files)


@api_router.get(
    "/{id:int}/e-docs/{edoc_type}/{filename}",
)
async def get_edoc(
    ref_code: Annotated[str, Depends(_get_ref_code)],
    edoc_type: Annotated[EdocType, Path(description="e-doc file type")],
    filename: Annotated[str, Path(description="e-doc file name")],
    _: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:view"]),
):
    return FileResponse(
        EdocFileHelper.full_path(ref_code, edoc_type, filename),
        filename=filename,
        media_type="application/octet-stream",
    )


@api_router.delete(
    "/{id:int}/e-docs/{edoc_type}/{filename}",
)
async def delete_edoc(
    ref_code: Annotated[str, Depends(_get_ref_code)],
    edoc_type: Annotated[EdocType, Path(description="e-doc file type")],
    filename: Annotated[str, Path(description="e-doc file name")],
    _: ServiceContext = Security(ServiceContext.create, scopes=["insurance:policy:update"]),
) -> BaseResponse[None]:

    EdocFileHelper.delete(
        uid=ref_code,
        edoc_type=edoc_type,
        filename=filename,
    )
    return BaseResponse.ok(None)

from typing import Annotated

from fastapi import APIRouter
from fastapi import Depends
from fastapi import Query
from fastapi import Security

from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import PageParams
from bethune.api.dto.base import Pagination
from bethune.api.dto.reminder import ReminderConfig
from bethune.api.dto.reminder import ReminderConfigCreate
from bethune.api.dto.reminder import ReminderConfigCreateOrUpdate
from bethune.api.dto.reminder import ReminderConfigList
from bethune.api.dto.reminder import ReminderMessage
from bethune.api.dto.reminder import ReminderMessageCreate
from bethune.api.dto.reminder import ReminderMessageList
from bethune.api.dto.reminder import ReminderMessageQuery
from bethune.api.endpoint.insurance.service_context import ServiceContext

api_router = APIRouter(prefix="/reminder", tags=["reminder"])


@api_router.post("/config", response_model=BaseResponse[ReminderConfig])
async def _(
    config_create: ReminderConfigCreate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:reminder:create"]),
):
    config_model = config_create.to_model(broker_id=sc.current_broker.id)  # type: ignore
    created_config = sc.reminder_config_service.create_or_update(config_model)
    return BaseResponse.ok(ReminderConfig.from_model(created_config))


@api_router.post("/config/batch", response_model=BaseResponse[list[ReminderConfig]])
async def _(
    config_create_or_updates: list[ReminderConfigCreateOrUpdate],
    sc: ServiceContext = Security(
        ServiceContext.create, scopes=["insurance:reminder:create", "insurance:reminder:update"]
    ),
):
    persisted_configs = []
    for config in config_create_or_updates:
        if config.id is not None:
            config = sc.reminder_config_service.create_or_update(config.to_update_model(broker_id=sc.current_broker.id))  # type: ignore
            persisted_configs.append(ReminderConfig.from_model(config))
        else:
            config = sc.reminder_config_service.create_or_update(config.to_create_model(broker_id=sc.current_broker.id))  # type: ignore
            persisted_configs.append(ReminderConfig.from_model(config))
    return BaseResponse.ok(persisted_configs)


@api_router.get("/config/{id}", response_model=BaseResponse[ReminderConfig])
async def _(id: int, sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:reminder:view"])):
    broker_id: int = sc.current_broker.id  # type: ignore
    config = sc.reminder_config_service.get_by_id_and_broker(id, broker_id)
    return BaseResponse.ok(ReminderConfig.from_model(config))


@api_router.get("/config", response_model=BaseResponse[list[ReminderConfigList]])
async def _(sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:reminder:view"])):
    broker_id: int = sc.current_broker.id  # type: ignore
    configs = sc.reminder_config_service.get_by_broker(broker_id)
    return BaseResponse.ok(ReminderConfigList.from_models(configs))


@api_router.get("/message", response_model=BaseResponse[Pagination[ReminderMessageList]])
async def get_messages(
    page_params: Annotated[PageParams, Query()],
    example: Annotated[ReminderMessageQuery, Depends()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:reminder:view"]),
) -> BaseResponse[Pagination[ReminderMessageList]]:
    example.receiver_id = sc.current_broker.id
    total, messages = sc.reminder_message_service.paged_list(
        example=example.to_model(), offset=page_params.offset(), limit=page_params.limit()
    )

    if total > 0:
        await sc.reminder_message_service.mark_messages_as_read(sc.current_broker.id)  # type: ignore

    data = Pagination[ReminderMessageList].from_items(
        ReminderMessageList.from_models(messages),
        total,
        page_params.page_no,
        page_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.get("/message/unread-count", response_model=BaseResponse[int])
async def _(sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:reminder:view"])):
    count = sc.reminder_message_service.get_unread_count(receiver_id=sc.current_broker.id)  # type: ignore
    return BaseResponse.ok(count)


@api_router.post("/message", response_model=BaseResponse[ReminderMessage])
async def _(
    message_create: ReminderMessageCreate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:reminder:create"]),
):
    created_message = sc.reminder_message_service.create(message_create.to_model())
    return BaseResponse.ok(ReminderMessage.from_model(created_message))


@api_router.patch("/message/{id}/read", response_model=BaseResponse[ReminderMessage])
async def mark_as_read(
    id: int, sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:reminder:view"])
):
    message = sc.reminder_message_service.mark_message_as_read(id, sc.current_broker.id)  # type: ignore
    return BaseResponse.ok(ReminderMessage.from_model(message))

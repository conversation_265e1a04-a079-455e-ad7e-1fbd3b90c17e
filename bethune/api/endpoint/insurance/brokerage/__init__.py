from fastapi import APIRouter

from .company import api_router as company_router
from .personnel import api_router as personnel_router
from bethune.api.endpoint.insurance.insurance_application import (
    api_router_for_brokerage as insurance_application_router,
)

api_router = APIRouter(prefix="/brokerage")

api_router.include_router(insurance_application_router)
api_router.include_router(company_router)
api_router.include_router(personnel_router)

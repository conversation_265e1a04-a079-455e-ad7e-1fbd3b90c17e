from typing import Annotated
from typing import Any

from fastapi import APIRouter
from fastapi import BackgroundTasks
from fastapi import Depends
from fastapi import Query
from fastapi import Security
from sqlalchemy.exc import NoResultFound

from bethune.api.dto import Pagination
from bethune.api.dto.auth import OAuth2PasswordRequestJSON
from bethune.api.dto.auth import SendEmailVerificationRequest
from bethune.api.dto.auth import Token
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.broker import BrokerCreateForBrokerage
from bethune.api.dto.brokerage_user import BrokerageUserBatchEnabledUpdatingRequest
from bethune.api.dto.brokerage_user import BrokerageUserChangePasswordRequestBase
from bethune.api.dto.brokerage_user import BrokerageUserCreatingRequest
from bethune.api.dto.brokerage_user import BrokerageUserListQueryParams
from bethune.api.dto.brokerage_user import BrokerageUserMeChangePasswordRequest
from bethune.api.dto.brokerage_user import BrokerageUserResetPasswordRequest
from bethune.api.dto.brokerage_user import BrokerageUserResponse
from bethune.api.dto.brokerage_user import BrokerageUserSummaryInListResponse
from bethune.api.dto.brokerage_user import BrokerageUserUpdatingRequest
from bethune.api.dto.dict.brokerage import BrokerageUserRoleEnum
from bethune.api.dto.user import UserCreate
from bethune.api.endpoint.core.service_context import ServiceContext as CoreServiceContext
from bethune.api.endpoint.insurance.service_context import ServiceContext as InsuranceServiceContext
from bethune.api.endpoint.system.service_context import ServiceContext as SystemServiceContext
from bethune.api.error import UnauthenticatedError
from bethune.error import NotFoundError
from bethune.error import PasswordNotMatchError
from bethune.model import BrokerageUser as BrokerageUserModel
from bethune.model import User as UserModel
from bethune.model import UserStatus as ModelUserStatus
from bethune.model import UserType as ModelUserType
from bethune.model.system import ROLE_BROKER_ID
from bethune.model.system import ROLE_BROKER_SUPPORT_ID
from bethune.service.core.ref_code import ReferenceTypeEnum
from bethune.util import hash_password

api_router = APIRouter(prefix="/personnel", tags=["brokerage", "personnel"])


def _create_system_user_and_brokerage_user(
    brokerage_user: BrokerageUserCreatingRequest,
    role_id: int,
    sc: InsuranceServiceContext,
    sc_system: SystemServiceContext,
) -> tuple[UserModel, BrokerageUserModel]:
    user_create = UserCreate(
        name=brokerage_user.name,
        email=brokerage_user.email,
        password=brokerage_user.password,
        user_type=ModelUserType.SAAS,
        status=ModelUserStatus.ACTIVE,
        mobile=brokerage_user.phone,
    )
    created_user = sc_system.user_service.create(user_create.to_model())
    sc_system.user_service.set_roles(created_user.id, [role_id])  # type: ignore
    created_brokerage_user = sc.brokerage_user_service.create(
        brokerage_user.to_model(created_user, sc.current_brokerage_user.brokerage)
    )
    return created_user, created_brokerage_user


def _create_broker_personnel(
    brokerage_user: BrokerageUserCreatingRequest,
    sc: InsuranceServiceContext,
    sc_system: SystemServiceContext,
    core_system: CoreServiceContext,
) -> BrokerageUserResponse:
    """
    用BrokerageUserCreatingRequest创建Broker
    :param brokerage_user:
    :param sc_system:
    :param sc:
    :return: 代理人
    """
    created_user, created_brokerage_user = _create_system_user_and_brokerage_user(
        brokerage_user, ROLE_BROKER_ID, sc, sc_system
    )
    broker = BrokerCreateForBrokerage(
        user_id=created_user.id,
        brokerage_id=created_brokerage_user.brokerage_id,
        name=brokerage_user.name,
        address=brokerage_user.address,
        province=brokerage_user.province,
        city=brokerage_user.city,
        postal_code=brokerage_user.postal_code if brokerage_user.postal_code else "",
        phone=brokerage_user.phone,
        email=brokerage_user.email,
        password=brokerage_user.password,
    ).to_model(
        created_user.id, core_system.reference_code_service.gen_code(ReferenceTypeEnum.BROKER)  # type: ignore
    )
    sc.broker_service.create_broker(
        broker,
        is_qualified=True,
    )
    return BrokerageUserResponse.from_model(created_brokerage_user)


def _create_support_personnel(
    brokerage_user: BrokerageUserCreatingRequest,
    sc: InsuranceServiceContext,
    sc_system: SystemServiceContext,
) -> BrokerageUserResponse:
    """
    用BrokerageUserCreatingRequest只创建BrokerageUser
    :param brokerage_user:
    :param sc_system:
    :param sc:
    :return: 内勤
    """
    created_user, created_brokerage_user = _create_system_user_and_brokerage_user(
        brokerage_user, ROLE_BROKER_SUPPORT_ID, sc, sc_system
    )
    return BrokerageUserResponse.from_model(created_brokerage_user)


def _change_password(
    change_password: BrokerageUserChangePasswordRequestBase,
    user: UserModel,
    sc_system: SystemServiceContext,
) -> UserModel:
    try:
        return sc_system.auth_service.change_password(
            user.email, change_password.origin_password, change_password.new_password
        )
    except UnauthenticatedError as e:
        raise PasswordNotMatchError(
            message="Password not match",
            detail={"email": user.email},
        ) from e


@api_router.post(
    "",
    summary="create a new brokerage user",
    response_model=BaseResponse[BrokerageUserResponse],
)
async def _(
    brokerage_user: BrokerageUserCreatingRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:personnel:create"]),
    sc_system: SystemServiceContext = Security(SystemServiceContext.create, scopes=["brokerage:personnel:create"]),
    sc_core: CoreServiceContext = Security(CoreServiceContext.create, scopes=["brokerage:personnel:create"]),
) -> BaseResponse[BrokerageUserResponse]:
    await sc_system.user_service.check_user_exists(brokerage_user.email)
    personnel = (
        _create_broker_personnel(brokerage_user, sc, sc_system, sc_core)
        if brokerage_user.user_role == BrokerageUserRoleEnum.BROKER
        else _create_support_personnel(brokerage_user, sc, sc_system)
    )
    return BaseResponse.ok(personnel)


@api_router.post(
    "/login",
    response_model=BaseResponse[Token],
    description="It returns BaseResponse Object just as other endpoints.",
)
async def _(
    sc_system: Annotated[SystemServiceContext, Depends()],
    sc: Annotated[InsuranceServiceContext, Depends()],
    json_data: OAuth2PasswordRequestJSON,
):
    user: UserModel = sc_system.auth_service.authenticate_user(json_data.username, json_data.password)  # type: ignore
    try:
        sc.brokerage_user_service.get_by_user_id(user.id)  # type: ignore
        if user.is_broker_role:
            raise UnauthenticatedError(err_msg="this type of user cannot log on this site")
    except (NoResultFound, NotFoundError):
        raise UnauthenticatedError(err_msg="user is not a brokerage user")
    return BaseResponse[Token].ok(sc_system.auth_service.create_token_by_user(user))


@api_router.get(
    "/{id:int}",
    summary="get brokerage user info by id",
    response_model=BaseResponse[BrokerageUserResponse],
)
async def _(
    id: int,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:personnel:view"]),
) -> BaseResponse[BrokerageUserResponse]:
    personnel = sc.brokerage_user_service.get_by_id(id)
    if personnel.brokerage_id != sc.current_brokerage_user.brokerage_id:
        raise NotFoundError(message="brokerage user not found")
    broker = None
    if personnel.user.is_broker_role:
        broker = sc.broker_service.get_by_user_id(personnel.user_id)
    return BaseResponse.ok(BrokerageUserResponse.from_model(personnel, broker))


@api_router.get(
    "",
    summary="get brokerage users by query condition",
    response_model=BaseResponse[Pagination[BrokerageUserSummaryInListResponse]],
)
async def _(
    query_params: BrokerageUserListQueryParams = Query(),
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:personnel:view"]),
) -> BaseResponse[Pagination[BrokerageUserSummaryInListResponse]]:
    filters = query_params.to_filters(sc.current_brokerage_user.brokerage_id)
    total, personnel = sc.brokerage_user_service.get_by_query_filters(
        filters,
        query_params.offset(),
        query_params.limit(),
    )
    data = Pagination[BrokerageUserSummaryInListResponse].from_items(
        BrokerageUserSummaryInListResponse.from_models(personnel),
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.patch(
    "/me/change_password",
    summary="change my password",
    response_model=BaseResponse[Any],
)
async def _(
    change_password: BrokerageUserMeChangePasswordRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
    sc_system: SystemServiceContext = Security(SystemServiceContext.create),
) -> BaseResponse[Any]:
    return BaseResponse.ok(_change_password(change_password, sc.current_user, sc_system))


@api_router.post("/reset_password_verification_code")
async def _(
    core_sc: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
    verification_request: SendEmailVerificationRequest,
    background_tasks: BackgroundTasks,
):
    if await sc_system.user_service.check_user_exists_no_error(str(verification_request.email)) is True:
        background_tasks.add_task(
            core_sc.verification_code_service.send_verification_email, str(verification_request.email), 4, True
        )
    return BaseResponse.ok(verification_request.email)


@api_router.patch("/reset_password", summary="Reset password", response_model=BaseResponse)
async def _(
    reset_password: BrokerageUserResetPasswordRequest,
    core_sc: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
) -> BaseResponse:
    await core_sc.verification_code_service.verify_verification_code(
        reset_password.email, reset_password.verification_code
    )
    user = sc_system.user_service.get_by_email(reset_password.email)
    user.password = hash_password(reset_password.new_password)
    user_updated = sc_system.user_service.update(user)
    if user_updated:
        await core_sc.verification_code_service.delete_verification_code(reset_password.email)
    return BaseResponse.ok(True)


@api_router.patch(
    "/enabled_status",
    summary="change the enabled status of specific user list",
    response_model=BaseResponse[int],
)
async def batch_update_personnel_enabled_status(
    enabled_status: BrokerageUserBatchEnabledUpdatingRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:personnel:edit"]),
) -> BaseResponse[int]:
    """
    批量更新指定ID列表的人员启用状态
    - 只能更新当前经纪公司下的人员
    - 需要 brokerage:personnel:edit 权限
    """
    # 验证所有ID都属于当前经纪公司
    valid = sc.brokerage_user_service.validate_brokerage_user_ids(
        enabled_status.ids, sc.current_brokerage_user.brokerage_id
    )
    if not valid:
        raise NotFoundError(
            message="部分人员ID不属于当前经纪公司",
        )

    # 批量更新启用状态
    return BaseResponse.ok(
        sc.brokerage_user_service.batch_update_enabled_status(
            ids=enabled_status.ids, enabled=enabled_status.enabled, brokerage_id=sc.current_brokerage_user.brokerage_id
        )
    )


@api_router.put(
    "/{id}",
    summary="update brokerage user by id",
    response_model=BaseResponse[BrokerageUserResponse],
)
async def _(
    id: int,
    update_request: BrokerageUserUpdatingRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:personnel:edit"]),
    sc_system: SystemServiceContext = Security(SystemServiceContext.create, scopes=["brokerage:personnel:edit"]),
) -> BaseResponse[BrokerageUserResponse]:
    # 验证当前操作的经纪行用户属于当前经纪公司
    brokerage_user = sc.brokerage_user_service.get_by_id(id)
    if brokerage_user.brokerage_id != sc.current_brokerage_user.brokerage_id:
        raise NotFoundError(message="brokerage user not found")

    # 更新BrokerageUser表
    sc.brokerage_user_service.update(update_request.to_model(id))

    # 获取关联的User
    user = sc_system.user_service.get_by_id(brokerage_user.user_id)
    if update_request.name is not None or update_request.phone is not None or update_request.password is not None:
        user.sqlmodel_update(
            update_request.model_dump(exclude_unset=True, exclude_none=True, exclude={"password"})
            | ({"mobile": update_request.phone} if update_request.phone else {})
            | ({"password": hash_password(update_request.password)} if update_request.password else {}),
        )
        sc_system.user_service.update(user)

    # 检查用户是否是经纪人角色
    user_roles = {role.id for role in user.roles}
    is_broker = ROLE_BROKER_ID in user_roles

    # 如果是经纪人，更新Broker表
    if is_broker:
        try:
            broker = sc.broker_service.get_by_user_id(user.id)  # type: ignore
            broker.sqlmodel_update(update_request.model_dump(exclude_unset=True, exclude_none=True))  # type: ignore
            sc.broker_service.update(broker)  # type: ignore
        except NotFoundError:
            # 处理经纪人记录不存在的情况
            raise NotFoundError(message="the broker of brokerage not found")

    # 返回更新后的完整信息
    updated_brokerage_user = sc.brokerage_user_service.get_by_id(id)
    return BaseResponse.ok(BrokerageUserResponse.from_model(updated_brokerage_user))

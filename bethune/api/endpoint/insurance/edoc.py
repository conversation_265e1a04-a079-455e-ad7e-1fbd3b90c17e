import os
from pathlib import Path

from fastapi import UploadFile

from bethune.api.dto.insurance_application import EdocType
from bethune.error.errors import NotFoundError
from bethune.settings import settings


def _get_edoc_dir(uid: str, edoc_type: EdocType) -> Path:
    return Path.joinpath(
        Path(settings.DATA_DIR),
        (settings.ELECTRONIC_POLICY_FOLDER if edoc_type == EdocType.POLICY else settings.ELECTRONIC_QUOTE_FOLDER),
        uid,
    )


def _get_file_path(uid: str, edoc_type: EdocType, filename: str) -> Path:
    file_path = _get_edoc_dir(uid, edoc_type) / filename
    if not file_path.exists():
        raise NotFoundError(
            message="File does not exist", detail={"uid": uid, "edoc_type": edoc_type, "filename": filename}
        )
    return file_path


def _list_files_for_type(uid: str, edoc_type: EdocType) -> list[str]:
    folder_path = _get_edoc_dir(uid, edoc_type)
    if not os.path.exists(folder_path):
        return []

    files = os.listdir(folder_path)
    if not files:
        return []
    return files


class EdocFileHelper:

    @staticmethod
    async def save(uid: str, edoc_type: EdocType, file: UploadFile):
        edoc_dir = _get_edoc_dir(uid, edoc_type)
        edoc_dir.mkdir(parents=True, exist_ok=True)
        file_path = edoc_dir / file.filename
        with open(file_path, "wb") as buffer:  # if file already exists, it will be overwritten
            content = await file.read()
            buffer.write(content)
        return {"filename": file.filename}

    @staticmethod
    def delete(uid: str, edoc_type: EdocType, filename: str):
        _get_file_path(uid, edoc_type, filename).unlink()

    @staticmethod
    def list_files(uid: str, edoc_type: EdocType | None = None) -> dict[EdocType, list[str]]:
        edoc_types = [edoc_type] if edoc_type else EdocType
        return {edoc_type: _list_files_for_type(uid, edoc_type) for edoc_type in edoc_types}

    @staticmethod
    def full_path(uid: str, edoc_type: EdocType, filename: str) -> Path:
        return _get_file_path(uid, edoc_type, filename)

    @staticmethod
    def copy_edocs(edoc_type: EdocType, from_uid: str, to_uid: str):
        from_path = _get_edoc_dir(from_uid, edoc_type)
        if not from_path.exists():
            return
        to_path = _get_edoc_dir(to_uid, edoc_type)
        to_path.mkdir(parents=True, exist_ok=True)

        for file in from_path.iterdir():
            if file.is_file():
                new_file_path = to_path / file.name
                with open(new_file_path, "wb") as buffer:
                    buffer.write(file.read_bytes())

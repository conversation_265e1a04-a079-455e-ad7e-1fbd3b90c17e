from typing import Any

from ..edoc import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ..edoc import EdocType
from bethune.api.dto.insurance_application import (
    InsuranceApplication as InsuranceApplicationDTO,
)
from bethune.api.dto.insurance_application import InsuranceApplicationList
from bethune.api.endpoint.insurance.service_context import ServiceContext
from bethune.logging import logger
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.model.insurance import InsurancePolicy
from bethune.model.payment import PaymentStatusEnum
from bethune.service.core.ref_code import ReferenceCodeService
from bethune.service.core.ref_code import ReferenceTypeEnum


def create_referral_fee_payment(
    sc: ServiceContext,
    application: InsuranceApplication,
):
    if not sc.insurance_application_service.is_lead_application(application):  # type: ignore
        return
    lead = sc.lead_service.get_by_id(sc.lead_application_service.get_by_application_id(application.id).lead_id)  # type: ignore
    sc.lead_referral_fee_payment_service.create_referral_fee_payment(
        lead,
        application,
    )


def create_policy_from_application(
    sc: ServiceContext,
    application: InsuranceApplication,
) -> InsurancePolicy:
    """
    Create a policy from the given insurance application.
    """
    insurance_policy = InsurancePolicy.from_application(application)  # type: ignore
    insurance_policy.ref_code = ReferenceCodeService().gen_code(ReferenceTypeEnum.INSURANCE_POLICY)
    insurance_policy = sc.insurance_policy_service.create(insurance_policy)  # type: ignore
    if application.serial_number is not None and insurance_policy.ref_code is not None:
        EdocFileHelper.copy_edocs(
            edoc_type=EdocType.POLICY, from_uid=application.serial_number, to_uid=insurance_policy.ref_code
        )  # type: ignore
    else:
        logger.warning(
            "Application[%s]'s serial number or policy[%s]'s reference code is None, cannot copy edocs.",
            application.id,
            insurance_policy.id,
        )
    return insurance_policy


def enrich_lead_info(
    sc: ServiceContext,
    applications: list[InsuranceApplicationList | InsuranceApplication],
) -> list[InsuranceApplicationList | InsuranceApplication]:
    lead_applications = sc.lead_application_service.get_by_application_ids(
        [application.id for application in applications if application.id is not None]
    )
    if lead_applications:
        lead_application_dicts = {
            lead_application.application_id: lead_application.lead_id for lead_application in lead_applications
        }

        payments = sc.lead_referral_fee_payment_service.get_by_lead_ids(list(lead_application_dicts.values()))
        payment_dicts = {payment.lead_id: payment for payment in payments}

        for application in applications:
            lead_id = lead_application_dicts.get(application.id)  # type: ignore
            if lead_id is not None:
                application.is_lead_application = True
                if application.status == InsuranceApplicationStatus.UNDERWRITTEN:
                    payment = payment_dicts.get(lead_id)
                    if payment and payment.payment_status != PaymentStatusEnum.PAID:
                        application.pending_lead_payment = True

    return applications


def enrich_broker_info(
    sc: ServiceContext,
    applications: list[InsuranceApplicationList | InsuranceApplication],
) -> list[InsuranceApplicationList | InsuranceApplication]:
    broker_ids = {application.broker_id for application in applications if application.broker_id is not None}
    brokers = sc.broker_service.get_by_ids(list(broker_ids))
    broker_dicts = {broker.id: broker for broker in brokers}

    for application in applications:
        if application.broker_id in broker_dicts:
            application.broker_name = broker_dicts[application.broker_id].name

    return applications


def to_insurance_application_dto(sc: ServiceContext, application: InsuranceApplication) -> InsuranceApplicationDTO:
    dto = InsuranceApplicationDTO.from_model(application)
    dto = enrich_lead_info(sc, [dto])[0]
    return enrich_broker_info(sc, [dto])[0]


def dump_application_to_json(
    sc: ServiceContext,
    new_data: dict[str, Any] | None,
    application: InsuranceApplication,
) -> dict[str, Any]:
    """
    Convert an InsuranceApplication to a JSON string.
    """
    dto = to_insurance_application_dto(sc, application)
    return sc.insurance_application_service.merge_json(
        application.id,  # type: ignore
        {
            **{"insurance_application": dto.model_dump(mode="json")},
            **(new_data if new_data else {}),
        },
    )

from typing import Annotated

from fastapi import APIRouter
from fastapi import Path
from fastapi.responses import RedirectResponse

from bethune.db.redis import get_redis
from bethune.error.errors import NotFoundError
from bethune.logging import logger


api_router = APIRouter(prefix="/share-link/ia")


@api_router.get("/{serial_number:str}")
async def redirect_to_shared_link(
    serial_number: Annotated[str, Path(description="Insurance application serial number")],
) -> RedirectResponse:
    """
    Redirect to the shared link of an insurance application.
    """
    shared_link = await get_redis().get(f"bethune:insurance_application:short_share_link:{serial_number}")
    logger.info(f"Shared link for {serial_number}: {shared_link}")
    if not shared_link:
        return NotFoundError(message="Shared link not found", detail={"serial_number": serial_number})

    return RedirectResponse(url=shared_link.decode("utf-8"))

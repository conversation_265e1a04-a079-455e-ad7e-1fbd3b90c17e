import json
from functools import partial

from .helper import create_policy_from_application
from .helper import create_referral_fee_payment
from .helper import dump_application_to_json
from bethune.api.dto.base import BusinessType
from bethune.api.dto.reminder import ReminderMessageCreate
from bethune.api.endpoint.insurance.service_context import ServiceContext
from bethune.model.base import InsuranceType
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.model.reminder import ReminderMessage
from bethune.service.insurance.application_state_machine import ApplicationStateMachine
from bethune.service.insurance.application_state_machine import OperatorRole
from bethune.service.insurance.application_state_machine import StateListener
from bethune.service.insurance.application_state_machine import TriggerEnum
from bethune.util.date import format_date


def application_state_transition(
    application: InsuranceApplication,
    sc: ServiceContext,
    trigger: TriggerEnum,
    new_data: dict | None = None,
) -> bool:
    if sc.current_user.is_broker_role:
        operator = OperatorRole.BROKER
    elif sc.current_user.is_broker_support_role or sc.current_user.is_brokerage_admin_role:
        operator = OperatorRole.BROKER_SUPPORT
        __send_trigger_notification(sc, application, trigger)
    else:
        raise ValueError("Invalid user role for application state transition")

    return ApplicationStateMachine.create_state_machine(
        application=application,
        is_lead_application=sc.insurance_application_service.is_lead_application(application),  # type: ignore
        operator=operator,
        listeners=__create_state_listeners(sc, new_data),
    ).trigger(
        trigger
    )  # type: ignore


def __create_state_listeners(
    sc: ServiceContext,
    new_data: dict | None = None,
) -> dict[InsuranceApplicationStatus, list[StateListener]]:
    special_listeners: dict[InsuranceApplicationStatus, list[StateListener]] = {
        InsuranceApplicationStatus.UNDERWRITTEN: [
            partial(
                create_policy_from_application,
                sc,
            ),
            partial(
                create_referral_fee_payment,
                sc,
            ),
        ],
    }
    return {
        status: [
            sc.insurance_application_service.update,
            *special_listeners.get(status, []),
            partial(
                dump_application_to_json,
                sc,
                new_data,
            ),
        ]
        for status in InsuranceApplicationStatus
    }


def __dispatch_notification(
    sc: ServiceContext,
    receiver_id: int,
    insurance_type: InsuranceType,
    business_type: BusinessType,
    message: str,
    extra_content: dict,
) -> ReminderMessage:
    return sc.reminder_message_service.create(
        ReminderMessageCreate(
            business_type=business_type,
            insurance_type=insurance_type,
            content=message,
            extra_content=json.dumps(extra_content),
            receiver_id=receiver_id,
            sender_id=0,  # Default to system message
        ).to_model()
    )


def __send_trigger_notification(sc: ServiceContext, application: InsuranceApplication, trigger: TriggerEnum):
    triggers = {
        TriggerEnum.UNDERWRITE: (
            "UNDERWRITTEN",
            "确认承保",
            BusinessType.POLICY_ISSUED,
            f"保费 {str(application.premium if application.premium else 0)}，承保周期：{format_date(application.start_date) if application.start_date else "-"}~{format_date(application.end_date) if application.end_date else "-"}",  # type: ignore
        ),
        TriggerEnum.QUOTE: (
            "QUOTE",
            "完成算费",
            BusinessType.PREMIUM_CALCULATED,
            f"保费金额 {str(application.premium if application.premium else 0)}，请及时查收并通知客户",
        ),
        TriggerEnum.REJECT: ("REJECT", "退回", BusinessType.POLICY_REJECT, "请及时查阅退回修改意见"),
    }

    if trigger in triggers:
        _, trigger_name, message_business_type, trigger_message = triggers[trigger]
        __dispatch_notification(
            sc=sc,
            receiver_id=application.broker_id,
            insurance_type=application.insurance_type,
            business_type=message_business_type,
            message=f"{application.customer_name}的投保单已由内勤{trigger_name}，{trigger_message}",
            extra_content={"customer_name": application.customer_name},
        )

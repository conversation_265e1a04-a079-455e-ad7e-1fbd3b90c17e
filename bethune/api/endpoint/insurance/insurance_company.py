from typing import Annotated

from fastapi import APIRouter
from fastapi import Depends

from bethune.api.dto.base import BaseResponse
from bethune.api.dto.insurance_company import InsuranceCompany
from bethune.api.dto.insurance_company import InsuranceCompanyList
from bethune.api.endpoint.insurance.service_context import ServiceContext


api_router = APIRouter(prefix="/insurance-company", tags=["insurance-company"])


@api_router.get("", summary="get insurance companies", response_model=BaseResponse[InsuranceCompanyList])
async def _(sc: Annotated[ServiceContext, Depends()]):
    return BaseResponse.ok(InsuranceCompanyList.from_models(sc.insurance_company_service.get_all()))


@api_router.get("/{id}", summary="get insurance company by id", response_model=BaseResponse[InsuranceCompany])
async def _(id: int, sc: Annotated[ServiceContext, Depends()]):
    return BaseResponse.ok(InsuranceCompanyList.from_model(sc.insurance_company_service.get_by_id(id)))

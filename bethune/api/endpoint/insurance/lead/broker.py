from fastapi import APIRouter
from fastapi import Security
from fastapi.params import Query

from bethune.api.dto import Pagination
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.broker import CandidateBrokerListForCreatingLead
from bethune.api.dto.lead import CandidateBrokerListForCreatingLeadQueryParams
from bethune.api.endpoint.insurance.service_context import ServiceContext as InsuranceServiceContext
from bethune.api.error import MissingRequiredParametersError
from bethune.api.error import UnauthorizedError

api_router = APIRouter(prefix="/candidate_broker_assignee", tags=["lead", "broker"])


@api_router.get(
    "",
    summary="get candidate broker list for creating lead",
    response_model=BaseResponse[Pagination[CandidateBrokerListForCreatingLead]],
)
async def _(
    query_params: CandidateBrokerListForCreatingLeadQueryParams = Query(),
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[Pagination[CandidateBrokerListForCreatingLead]]:
    if query_params.lead_id:
        lead = sc.lead_service.get_by_id(query_params.lead_id)
        if lead.created_by != sc.current_broker.id:
            raise UnauthorizedError("The Lead is not from the logged in person")
        insurance_type = lead.insurance_type
        customer_province = lead.customer_province
        customer_city = lead.customer_city or ""
    else:
        missing_params = []
        if not query_params.insurance_type:
            missing_params.append("insurance_type")
        if not query_params.customer_province:
            missing_params.append("customer_province")
        if not query_params.customer_city:
            missing_params.append("customer_city")

        if missing_params:
            raise MissingRequiredParametersError(
                f"Missing required parameters for broker search: {', '.join(missing_params)}. "
                "These parameters are required when lead_id is not provided."
            )

        insurance_type = query_params.insurance_type  # type: ignore
        customer_province = query_params.customer_province  # type: ignore
        customer_city = query_params.customer_city  # type: ignore

    total, brokers, referer_broker = sc.broker_service.find_candidate_brokers_for_creating_lead(
        sc.current_broker,
        insurance_type,
        customer_province,
        customer_city,
        query_params.keyword,
        query_params.page_no,
        query_params.page_size,
        query_params.offset(),
        query_params.limit(),
    )
    data = Pagination[CandidateBrokerListForCreatingLead].from_items(
        CandidateBrokerListForCreatingLead.from_models(brokers, referer_broker is not None),
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)

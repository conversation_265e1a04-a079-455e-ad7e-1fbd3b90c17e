import uuid

from fastapi import APIRouter
from fastapi import BackgroundTasks
from fastapi import Query
from fastapi import Security

from bethune.api.dto import Pagination
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import GenderEnum
from bethune.api.dto.insurance_application import InsuranceApplication
from bethune.api.dto.insurance_application import InsuranceApplicationCreate
from bethune.api.dto.insurance_application import InsuranceApplicationStatus
from bethune.api.dto.insurance_application import InsuranceType
from bethune.api.dto.lead import LeadAssignRequest
from bethune.api.dto.lead import LeadCreatingRequest
from bethune.api.dto.lead import LeadListQueryParams
from bethune.api.dto.lead import LeadResponse
from bethune.api.dto.lead import LeadRichInfoListResponse
from bethune.api.dto.lead import LeadUpdatingRequest
from bethune.api.endpoint.core.service_context import (
    ServiceContext as CoreServiceContext,
)
from bethune.api.endpoint.insurance.service_context import (
    ServiceContext as InsuranceServiceContext,
)
from bethune.api.error import UnauthorizedError
from bethune.error import DataValidationError
from bethune.model import Broker
from bethune.model import Customer
from bethune.model.base import InsuranceType as ModelInsuranceType
from bethune.model.base import ReferralFeeTypeEnum
from bethune.model.lead import Lead as LeadModel
from bethune.model.lead import LeadApplication as LeadApplicationModel
from bethune.model.lead import LeadQueryFilters
from bethune.model.lead import LeadStatusEnum
from bethune.settings import settings

api_router = APIRouter(prefix="/lead", tags=["lead"])


def _modify_lead_status(
    lead: LeadModel,
    sc: InsuranceServiceContext,
    valid_old_status: LeadStatusEnum,
    new_status: LeadStatusEnum,
) -> LeadModel:
    current_broker = sc.current_broker
    if new_status == LeadStatusEnum.WITHDRAWN and lead.created_by != sc.current_broker.id:
        raise UnauthorizedError("Only the creator of the lead can withdraw it")
    if current_broker.profile.qualification.is_qualified and lead.assign_to != current_broker.id:
        raise UnauthorizedError("You are not the assignee of lead")
    if not current_broker.profile.qualification.is_qualified and lead.created_by != current_broker.id:
        raise UnauthorizedError("You are not the assigner of lead")
    if valid_old_status != lead.status:
        raise DataValidationError("The status of current lead is not valid, you can not operate this lead")
    return sc.lead_service.update(
        LeadModel(
            id=lead.id,
            status=new_status,
        )
    )


def _assign_lead_to_broker(lead: LeadModel, assign_to_id: int, sc: InsuranceServiceContext) -> LeadModel:
    assign_to_broker = sc.broker_service.get_broker_by_id(assign_to_id)

    if assign_to_broker is None:
        raise DataValidationError("The assignee of lead is invalid")
    lead_fee = sc.broker_service.get_lead_fee_by_broker_id_and_insurance_type(
        assign_to_id, ModelInsuranceType(lead.insurance_type.value)
    )

    return lead.sqlmodel_update(
        {
            "referral_fee_type": (lead_fee.referral_fee_type if lead_fee else ReferralFeeTypeEnum.PREMIUM_PERCENTAGE),
            "referral_fee_value": (
                lead_fee.referral_fee_value if assign_to_broker.lead_config.willing_pay_for_leads and lead_fee else 0
            ),
            "assign_to": assign_to_id,
        }
    )


def _check_existing_customer(
    sc: InsuranceServiceContext,
    lead: LeadModel | LeadCreatingRequest,
    broker: Broker,
    referer_id: int | None = None,
) -> Customer:
    return sc.customer_service.check_existing_customer(
        Customer(
            name=lead.customer_name,
            gender=lead.customer_gender,
            email=lead.customer_email.strip().lower() if lead.customer_email else None,
            province=lead.customer_province,
            city=lead.customer_city,
            address=lead.customer_address,
            postal_code=lead.customer_postal_code,
            phone=lead.customer_phone.strip() if lead.customer_phone else None,
            birthday=lead.customer_birthday,
            broker_id=broker.id,
            referer_id=referer_id,
        )
    )


@api_router.post(
    "",
    summary="push a new lead to specific broker",
    response_model=BaseResponse[LeadResponse],
)
async def _(
    lead: LeadCreatingRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
    core_sc: CoreServiceContext = Security(CoreServiceContext.create),
) -> BaseResponse[LeadResponse]:
    current_broker = sc.current_broker
    if lead.customer_id is None:
        customer = _check_existing_customer(sc, lead, current_broker)
        lead.customer_id = customer.id

    new_lead = sc.lead_service.create(
        lead.to_model(
            current_broker.id,  # type: ignore
            core_sc.reference_code_service,
        )
    )

    if lead.insurance_info:
        sc.lead_service.save_json(lead.insurance_info, new_lead.serial_number)  # type: ignore
    return BaseResponse.ok(LeadResponse.from_model(new_lead, current_broker))


@api_router.get(
    "/{id:int}",
    summary="get lead by id",
    response_model=BaseResponse[LeadRichInfoListResponse],
)
async def _(
    id: int,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[LeadRichInfoListResponse]:
    current_broker = sc.current_broker
    lead = sc.lead_service.get_by_id_with_rich_info(id)
    if lead.serial_number != "":
        lead.insurance_info = sc.lead_service.read_json(lead.serial_number)

    return BaseResponse.ok(LeadRichInfoListResponse.from_model(lead, current_broker))


@api_router.put(
    "/{id:int}",
    summary="modify a pending lead which is the creator owned",
    response_model=BaseResponse[LeadResponse],
)
async def _(
    id: int,
    lead: LeadUpdatingRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[LeadResponse]:
    current_lead = sc.lead_service.get_by_id(id)
    if current_lead.serial_number == "":
        current_lead.serial_number = uuid.uuid4().hex

    if current_lead.created_by != sc.current_broker.id:
        raise UnauthorizedError("You are not the owner of lead")
    if LeadStatusEnum.DRAFT != current_lead.status and LeadStatusEnum.PENDING != current_lead.status:
        raise DataValidationError("The status of current lead is not draft or pending, you can not modify this lead")
    if lead.customer_id:
        customer = sc.customer_service.get_by_id(lead.customer_id)
        if customer.broker_id != sc.current_broker.id:
            raise DataValidationError("The current broker is not the owner of customer")
    updated_lead = sc.lead_service.update(lead.to_model(id, current_lead.serial_number))  # type: ignore

    if lead.insurance_info:
        sc.lead_service.merge_json(updated_lead.id, lead.insurance_info)  # type: ignore

    return BaseResponse.ok(LeadResponse.from_model(updated_lead, sc.current_broker))


@api_router.post(
    "/{id:int}/assign_to",
    summary="the referral broker assign a draft lead to a broker",
    response_model=BaseResponse[LeadResponse],
)
async def _(
    id: int,
    assign_request: LeadAssignRequest,
    background_tasks: BackgroundTasks,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
    core_sc: CoreServiceContext = Security(CoreServiceContext.create),
) -> BaseResponse[LeadResponse]:
    current_lead = sc.lead_service.get_by_id(id)
    if current_lead.created_by != sc.current_broker.id:
        raise UnauthorizedError("You are not the owner of lead")
    if LeadStatusEnum.DRAFT != current_lead.status:
        raise DataValidationError(
            "The status of current lead is not draft, you can not change the assignment of this lead"
        )

    current_lead.is_anonymous = assign_request.is_anonymous

    lead = _modify_lead_status(
        sc.lead_service.update(_assign_lead_to_broker(current_lead, assign_request.assign_to_id, sc)),
        sc,
        LeadStatusEnum.DRAFT,
        LeadStatusEnum.PENDING,
    )

    insurance_type = lead.insurance_type
    assign_to_broker = sc.broker_service.get_broker_by_id(assign_request.assign_to_id)
    template_context = {
        "insurance_type": insurance_type,
        "name": assign_to_broker.name,
        "link": f"{settings.BETHUNE_SITE_URL}/#/lead",
    }

    background_tasks.add_task(
        core_sc.email_service.send_email,
        subject=f"🎉 Congratulations! You’ve Received a New Sales Lead – {insurance_type}",
        recipients=[assign_to_broker.user.email],
        template_name="broker_lead_notification.html",
        template_context=template_context,
    )

    return BaseResponse.ok(
        LeadResponse.from_model(
            lead,
            sc.current_broker,
        )
    )


@api_router.post(
    "/{id:int}/accept",
    summary="the broker accepts a pending lead which is assign to whom",
    response_model=BaseResponse[LeadResponse],
)
async def _(
    id: int,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[LeadResponse]:
    return BaseResponse.ok(
        LeadResponse.from_model(
            _modify_lead_status(
                sc.lead_service.get_by_id(id),
                sc,
                LeadStatusEnum.PENDING,
                LeadStatusEnum.ACCEPTED,
            ),
            sc.current_broker,
        )
    )


@api_router.post(
    "/{id:int}/withdraw",
    summary="the broker withdraws a pending lead which is assign to whom",
    response_model=BaseResponse[LeadResponse],
)
async def _(
    id: int,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[LeadResponse]:
    return BaseResponse.ok(
        LeadResponse.from_model(
            _modify_lead_status(
                sc.lead_service.get_by_id(id),
                sc,
                LeadStatusEnum.PENDING,
                LeadStatusEnum.WITHDRAWN,
            ),
            sc.current_broker,
        )
    )


@api_router.post(
    "/{id:int}/reject",
    summary="the broker rejects a pending lead which is assign to whom",
    response_model=BaseResponse[LeadResponse],
)
async def _(
    id: int,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[LeadResponse]:
    return BaseResponse.ok(
        LeadResponse.from_model(
            _modify_lead_status(
                sc.lead_service.get_by_id(id),
                sc,
                LeadStatusEnum.PENDING,
                LeadStatusEnum.REJECTED,
            ),
            sc.current_broker,
        )
    )


@api_router.get(
    "",
    summary="get leads by query condition",
    response_model=BaseResponse[Pagination[LeadRichInfoListResponse]],
)
async def _(
    query_params: LeadListQueryParams = Query(),
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[Pagination[LeadRichInfoListResponse]]:
    current_broker = sc.current_broker
    filters = LeadQueryFilters(
        **query_params.model_dump(exclude={"page_no", "page_size"}, exclude_unset=True),
    )
    total, leads = sc.lead_service.get_by_query_filters(
        current_broker,
        filters,
        query_params.offset(),
        query_params.limit(),
    )
    data = Pagination[LeadRichInfoListResponse].from_items(
        LeadRichInfoListResponse.from_models(leads, current_broker),
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.post(
    "/{id:int}/application",
    summary="derive new application from a lead",
    response_model=BaseResponse[InsuranceApplication],
)
async def _(
    id: int,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
    core_sc: CoreServiceContext = Security(CoreServiceContext.create),
) -> BaseResponse[InsuranceApplication]:
    current_lead = sc.lead_service.get_by_id(id)
    current_broker = sc.current_broker

    if LeadStatusEnum.ACCEPTED != current_lead.status:
        raise DataValidationError(
            "The status of current lead is not accept, you can not derive new application from this lead"
        )

    customer = _check_existing_customer(sc, current_lead, current_broker, current_lead.created_by)
    new_application = sc.insurance_application_service.create(
        InsuranceApplicationCreate(
            insurance_type=(
                InsuranceType(current_lead.insurance_type)
                if current_lead.insurance_type
                else InsuranceType.HOUSE_INSURANCE
            ),
            province=current_lead.customer_province,
            city=current_lead.customer_city,
            address=current_lead.customer_address,
            postal_code=current_lead.customer_postal_code,
            customer_name=current_lead.customer_name,
            customer_gender=(GenderEnum(current_lead.customer_gender.value) if current_lead.customer_gender else None),
            email=current_lead.customer_email,
            phone=current_lead.customer_phone,
            birthday=current_lead.customer_birthday,
            customer_id=customer.id,
            broker_id=current_broker.id,
            brokerage_id=current_broker.brokerage_id,
            status=InsuranceApplicationStatus.PENDING_UNDERWRITTEN if current_broker.brokerage_id is None else InsuranceApplicationStatus.PENDING_QUOTE,  # type: ignore
        ).to_model(core_sc.reference_code_service)
    )

    application_insurance_info = {
        "province": customer.province,
        "city": customer.city,
        "postal_code": customer.postal_code,
        "address": customer.address,
        "broker_id": new_application.broker_id,
    }

    if current_lead.serial_number != "":
        lead_insurance_info = sc.lead_service.read_json(current_lead.serial_number)
        application_insurance_info = {**application_insurance_info, **lead_insurance_info}

    sc.insurance_application_service.save_json(
        {
            **{"insurance_application": new_application.model_dump(mode="json")},
            **{
                "applicant_info": {
                    "customer_id": customer.id,
                    "name": customer.name,
                    "gender": customer.gender,
                    "email": customer.email,
                    "phone": customer.phone,
                    "birthday": (customer.birthday.isoformat() if customer.birthday else None),
                },
                "insurance_info": application_insurance_info,
            },
        },
        new_application.serial_number,  # type: ignore
    )

    sc.lead_service.update(
        LeadModel(
            id=current_lead.id,
            status=LeadStatusEnum.PROCESSING,
        )
    )
    sc.lead_application_service.create(
        LeadApplicationModel(
            lead_id=current_lead.id,
            application_id=new_application.id,
        )
    )

    return BaseResponse.ok(InsuranceApplication.from_model(new_application))

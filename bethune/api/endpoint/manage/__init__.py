from fastapi import APIRouter
from fastapi_health import health as fhealth

from bethune.api.endpoint.manage.health import liveness
from bethune.api.endpoint.manage.health import readiness

manage_router = APIRouter(prefix="/manage")

manage_router.add_api_route(
    "/health/liveness",
    fhealth(
        [
            liveness,
        ]
    ),
)
manage_router.add_api_route(
    "/health/readiness",
    fhealth(
        [
            readiness,
        ]
    ),
)

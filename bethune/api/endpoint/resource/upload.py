import base64
import uuid
from pathlib import Path

from fastapi import APIRouter
from fastapi import Depends
from fastapi import File
from fastapi import Form
from fastapi import UploadFile

from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import ImageUploadRequest
from bethune.api.dto.base import ResourceTypeEnum
from bethune.error.errors import IMAGE_FORMAT_ERROR
from bethune.error.errors import VerificationCodeError
from bethune.settings import settings
from bethune.util.file import upload_file

api_router = APIRouter(prefix="", tags=["resource"])

_RESOURCE_FOLDER_MAP = {
    ResourceTypeEnum.AVATAR: settings.AVATARS_FOLDER,
    ResourceTypeEnum.LOGO: settings.LOGOS_FOLDER,
}


def _detect_image_format(image_data: bytes) -> str:
    if image_data.startswith(b"\xff\xd8\xff"):
        return "jpg"
    if image_data.startswith(b"\x89PNG\r\n\x1a\n"):
        return "png"
    if image_data.startswith(b"GIF87a") or image_data.startswith(b"GIF89a"):
        return "gif"
    if image_data.startswith(b"RIFF") and b"WEBP" in image_data[:12]:
        return "webp"
    return "jpg"


def _get_resource_type(type: ResourceTypeEnum = Form(...)) -> ResourceTypeEnum:
    return type


def _get_folder_name(resource_type: ResourceTypeEnum) -> str:
    folder_name = _RESOURCE_FOLDER_MAP.get(resource_type)
    if not folder_name:
        raise VerificationCodeError(IMAGE_FORMAT_ERROR, "Illegal data format.")
    return folder_name


def _process_base64_data(base64_str: str) -> bytes:
    if "," in base64_str:
        base64_str = base64_str.split(",", 1)[1]

    image_bytes = base64.b64decode(base64_str)

    if not image_bytes:
        raise VerificationCodeError(IMAGE_FORMAT_ERROR, "Illegal data format.")

    return image_bytes


def _save_image_file(image_bytes: bytes, folder_name: str) -> tuple[str, str]:
    files_path = Path(settings.DATA_DIR) / settings.UPLOADS_FOLDER / folder_name
    files_path.mkdir(parents=True, exist_ok=True)

    image_format = _detect_image_format(image_bytes)
    filename = f"{uuid.uuid4().hex}.{image_format}"
    file_path = files_path / filename

    with file_path.open("wb") as buffer:
        buffer.write(image_bytes)

    relative_path = f"{folder_name}/{filename}"
    file_url = f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{folder_name}/{filename}"

    return relative_path, file_url


@api_router.post(
    "/file",
    summary="upload file with upload type (mixed format)",
    response_model=BaseResponse[dict],
)
def upload_file_with_type(
    file: UploadFile = File(...),
    resource_type: ResourceTypeEnum = Depends(_get_resource_type),
) -> BaseResponse[dict]:
    folder_name = _get_folder_name(resource_type)
    filename, file_url = upload_file(file, folder_name)
    file_path = f"{folder_name}/{filename}"
    return BaseResponse.ok({"file": file_path, "file_url": file_url})


@api_router.post(
    "/image",
    summary="upload image with base64 format",
    response_model=BaseResponse[dict],
)
def upload_image_base64(
    request: ImageUploadRequest,
) -> BaseResponse[dict]:
    folder_name = _get_folder_name(request.type)
    image_bytes = _process_base64_data(request.data)
    relative_path, file_url = _save_image_file(image_bytes, folder_name)
    return BaseResponse.ok({"file": relative_path, "file_url": file_url})

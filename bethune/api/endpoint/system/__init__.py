from fastapi import APIRouter

from .auth import api_router as auth_router
from .oauth import api_router as oauth_router
from .permission import api_router as permission_router
from .role import api_router as role_router
from .user import api_router as user_router

system_router = APIRouter(prefix="/system")

system_router.include_router(user_router)
system_router.include_router(permission_router)
system_router.include_router(role_router)
system_router.include_router(auth_router)
system_router.include_router(oauth_router)

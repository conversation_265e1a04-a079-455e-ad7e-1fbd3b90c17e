from typing import Annotated

from fastapi import APIRouter
from fastapi import Depends
from fastapi.security import OAuth2PasswordRequestForm

from ...dto.auth import Token
from ...dto.auth import ValidateEmailVerificationRequest
from ..core.service_context import ServiceContext as CoreServiceContext
from .service_context import ServiceContext
from bethune.api.dto import BaseResponse

api_router = APIRouter(prefix="/auth", tags=["authn and authz"])


@api_router.post(
    "/token",
    response_model=Token,
    description="It returns Token Object to follow the oauth2 standard.",
)
async def token(
    sc: Annotated[ServiceContext, Depends()],
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
):
    return sc.auth_service.create_token(form_data.username, form_data.password)


# @api_router.post("/verification_code/verify")
async def verify_verification_code(
    core_sc: Annotated[CoreServiceContext, Depends()], verification_request: ValidateEmailVerificationRequest
):
    await core_sc.verification_code_service.verify_verification_code(
        str(verification_request.email), verification_request.verification_code
    )
    return BaseResponse.ok(verification_request.email)

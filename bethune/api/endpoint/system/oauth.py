from io import Bytes<PERSON>
from pathlib import Path
from typing import Annotated
from urllib.parse import quote

import httpx
from fastapi import APIRouter
from fastapi import Depends
from fastapi import Query
from fastapi import UploadFile
from fastapi.responses import RedirectResponse
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

from .service_context import ServiceContext
from bethune.db.redis import get_redis
from bethune.error import DataValidationError
from bethune.error.errors import NotFoundError
from bethune.logging import logger
from bethune.model.system import OAuthProvider
from bethune.model.system import OAuthUserInfo
from bethune.settings import settings


api_router = APIRouter(prefix="/oauth", tags=["oauth"])


OPENID_SCOPE = "openid"
EMAIL_SCOPE = "https://www.googleapis.com/auth/userinfo.email"
PROFILE_SCOPE = "https://www.googleapis.com/auth/userinfo.profile"
GOOGLE_SCOPES = [OPENID_SCOPE, EMAIL_SCOPE, PROFILE_SCOPE]


@api_router.get("/callback/google")
async def google_callback(
    state: Annotated[str, Query()],
    sc: Annotated[ServiceContext, Depends()],
    code: Annotated[str | None, Query()] = None,
    error: Annotated[str | None, Query()] = None,
) -> RedirectResponse:
    if error is not None:
        logger.error(
            "receive error callback from google, error: {error}, state: {state}",
            error,
            state,
        )
        raise DataValidationError(f"Google OAuth error: {error}")
    logger.info("google oauth callback: state={state}, code={code}", state=state, code=code)
    registry_url = await get_redis().hget(f"oauth:google:state:{state}", "registry_url")
    if registry_url is None:
        raise DataValidationError("illegal callback")
    auth_url = await get_redis().hget(f"oauth:google:state:{state}", "auth_url")
    if auth_url is None:
        raise DataValidationError("illegal callback")
    # check state
    flow = Flow.from_client_config(
        settings.google_client_config(),
        GOOGLE_SCOPES,
    )
    flow.state = state
    flow.redirect_uri = settings.GOOGLE_REDIRECT_URIS[0]
    flow.fetch_token(
        code=code,
    )

    with build("people", "v1", credentials=flow.credentials) as service:
        result = (
            service.people()
            .get(
                resourceName="people/me",
                personFields="metadata,emailAddresses,names,photos",
            )  # 获取当前用户  # 指定所需字段
            .execute()
        )

        # 提取信息
        openid = result["metadata"]["sources"][0]["id"]  # OpenID
        email = result["emailAddresses"][0]["value"]  # 邮箱
        profile_pic = result["photos"][0]["url"]  # 头像URL
        name = result["names"][0]["displayName"]  # 显示名称
    oauth_user_info = sc.oauth_user_info_service.get_by_open_id_and_provider(openid, OAuthProvider.GOOGLE)
    if oauth_user_info is None:
        # new oauth user
        try:
            user = sc.user_service.get_by_email(email)
            oauth_user_info = OAuthUserInfo(
                user_id=user.id,
                open_id=openid,
                provider=OAuthProvider.GOOGLE,
                refresh_token=flow.credentials.refresh_token,
                expires_at=flow.credentials.expiry,
            )
            sc.oauth_user_info_service.create(oauth_user_info)
        except NotFoundError:
            oauth_user_info = OAuthUserInfo(
                user_id=0,
                open_id=openid,
                provider=OAuthProvider.GOOGLE,
                refresh_token=flow.credentials.refresh_token,
                expires_at=flow.credentials.expiry,
            )
            logger.info(
                "new oauth user: openid={openid}, email={email}, name={name}, profile_pic={profile_pic}",
                openid=openid,
                email=email,
                name=name,
                profile_pic=profile_pic,
            )
            json = oauth_user_info.model_dump_json()
            logger.info("save oauth user info: {json}", json=json)
            await get_redis().setex(
                name=f"bethune:register:oauth_id:{state}",
                value=json.encode("utf-8"),
                time=60 * 60,
            )
            # download avatar from google
            avatar_url = await _save_avatar(openid, profile_pic, sc)
            logger.info("avatar_url: {avatar_url}", avatar_url=avatar_url)
            redirect_url = f"{settings.BETHUNE_SITE_URL}?email={email}&oauth_id={state}&name={name}"
            if avatar_url:
                redirect_url = f"{redirect_url}&avatar={avatar_url}"
            redirect_url = quote(redirect_url, safe=":/?&=;")
            registry_url = registry_url.decode("utf-8")
            logger.info("new oauth user: redirect to {registry_url}", registry_url=registry_url)
            return RedirectResponse(f"{redirect_url}{registry_url}")
    else:
        oauth_user_info.open_id = openid
        oauth_user_info.refresh_token = flow.credentials.refresh_token
        oauth_user_info.expires_at = flow.credentials.expiry
        sc.oauth_user_info_service.update(oauth_user_info)
        user = sc.user_service.get_by_id(oauth_user_info.user_id)

    token = sc.auth_service.create_token_by_user(user)
    redirect_url = f"{settings.BETHUNE_SITE_URL}{auth_url.decode('utf-8')}"
    query = quote(f"?token={token.access_token}&type={token.token_type}", safe=":/?&=;")
    logger.info(
        "google oauth callback: redirect to {redirect_url} with user_id={user_id}, email={email}, name={name}",
        redirect_url=redirect_url,
        user_id=user.id,
        email=email,
        name=name,
    )
    return RedirectResponse(f"{redirect_url}{query}")


async def _save_avatar(openid: str, profile_pic: str, sc: ServiceContext) -> UploadFile | None:
    logger.info(f"Downloading avatar for {openid} from {profile_pic}")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(profile_pic, timeout=30.0)
            response.raise_for_status()  # 检查HTTP错误
            filename = Path(profile_pic).name.split("?")[0]  # 去除查询参数
            upload_file = UploadFile(
                file=BytesIO(response.content),
                size=len(response.content),
                filename=filename,
                headers=response.headers,
            )
            _, avatar_url = await sc.user_service.upload_avatar(upload_file, filename)
            return avatar_url
        except Exception as e:
            logger.error(f"Failed to download or save avatar for {openid}: {e}")
            return None


@api_router.get("/auth/google")
async def google_auth_url(
    registry_url: Annotated[str, Query()],
    auth_url: Annotated[str, Query()],
) -> RedirectResponse:
    flow = Flow.from_client_config(
        settings.google_client_config(),
        GOOGLE_SCOPES,
    )
    flow.redirect_uri = settings.GOOGLE_REDIRECT_URIS[0]
    authorization_url, state = flow.authorization_url(
        # response_type="code",
        # scope=" ".join(GOOGLE_SCOPES),
        access_type="offline",
        include_granted_scopes="true",
        prompt="consent",
    )
    logger.info(
        "registry_url: {registry_url}, auth_url: {auth_url}, state: {state}",
        registry_url=registry_url,
        auth_url=auth_url,
        state=state,
    )
    await get_redis().hset(
        name=f"oauth:google:state:{state}",
        key="registry_url",
        value=registry_url.encode("utf-8"),
    )
    await get_redis().hset(
        name=f"oauth:google:state:{state}",
        key="auth_url",
        value=auth_url.encode("utf-8"),
    )
    await get_redis().expire(name=f"oauth:google:state:{state}", time=60 * 60)  # 1 hour expiration

    return RedirectResponse(url=authorization_url)

from typing import Annotated

from fastapi import APIRouter
from fastapi import Query
from fastapi import Security

from .service_context import ServiceContext
from bethune.api.dto import BaseResponse
from bethune.api.dto import PageParams
from bethune.api.dto import Pagination
from bethune.api.dto.permission import PermissionList


api_router = APIRouter(prefix="/permission", tags=["permission management"])


@api_router.get(
    "",
    summary="get permissions",
    response_model=BaseResponse[Pagination[PermissionList]],
)
async def get_permissions(
    page_params: Annotated[PageParams, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:permission:view"]),
) -> BaseResponse[Pagination[PermissionList]]:
    total, permissions = sc.permission_service.get_by_example_with_total(
        offset=page_params.offset(), limit=page_params.limit()
    )
    data = Pagination[PermissionList].from_items(
        PermissionList.from_models(permissions),
        total,
        page_params.page_no,
        page_params.page_size,
    )
    return BaseResponse.ok(data)

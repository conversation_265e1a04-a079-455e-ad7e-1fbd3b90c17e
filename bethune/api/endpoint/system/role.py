from typing import Annotated

from fastapi import APIRouter
from fastapi import Query
from fastapi import Security

from .service_context import ServiceContext
from bethune.api.dto import BaseResponse
from bethune.api.dto import PageParams
from bethune.api.dto import Pagination
from bethune.api.dto.permission import PermissionList
from bethune.api.dto.role import Role
from bethune.api.dto.role import RoleCreate
from bethune.api.dto.role import RoleList
from bethune.api.dto.role import RoleUpdate


api_router = APIRouter(prefix="/role", tags=["role management"])


@api_router.post(
    "",
    summary="create role",
    response_model=BaseResponse[Role],
)
async def create_user(
    role: RoleCreate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:role:create"]),
) -> BaseResponse[Role]:

    created_role = sc.role_service.create(role.to_model())
    return BaseResponse.ok(Role.from_model(created_role))


@api_router.get(
    "/{id}",
    summary="get role by id",
    response_model=BaseResponse[Role],
)
async def get_role(
    id: int,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:role:view"]),
) -> BaseResponse[Role]:

    user = sc.role_service.get_by_id(id)
    return BaseResponse.ok(Role.from_model(user))


@api_router.patch(
    "/{id}",
    summary="update role by id",
    response_model=BaseResponse[Role],
)
async def update_role(
    id: int,
    role: RoleUpdate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:role:update"]),
) -> BaseResponse[Role]:

    updated_role = sc.role_service.update(role.to_model(id))
    return BaseResponse.ok(Role.from_model(updated_role))


@api_router.get(
    "",
    summary="get roles",
    response_model=BaseResponse[Pagination[RoleList]],
)
async def get_roles(
    page_params: Annotated[PageParams, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:role:view"]),
) -> BaseResponse[Pagination[RoleList]]:

    total, roles = sc.role_service.get_by_example_with_total(offset=page_params.offset(), limit=page_params.limit())
    data = Pagination[RoleList].from_items(
        RoleList.from_models(roles),
        total,
        page_params.page_no,
        page_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.get(
    "/{id}/permission",
    summary="get role permissions",
)
async def get_permissions(
    id: int,
    page_params: Annotated[PageParams, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:role:permission:view"]),
) -> BaseResponse[Pagination[PermissionList]]:
    total, permissions = sc.role_service.get_permissions(id, offset=page_params.offset(), limit=page_params.limit())
    data = Pagination[PermissionList].from_items(
        PermissionList.from_models(permissions),
        total,
        page_params.page_no,
        page_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.post(
    "/{id}/permission",
    summary="set role permissions",
)
async def set_role_permissions(
    id: int,
    permission_ids: list[int],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:role:permission:assign"]),
) -> BaseResponse[None]:
    sc.role_service.set_permissions(id, permission_ids)
    return BaseResponse.ok(None)

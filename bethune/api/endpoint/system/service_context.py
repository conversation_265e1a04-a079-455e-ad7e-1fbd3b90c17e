from bethune.api.dependencies.authorization import BaseServiceContext
from bethune.service.system import PermissionService
from bethune.service.system import RoleService
from bethune.service.system import ServiceFactory
from bethune.service.system import UserService
from bethune.service.system.auth import AuthService
from bethune.service.system.oauth_user_info import OAuthUserInfoService


class ServiceContext(BaseServiceContext):

    def __init__(self):
        super().__init__()
        self._user_service = None
        self._role_service = None
        self._permission_service = None
        self._auth_service = None
        # self._reminder_config_service = None
        # self._message_template_service = None
        # self._broker_reminder_settings_service = None
        self._oauth_user_info_service = None

    @property
    def user_service(self) -> UserService:
        if not self._user_service:
            self._user_service = ServiceFactory.create_user_service()
        return self._user_service

    @property
    def role_service(self) -> RoleService:
        if not self._role_service:
            self._role_service = ServiceFactory.create_role_service()
        return self._role_service

    @property
    def permission_service(self) -> PermissionService:
        if not self._permission_service:
            self._permission_service = ServiceFactory.create_permission_service()
        return self._permission_service

    @property
    def auth_service(self) -> AuthService:
        if not self._auth_service:
            self._auth_service = ServiceFactory.create_auth_service(self.user_service)
        return self._auth_service

    @property
    def oauth_user_info_service(self) -> OAuthUserInfoService:
        if not self._oauth_user_info_service:
            self._oauth_user_info_service = ServiceFactory.create_oauth_user_info_service()
        return self._oauth_user_info_service

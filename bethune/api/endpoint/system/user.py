from typing import Annotated

from fastapi import APIRouter
from fastapi import Query
from fastapi import Security

from .service_context import ServiceContext
from bethune.api.dto import BaseResponse
from bethune.api.dto import PageParams
from bethune.api.dto import Pagination
from bethune.api.dto.role import RoleList
from bethune.api.dto.user import User
from bethune.api.dto.user import UserCreate
from bethune.api.dto.user import UserList
from bethune.api.dto.user import UserUpdate

api_router = APIRouter(prefix="/user", tags=["user management"])


@api_router.post(
    "",
    summary="create user",
    response_model=BaseResponse[User],
)
async def create_user(
    user: UserCreate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:user:create"]),
) -> BaseResponse[User]:

    created_user = sc.user_service.create(user.to_model())
    return BaseResponse.ok(User.from_model(created_user))


@api_router.get(
    "/{id}",
    summary="get user by id",
    response_model=BaseResponse[User],
)
async def get_user(
    id: int,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:user:view"]),
) -> BaseResponse[User]:
    user = sc.user_service.get_by_id(id)
    return BaseResponse.ok(User.from_model(user))


@api_router.patch(
    "/{id}",
    summary="update user by id",
    response_model=BaseResponse[User],
)
async def update_user(
    id: int,
    user: UserUpdate,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:user:update"]),
) -> BaseResponse[User]:
    updated_user = sc.user_service.update(user.to_model(id))

    return BaseResponse.ok(User.from_model(updated_user))


@api_router.get(
    "",
    summary="get users",
    response_model=BaseResponse[Pagination[UserList]],
)
async def get_users(
    page_params: Annotated[PageParams, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:user:view"]),
) -> BaseResponse[Pagination[UserList]]:
    total, users = sc.user_service.get_by_example_with_total(offset=page_params.offset(), limit=page_params.limit())
    data = Pagination[UserList].from_items(
        UserList.from_models(users),
        total,
        page_params.page_no,
        page_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.get(
    "/{id}/role",
    summary="get user roles",
)
async def get_user_roles(
    id: int,
    page_params: Annotated[PageParams, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:user:role:view"]),
) -> BaseResponse[Pagination[RoleList]]:
    total, roles = sc.user_service.get_roles(id, page_params.offset(), page_params.limit())
    data = Pagination[RoleList].from_items(
        RoleList.from_models(roles),
        total,
        page_params.page_no,
        page_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.post(
    "/{id}/role",
    summary="set user roles",
)
async def set_user_roles(
    id: int,
    role_ids: list[int],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["system:user:role:assign"]),
) -> BaseResponse[None]:
    sc.user_service.set_roles(id, role_ids)
    return BaseResponse.ok(None)

from bethune.error.errors import BusinessError
from bethune.util import get_text

UNAUTHENTICATED = 4040
UNAUTHORIZED = 4050
MISSING_REQUIRED_PARAMETERS = 4080


class UnauthenticatedError(BusinessError):
    """
    未认证
    """

    def __init__(
        self,
        err_msg: str = "Unauthenticated",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(
            code=UNAUTHENTICATED,
            message=get_text(err_msg),
            detail=detail,
            metadata=metadata,
        )


class UnauthorizedError(BusinessError):
    """
    无权限
    """

    def __init__(self, err_msg: str = "Unauthorized", detail: dict | None = None):
        super().__init__(
            code=UNAUTHORIZED,
            message=get_text(err_msg),
            detail=detail,
            metadata=None,
        )


class MissingRequiredParametersError(BusinessError):
    """
    缺少必需参数
    """

    def __init__(
        self,
        err_msg: str = "Missing required parameters",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(
            code=MISSING_REQUIRED_PARAMETERS,
            message=get_text(err_msg),
            detail=detail,
            metadata=metadata,
        )

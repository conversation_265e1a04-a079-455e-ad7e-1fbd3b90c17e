from collections import defaultdict
from uuid import uuid4

from fastapi import FastAP<PERSON>
from fastapi import Request
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from starlette.responses import JSONResponse
from starlette.status import HTTP_401_UNAUTHORIZED
from starlette.status import HTTP_403_FORBIDDEN
from starlette.status import HTTP_404_NOT_FOUND
from starlette.status import HTTP_417_EXPECTATION_FAILED
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from bethune.api.dto import BusinessErrorResponse
from bethune.api.error import UnauthenticatedError
from bethune.api.error import UnauthorizedError
from bethune.error import BusinessError
from bethune.error import DataValidationError
from bethune.error import NotFoundError
from bethune.error import PasswordExpiredError
from bethune.error import UnexpectedError
from bethune.error.errors import VerificationCodeError
from bethune.logging import logger


BusinessError_ERROR_HTTP_CODE_MAPPING: defaultdict[type, int] = defaultdict(
    lambda: HTTP_500_INTERNAL_SERVER_ERROR,
    {
        UnauthenticatedError: HTTP_401_UNAUTHORIZED,
        UnauthorizedError: HTTP_403_FORBIDDEN,
        NotFoundError: HTTP_404_NOT_FOUND,
        PasswordExpiredError: HTTP_403_FORBIDDEN,
        DataValidationError: HTTP_422_UNPROCESSABLE_ENTITY,
        VerificationCodeError: HTTP_417_EXPECTATION_FAILED,
    },
)


def _response_business_err(err: BusinessError) -> JSONResponse:
    http_status_code = BusinessError_ERROR_HTTP_CODE_MAPPING[type(err)]
    body = BusinessErrorResponse.fail(err).model_dump(exclude_unset=True, exclude_none=True)
    return JSONResponse(
        status_code=http_status_code,
        content=body,
    )


def register_exception_handler(app: FastAPI) -> None:
    """
    全局异常捕获处理
    :param app:
    :return:
    """

    @app.exception_handler(BusinessError)
    async def business_error_handler(_request: Request, exc: BusinessError) -> JSONResponse:
        """
        未认证异常
        :param request:
        :param exc:
        :return:
        """
        return _response_business_err(exc)

    @app.exception_handler(RequestValidationError)
    async def validation_error_handler(_request: Request, exc: RequestValidationError) -> JSONResponse:
        """
        数据校验异常
        :param request:
        :param exc:
        :return:
        """
        err_msg = [f"Field: {err['loc']}, Message: {err['msg']}, Type: {err['type']}" for err in exc.errors()]
        return _response_business_err(
            DataValidationError(
                message=f"Data validation error, detail info: {', '.join(err_msg)}",
                detail={"errors": exc.errors()},
            ),
        )

    @app.exception_handler(ValidationError)
    async def pydantic_validation_error_handler(_request: Request, exc: ValidationError) -> JSONResponse:
        """
        Pydantic 数据校验异常
        :param request:
        :param exc:
        :return:
        """
        err_msg = [f"Field: {err['loc']}, Message: {err['msg']}, Type: {err['type']}" for err in exc.errors()]
        return _response_business_err(
            DataValidationError(
                message=f"Data validation error, detail info: {', '.join(err_msg)}",
                detail={"errors": exc.errors()},
            ),
        )

    @app.exception_handler(Exception)
    async def exception_handler(_request: Request, exc: Exception) -> JSONResponse:
        """
        未知异常
        :param request:
        :param exc:
        :return:
        """
        uid = uuid4().hex
        logger.exception(f"error uniqu id: {uid}", exc)
        return _response_business_err(UnexpectedError(detail={"uid": uid, "exception": str(exc)}))

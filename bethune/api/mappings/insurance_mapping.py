INSURANCE_MAPPINGS = {
    "insurance_type": "insurance_type",
    "insurance_consultation_id": "insurance_consultation_id",  # 咨询ID
    # applicant_info 投保人信息
    "customer_id": "applicant_info.customer_id",  # customer_id
    "customer_name": "applicant_info.name",  # 姓名
    "customer_gender": "applicant_info.gender",  # 姓名
    "email": "applicant_info.email",  # 邮箱
    "phone": "applicant_info.phone",  # 投保人电话
    "birthday": "applicant_info.birthday",  # 出生日期
    "move_in_date": "applicant_info.move_in_date",  # 入住日期
    # house_info
    # "year_built": "house_info.year_built",  # 房屋建造年份
    # "house_type": "house_info.house_type",  # 房屋类型
    # "is_mortgage": "house_info.is_mortgage",  # 是否存在抵押
    # "house_area": "house_info.house_area",  # 房屋面积（平方米，不含地下室）
    # insurance_info
    "province": "insurance_info.province",
    "city": "insurance_info.city",
    "postal_code": "insurance_info.postal_code",
    "address": "insurance_info.address",  # 投保地址
    "expected_start_date": "insurance_info.expected_start_date",  # 期望效日期
    # "broker_id": "insurance_info.broker_id",
    # "start_date": "insurance_info.start_date",  # 生效日期
    # "end_date": "insurance_info.end_date",  # 失效日期
    # "premium": "insurance_info.premium",  # 保费
    # insurance_history_info
    "is_first_apply": "insurance_history_info.is_first_apply",  # 是否首次投保
    # other_info
    "has_rejection_record": "other_info.has_rejection_record",  # 是否有拒保记录
}

from contextlib import asynccontextmanager
from pathlib import Path

import email_validator
from fastapi import Depends
from fastapi import FastAPI
from fastapi import Request
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
from fastapi.staticfiles import StaticFiles
from fastapi_babel.middleware import BabelMiddleware as I18nMiddleware
from fastapi_babel.properties import RootConfigs
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette_context import plugins
from starlette_context.middleware import RawContextMiddleware

from bethune.api.endpoint import api_router
from bethune.api.handler import register_exception_handler
from bethune.constant import SUPPORTED_LANGUAGE_PREFIXES
from bethune.db import create_db_session
from bethune.infrastructure.task.scheduler import init_scheduler
from bethune.infrastructure.task.scheduler import shutdown_scheduler
from bethune.infrastructure.task.worker import start_worker
from bethune.infrastructure.task.worker import stop_worker
from bethune.logging import logger
from bethune.settings import settings


email_validator.CHECK_DELIVERABILITY = False  # 禁用全局可交付性检查


async def create_db_session_per_request():
    for session in create_db_session():
        yield session


logger.info("create FastAPI app")


@asynccontextmanager
async def lifespan(_app: FastAPI):
    await init_scheduler()

    # 启动后台Worker线程
    start_worker()
    yield

    # 关闭阶段
    await shutdown_scheduler()
    stop_worker()


def _default_locale_selector(request: Request):
    lang = (
        request.state.language
        if hasattr(request.state, "language")
        else request.headers.get("Accept-Language", settings.I18N_DEFAULT_LANGUAGE)
    )
    return next(
        (lang for supported_lang_prefix in SUPPORTED_LANGUAGE_PREFIXES if lang.startswith(supported_lang_prefix)),
        settings.I18N_DEFAULT_LANGUAGE,
    )


app = FastAPI(
    lifespan=lifespan,
    title=settings.PROJECT_NAME,
    root_path=f"{settings.ROOT_PATH}",
    version=f"{settings.VERSION}",
    dependencies=[Depends(create_db_session_per_request)],
    middleware=[
        Middleware(RawContextMiddleware, plugins=([plugins.RequestIdPlugin()])),
        Middleware(
            I18nMiddleware,
            babel_configs=RootConfigs(
                ROOT_DIR=settings.I18N_CONFIG_ROOT_DIR,
                BABEL_DEFAULT_LOCALE="nonexistent",  # 这是个hack，为了让fastapi_babel强制使用自己的gt.gettext，而非系统的gettext
                BABEL_TRANSLATION_DIRECTORY=settings.I18N_LOCALE_DIR,
            ),
            locale_selector=_default_locale_selector,
        ),
    ],
    docs_url=None,
)


@app.middleware("http")
async def ensure_default_language(request: Request, call_next):
    """确保所有请求都有语言设置"""
    # 将语言设置存储在请求状态中
    if "Accept-Language" not in request.headers:
        request.state.language = settings.I18N_DEFAULT_LANGUAGE

    return await call_next(request)


# TODO 增加请求拦截hook
logger.info("register exception handler to app")
register_exception_handler(app)

app.include_router(api_router)

# 如果需要，挂载静态目录
app.mount("/uploads", StaticFiles(directory=Path(settings.DATA_DIR)), name="uploads")


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://unpkg.com/swagger-ui-dist@5.11.0/swagger-ui-bundle.js",
        swagger_css_url="https://unpkg.com/swagger-ui-dist@5.11.0/swagger-ui.css",
    )


@app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False)
async def swagger_ui_redirect():
    return get_swagger_ui_oauth2_redirect_html()


# Set all CORS enabled origins
if settings.CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

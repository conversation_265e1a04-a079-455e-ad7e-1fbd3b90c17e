from typing import Any

from dogpile.cache import make_region
from dogpile.util import compat

from bethune.settings import get_dynamic_settings
from bethune.settings import settings


CACHE_KEY_SEPARATOR = ":"


def _get_cache_settings(prefix: str) -> dict[str, Any]:
    cache_settings = get_dynamic_settings(prefix)
    return {
        str.replace(key.strip().lower(), "arguments_", "arguments."): value for key, value in cache_settings.items()
    }


def _func_key_generator(namespace, fn):
    args = compat.inspect_getargspec(fn)
    has_self = args[0] and args[0][0] in ("self", "cls")

    def generate_key(*args, **kwargs):
        segments = []
        if namespace:
            segments.append(namespace)
        else:
            segments.append(fn.__module__)
            segments.append(fn.__name__)
        for arg in args if not has_self else args[1:]:
            segments.append(str(arg))
        for k, v in kwargs.items():
            segments.append(f"{k}={v}")
        return CACHE_KEY_SEPARATOR.join(segments)

    return generate_key


def clear_cache(namespace: str, fn, *args, **kwargs):
    cache.delete(_func_key_generator(namespace, fn)(*args, **kwargs))


cache = make_region(
    key_mangler=lambda key: CACHE_KEY_SEPARATOR.join(["bethune", "cache", key]),
    function_key_generator=_func_key_generator,
).configure_from_config(_get_cache_settings(settings.CACHE_PREFIX), settings.CACHE_PREFIX.strip().lower())

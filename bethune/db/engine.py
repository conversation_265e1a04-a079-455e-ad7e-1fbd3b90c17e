from sqlalchemy.engine import Engine
from sqlmodel import create_engine
from sqlmodel import Session

from bethune.settings import settings


default_engine: Engine | None = None


def _create_session() -> Session:
    global default_engine
    if default_engine is None:
        default_engine = create_engine(
            settings.DATABASE_URL,
            echo=True,
        )
    return Session(default_engine, autoflush=True)

from redis import Redis as SyncRedis
from redis.asyncio import Redis

from bethune.settings import settings

# 初始化为 None，允许延迟初始化
_redis_client: Redis | None = None
_sync_redis_client: SyncRedis | None = None


def get_redis() -> Redis:
    global _redis_client
    if _redis_client is None:
        # 默认使用配置中的 URL
        _redis_client = Redis.from_url(settings.REDIS_URL)
    return _redis_client  # type: ignore  # noqa: R504


def get_sync_redis() -> SyncRedis:
    global _sync_redis_client
    if _sync_redis_client is None:
        # 默认使用配置中的 URL
        _sync_redis_client = SyncRedis.from_url(settings.REDIS_URL)
    return _sync_redis_client  # type: ignore  # noqa: R504

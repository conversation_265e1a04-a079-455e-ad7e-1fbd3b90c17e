from collections.abc import Callable
from contextlib import asynccontextmanager
from contextlib import contextmanager
from contextvars import Context<PERSON><PERSON>

from loguru import logger
from sqlmodel import Session

from bethune.db.engine import _create_session


__SESSION_CONTEXT: ContextVar[Session] = ContextVar("db_session")


def create_db_session(session_maker: Callable[[], Session] = _create_session, rollback_only: bool = False):
    session: Session
    try:
        session = session_maker()
        token = __SESSION_CONTEXT.set(session)
        logger.trace(
            "db session [{}] is created and be stored in db session context, token is {}",
            session,
            token,
        )
        yield session
    except Exception as e:
        if session is not None:
            logger.error("error presented, will rollback session, exception: {}", e)
            session.rollback()
        raise e
    else:
        if session.in_transaction():
            if rollback_only:
                logger.trace("rollback_only is true, will rollback session [{}]", session)
                session.rollback()
            else:
                logger.trace("commit session [{}]", session)
                session.commit()
    finally:
        if token is not None:
            session.close()
            __SESSION_CONTEXT.reset(token)


def session() -> Session:
    try:
        return __SESSION_CONTEXT.get()
    except LookupError:
        raise RuntimeError("can't get session from session_context")


@contextmanager
def session_context(session_maker: Callable[[], Session] = _create_session, rollback_only: bool = False):
    yield from create_db_session(session_maker=session_maker, rollback_only=rollback_only)


@asynccontextmanager
async def async_session_context(session_maker: Callable[[], Session] = _create_session, rollback_only: bool = False):
    for _ in create_db_session(session_maker=session_maker, rollback_only=rollback_only):
        yield

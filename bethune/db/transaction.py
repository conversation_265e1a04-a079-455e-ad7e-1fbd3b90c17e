import inspect
from contextlib import contextmanager
from functools import partial
from functools import wraps

from sqlmodel import Session

from bethune.db.session_context import session


def __current_session() -> Session:
    current_session = session()
    if current_session is None:
        raise RuntimeError("session is not initialized")
    return current_session


def __create_transaction_if_required(force_new: bool = False) -> tuple[Session, bool]:
    session = __current_session()
    if not session.in_transaction() and not session.in_nested_transaction():
        return session.begin(), True
    if force_new:
        return session.begin_nested(), True
    if session.in_nested_transaction():
        return session.get_nested_transaction(), False
    return session.get_transaction(), False


@contextmanager
def __current_transaction(force_new: bool = False, rollback_only: bool = False):
    transaction, is_created = __create_transaction_if_required(force_new=force_new)
    try:
        yield transaction
    except Exception as e:
        if is_created:
            # don't manage the transaction if it's not created by this context
            transaction.rollback()
        raise e
    else:
        if is_created:
            # don't manage the transaction if it's not created by this context
            if rollback_only:
                transaction.rollback()
            else:
                transaction.commit()


def transaction(
    func=None,
    *,
    force_new: bool = False,
    rollback_only: bool = False,
):
    if func is None:
        return partial(transaction, force_new=force_new, rollback_only=rollback_only)

    if inspect.iscoroutinefunction(func):

        @wraps(func)
        async def coroutine_wrapper(*args, **kwargs):
            with __current_transaction(force_new, rollback_only):
                return await func(*args, **kwargs)

        return coroutine_wrapper

    @wraps(func)
    def wrapper(*args, **kwargs):
        with __current_transaction(force_new, rollback_only):
            return func(*args, **kwargs)

    return wrapper

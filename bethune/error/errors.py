BUSINESS_ERROR = 4000
NOT_FOUND = 4010
PASSWORD_EXPIRED = 4020
PASSWORD_INVALID = 4022
PASSWORD_NOT_MATCH = 4024
DATA_VALIDATION_ERROR = 4030

VERIFICATION_CODE_ERROR = 4060
VERIFICATION_CODE_EXPIRED_ERROR = 4061
VERIFICATION_CODE_INCORRECT_ERROR = 4062

IMAGE_FORMAT_ERROR = 4070

UNEXPECTED_ERROR = 5000


class BusinessError(Exception):
    """
    业务异常
    """

    def __init__(
        self,
        code: int,
        message: str,
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        self.code = code
        self.message = message
        self.detail = detail
        self.metadata = metadata


class NotFoundError(BusinessError):
    """
    未找到
    """

    def __init__(
        self,
        message: str = "Not Found",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(code=NOT_FOUND, message=message, detail=detail, metadata=metadata)


class PasswordExpiredError(BusinessError):
    """
    密码已过期
    """

    def __init__(
        self,
        message: str = "Password Expired",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(code=PASSWORD_EXPIRED, message=message, detail=detail, metadata=metadata)


class PasswordInvalidError(BusinessError):
    """
    密码错误
    """

    def __init__(
        self,
        message: str = "Password Invalid",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(code=PASSWORD_INVALID, message=message, detail=detail, metadata=metadata)


class PasswordNotMatchError(BusinessError):
    """
    密码错误
    """

    def __init__(
        self,
        message: str = "Password Does Not Match",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(code=PASSWORD_NOT_MATCH, message=message, detail=detail, metadata=metadata)


class DataValidationError(BusinessError):
    """
    数据验证错误
    """

    def __init__(
        self,
        message: str = "Data Validation Error",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(
            code=DATA_VALIDATION_ERROR,
            message=message,
            detail=detail,
            metadata=metadata,
        )


class VerificationCodeError(BusinessError):
    """
    验证码错误
    """

    def __init__(
        self,
        code: int = VERIFICATION_CODE_ERROR,
        message: str = "Verification Code Error",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(code=code, message=message, detail=detail, metadata=metadata)


class UnexpectedError(BusinessError):
    """
    未知错误
    """

    def __init__(
        self,
        message: str = "Unexpected Error",
        detail: dict | None = None,
        metadata: dict | None = None,
    ):
        super().__init__(code=UNEXPECTED_ERROR, message=message, detail=detail, metadata=metadata)

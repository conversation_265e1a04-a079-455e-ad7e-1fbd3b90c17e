import asyncio
import imaplib
from email import policy
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.parser import BytesParser
from typing import cast

from aiosmtplib import SMTP
from jinja2 import Environment
from jinja2 import FileSystemLoader
from loguru import logger

from bethune.model.email import ReceivedEmail
from bethune.settings import settings


class EmailClient:
    def __init__(self, template_dir: str = "./bethune/templates"):
        self.env = Environment(loader=FileSystemLoader(template_dir))

    async def check_connection(self):
        try:
            async with SMTP(hostname=settings.SMTP_SERVER, port=settings.SMTP_PORT) as smtp:
                response = await smtp.noop()
                logger.info("SMTP server is active:", response)
            return True
        except Exception as e:
            logger.error(f"Connection error: {e}")
            return False

    async def send_email(
        self,
        subject: str,
        recipients: list,
        template_name: str,
        template_context: dict | None = None,
    ):
        template_context = template_context or {}
        template = self.env.get_template(template_name)
        rendered_content = template.render(template_context)

        message = MIMEMultipart("alternative")
        message["From"] = settings.SMTP_SENDER  # 使用aws ses给定的专属email
        message["To"] = ", ".join(recipients)
        message["Subject"] = subject
        message.attach(MIMEText(rendered_content, "html"))

        async with SMTP(
            hostname=settings.SMTP_SERVER,
            port=settings.SMTP_PORT,
            use_tls=settings.SMTP_USE_TLS,
            start_tls=settings.SMTP_START_TLS,
            username=settings.SMTP_USERNAME,
            password=settings.SMTP_PASSWORD,
        ) as smtp:
            await smtp.send_message(message)

    def parse_email(self, raw_email: bytes) -> ReceivedEmail:
        """
        解析原始邮件内容
        """
        msg = BytesParser(policy=cast(policy.Policy, policy.default)).parsebytes(raw_email)

        # 解析正文内容
        body_plain = []
        body_html = []
        attachments = []

        for part in msg.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))

            if "attachment" in content_disposition:
                attachments.append(
                    {
                        "filename": part.get_filename(),
                        "content_type": content_type,
                        "size": len(part.get_payload(decode=True)),
                        "content": part.get_payload(decode=True),
                    }
                )
            elif content_type == "text/plain":
                body_plain.append(part.get_payload(decode=True).decode())  # type: ignore
            elif content_type == "text/html":
                body_html.append(part.get_payload(decode=True).decode())  # type: ignore

        # 解析邮件头信息
        headers = {k: v for k, v in msg.items()}

        return ReceivedEmail(
            uid=msg["Message-ID"],
            subject=msg["Subject"],
            sender=msg["From"],
            recipients=msg["To"].split(", "),
            body_plain="\n".join(body_plain) if body_plain else None,
            body_html="\n".join(body_html) if body_html else None,
            attachments=attachments,
            raw_headers=headers,
            date=msg["Date"],
        )

    async def fetch_raw_emails(self, limit: int, unseen_only: bool) -> list[bytes]:
        """
        与IMAP服务器交互获取原始邮件数据
        """

        def sync_fetch():
            with imaplib.IMAP4_SSL(settings.IMAP_HOST) as mail:
                mail.login(settings.IMAP_USERNAME, settings.IMAP_PASSWORD)
                mail.select("INBOX")

                search_criteria = "(UNSEEN)" if unseen_only else "ALL"
                _, data = mail.search(None, search_criteria)
                mail_ids = data[0].split()[-limit:]  # 获取最新的N封邮件

                emails = []
                for num in mail_ids:
                    _, msg_data = mail.fetch(num, "(RFC822)")
                    emails.append(msg_data[0][1])
                    mail.store(num, "+FLAGS", "\\Seen")  # 标记为已读
                return emails

        return await asyncio.to_thread(sync_fetch)

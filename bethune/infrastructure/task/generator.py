import asyncio
import uuid
from abc import ABC
from abc import abstractmethod

from redis import Redis

from bethune.db.session_context import session_context
from bethune.infrastructure.task.messages import InsuranceReminderMessage
from bethune.infrastructure.task.messages import ReminderMessage
from bethune.infrastructure.task.task import TaskFactory
from bethune.logging import logger
from bethune.model.reminder import BusinessType
from bethune.model.reminder import ReminderConfig
from bethune.service.insurance.factory import ServiceFactory as InsuranceServiceFactory
from bethune.service.reminder.factory import ServiceFactory as ReminderServiceFactory
from bethune.service.system.factory import ServiceFactory as SystemServiceFactory
from bethune.util.date import add_days
from bethune.util.date import format_date
from bethune.util.date import get_current_date


class TaskGenerator(ABC):
    def __init__(self, redis_conn: Redis, task_factory: TaskFactory, task_name: str):
        self._redis = redis_conn
        self.task_factory = task_factory
        self.task_name = task_name

    async def _acquire_lock(self):
        """获取任务生成锁"""
        self._lock = self._redis.lock(f"task_lock:{self.task_name}", timeout=300)
        return await self._lock.acquire(blocking=True)

    async def _release_lock(self):
        """释放任务生成锁"""
        if hasattr(self, "_lock"):
            await self._lock.release()
            logger.info("锁已释放")

    async def _close_redis(self):
        """关闭Redis连接"""
        await self._redis.close()

    async def _update_progress(self, progress_key: str, last_business_id: int):
        """更新处理进度
        Args:
            progress_key: Redis key
            last_business_id: 最后处理的business_id
        """
        if last_business_id > int(await self._redis.get(progress_key) or 0):  # type: ignore
            await self._redis.set(progress_key, last_business_id)  # type: ignore

    def _get_progress_key(self, broker_id: int, reminder_days: int) -> str:
        """获取进度key
        Args:
            broker_id: 经纪人ID
            reminder_days: 提醒天数
        """
        return f"task_progress:{broker_id}:{reminder_days}"

    async def _get_last_processed_business_id(self, progress_key: str) -> int:
        """获取上次处理的business_id
        Args:
            progress_key: Redis key
        """
        return int(await self._redis.get(progress_key) or 0)  # type: ignore

    async def _produce_task(self, queue: str, task: dict):
        """将任务推送到Redis队列"""
        task_key = self._get_task_key(task).lower()

        # 检查是否已存在
        if await self._redis.exists(task_key):  # type: ignore
            logger.info(f"任务已存在，跳过: {task_key}")
            return

        # 推送任务并设置24小时有效期
        await self.task_factory.produce_task(queue, task)
        await self._redis.set(task_key, 1, ex=86400)  # type: ignore
        logger.info(f"成功生成任务: {task['task_id']}")

    @classmethod
    def _get_task_key(cls, task: dict) -> str:
        return f"task:{task['notify_methods']}:{task['business_type']}:{task['reminder_type']}:{task['business_id']}"

    @abstractmethod
    async def get_task_data(self) -> ReminderMessage:
        """获取业务数据，由子类实现"""

    @abstractmethod
    async def create_task(self, data, config) -> dict:
        """创建单个任务，由子类实现"""

    async def get_task_queue(self, task: ReminderMessage) -> str:
        return f"reminder_tasks:{task.notify_methods.lower()}:{task.reminder_type.lower()}:{task.business_type.lower()}"

    async def generate_tasks(self):
        """生成具体业务任务并推送到Redis队列"""
        try:
            if not await self._acquire_lock():
                logger.info("无法获取锁，任务生成已取消")
                return

            total_tasks = 0
            async for tasks in self.create_task():
                for task in tasks:
                    queue = await self.get_task_queue(task)
                    await self._produce_task(queue, task.model_dump(exclude_none=True))
                    total_tasks += 1
            logger.info(f"成功生成 {total_tasks} 个任务")
        except Exception as e:
            logger.error(f"任务生成失败: {e}", exc_info=True)
        except asyncio.CancelledError as e:
            logger.error(str(e))
        finally:
            await self._release_lock()
            await self._close_redis()


class PolicyReminderTaskGenerator(TaskGenerator):
    def __init__(self, redis_conn: Redis, task_factory: TaskFactory):
        super().__init__(redis_conn, task_factory, "policy_reminder")
        self.house_insurance_policy_service = InsuranceServiceFactory.create_insurance_policy_service()
        self.reminder_config_service = ReminderServiceFactory.create_reminder_config_service()
        self.broker_service = InsuranceServiceFactory.create_broker_service()
        self.user_service = SystemServiceFactory.create_user_service()

    async def _get_expiring_policies(self, broker_id: int, reminder_days: int, last_processed_policy_id: int = 0):
        """获取即将到期的保单
        Args:
            broker_id: 经纪人ID
            reminder_days: 提醒天数
            last_processed_policy_id: 上次处理的最后一个保单ID
        """
        page_size = 100
        offset = 0

        while True:
            # 获取当前页保单
            policies = self.house_insurance_policy_service.get_policies_by_expiration(
                broker_id=broker_id,
                end_date=add_days(dt=get_current_date(), days=reminder_days),
                last_processed_policy_id=last_processed_policy_id,
                offset=offset,
                limit=page_size,
            )
            if not policies:
                break

            for policy in policies:
                yield policy

            if len(policies) < page_size:
                break

            # 更新offset
            offset += page_size

    async def _process_reminder_configs(self):
        """分页处理提醒配置"""

        # 创建服务实例
        page_size = 100
        offset = 0

        configs_page = self.reminder_config_service.get_by_example(
            example=ReminderConfig(business_type=BusinessType.POLICY_RENEWAL), offset=offset, limit=page_size
        )
        for config in configs_page:
            yield config

        # 更新offset
        offset += page_size

    async def get_task_data(self):
        """获取续保提醒任务数据"""
        with session_context():
            async for config in self._process_reminder_configs():
                # 获取上次处理的最后一个policy_id
                progress_key = self._get_progress_key(config.broker_id, config.first_reminder_days)
                last_processed_policy_id = await self._get_last_processed_business_id(progress_key)

                broker = self.broker_service.get_broker_cache_by_id(config.broker_id)
                broker_user = self.user_service.get_user_cache_by_id(broker.user_id)

                last_policy_id = last_processed_policy_id
                async for policy in self._get_expiring_policies(
                    broker_id=config.broker_id,
                    reminder_days=config.first_reminder_days,
                    last_processed_policy_id=last_processed_policy_id,
                ):
                    if not policy:
                        break

                    last_policy_id = policy.id
                    yield (broker_user, broker, policy, config)

                # 更新处理进度
                await self._update_progress(progress_key, last_policy_id)

                if config.second_reminder_days:
                    # 获取上次处理的最后一个policy_id
                    progress_key = self._get_progress_key(config.broker_id, config.second_reminder_days)
                    last_processed_policy_id = await self._get_last_processed_business_id(progress_key)

                    last_policy_id = last_processed_policy_id
                    async for policy in self._get_expiring_policies(
                        broker_id=config.broker_id,
                        reminder_days=config.second_reminder_days,
                        last_processed_policy_id=last_processed_policy_id,
                    ):
                        if not policy:
                            break

                        last_policy_id = policy.id
                        yield (broker_user, broker, policy, config)

                    # 更新处理进度
                    await self._update_progress(progress_key, last_policy_id)

    async def create_task(self):
        """创建续保提醒任务"""
        async for broker_user, broker, policy, config in self.get_task_data():
            if not (broker_user or broker or policy or config):
                break

            tasks = []
            for notify_method in config.notify_methods.split(","):
                tasks.append(
                    InsuranceReminderMessage(
                        task_id=str(uuid.uuid4()),
                        business_id=policy.id,
                        broker_id=broker.id,
                        broker_name=broker.name,
                        broker_email=broker_user.email,
                        broker_phone=broker.phone,
                        customer_name=policy.customer_name,
                        customer_email=policy.email,
                        expiration_date=format_date(policy.end_date),
                        notify_methods=notify_method,
                        business_type=BusinessType.POLICY_RENEWAL,
                        insurance_type=policy.insurance_type,
                        reminder_type=config.reminder_type,
                    )
                )
                yield tasks

from bethune.db.redis import get_redis
from bethune.db.session_context import session_context
from bethune.logging import logger
from bethune.service.insurance.factory import ServiceFactory as InsuranceServiceFactory
from bethune.settings import settings


class LeadExpirationProcessor:
    def __init__(self):
        self.async_redis = get_redis()
        self.lead_service = InsuranceServiceFactory.create_lead_service()
        self.lock_key = "lead_expiration_lock"

    async def _acquire_lock(self):
        """获取分布式锁"""
        return await self.async_redis.set(self.lock_key, "locked", nx=True, ex=300)

    async def _release_lock(self):
        """释放分布式锁"""
        await self.async_redis.delete(self.lock_key)

    async def _process_lead(self):
        with session_context():
            await self.lead_service.update_leads_by_expiration(settings.LEAD_EXPIRED_DAYS)

    async def run(self):
        """运行过期线索处理任务"""
        try:
            # 获取分布式锁
            if await self._acquire_lock():
                await self._process_lead()
        except Exception as e:
            logger.error(f"处理过期线索失败: {e}", exc_info=True)
        finally:
            await self._release_lock()
            # await self.async_redis.close()


async def daily_lead_expired_job():
    """每日过期线索处理任务"""
    processor = LeadExpirationProcessor()
    await processor.run()

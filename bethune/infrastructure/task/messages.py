import json

from pydantic import BaseModel

from bethune.api.dto.base import BusinessType
from bethune.api.dto.base import InsuranceType
from bethune.api.dto.reminder import ReminderType
from bethune.model.reminder import NotifyMethod


class ReminderMessage(BaseModel):
    """Base task data class"""

    task_id: str
    notify_methods: NotifyMethod
    insurance_type: InsuranceType
    business_type: BusinessType
    reminder_type: ReminderType

    business_id: int  # 提醒的业务ID


class EmailMessageMixin(BaseModel):
    """Email message mixin"""

    subject: str | None = None
    recipients: list[str] | None = None
    template_name: str | None = None
    template_context: dict | None = None

    async def to_email_message(self):
        pass


class InboxMessageMixin(BaseModel):
    """Inbox message mixin"""

    receiver_id: int | None = None
    content: str | None = None
    extra_content: str | None = None

    async def to_inbox_message(self):
        pass


class InsuranceReminderMessage(ReminderMessage, EmailMessageMixin, InboxMessageMixin):
    """Insurance reminder specific task data"""

    broker_id: int
    broker_name: str
    broker_email: str
    broker_phone: str
    customer_name: str
    customer_email: str
    expiration_date: str

    def __init__(self, **data):
        super().__init__(**data)
        self.business_type = BusinessType.POLICY_RENEWAL

    def to_email_message(self):
        """Convert reminder message to email message"""
        # Get template and subject
        template_name, subject, recipients = self._get_template_and_subject()

        return EmailMessageMixin(
            subject=subject,
            recipients=recipients,
            template_name=template_name,
            template_context={
                "customer_name": self.customer_name,
                "expiration_date": self.expiration_date,
                "broker_name": self.broker_name,
                "broker_email": self.broker_email,
                "broker_phone": self.broker_phone,
            },
        )

    def to_inbox_message(self):
        """Prepare all message details for insurance policy renewal"""

        """Generate insurance-specific message content"""
        content_map = {
            BusinessType.POLICY_RENEWAL.value: {
                ReminderType.BROKER.value: f"{self.customer_name}的历史保单将于{self.expiration_date}到期，请尽快跟进续保"
            }
        }

        return InboxMessageMixin(
            receiver_id=self.broker_id,
            content=content_map.get(BusinessType.POLICY_RENEWAL.value, {}).get(
                self.reminder_type.value, "您有一条新的保险提醒消息"
            ),
            extra_content=json.dumps({"customer_name": self.customer_name}),
        )

    def _get_template_and_subject(self):
        """Get insurance-specific template and subject"""

        business_type = self.business_type.value
        reminder_type = self.reminder_type.value

        template_map = {
            business_type: {
                ReminderType.BROKER.value: "policy_renewal_reminder_broker.html",
                ReminderType.CUSTOMER.value: "policy_renewal_reminder_customer.html",
            }
        }

        subject_map = {
            business_type: {
                ReminderType.BROKER.value: f"【续保提醒】请及时联系客户{self.customer_name}，保单将于{self.expiration_date}到期",
                ReminderType.CUSTOMER.value: f"【保单即将到期】请尽快续保，避免保障中断（到期日：{self.expiration_date}）",
            }
        }

        return (
            template_map.get(business_type, {}).get(reminder_type),
            subject_map.get(business_type, {}).get(reminder_type),
            [self.broker_email] if reminder_type == ReminderType.BROKER.value else [self.customer_email],
        )

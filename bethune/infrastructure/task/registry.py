from .implementations import BaseTask
from .implementations import PolicyRenewalEmailTask
from .implementations import PolicyRenewalInboxTask


class TaskRegistry:
    def __init__(self):
        self._task_types: dict[str, type[BaseTask]] = {}
        self._queues: list[str] = []

    def register_task(self, task_type: str, task_class: type[BaseTask], queue: str):
        self._task_types[task_type] = task_class
        if queue not in self._queues:
            self._queues.append(queue)

    def get_task_class(self, task_type: str) -> type[BaseTask]:
        if task_type not in self._task_types:
            raise ValueError(f"Unknown task type: {task_type}")
        return self._task_types[task_type]

    def get_queues(self) -> list[str]:
        return self._queues


def initialize_task_registry() -> TaskRegistry:
    """Initialize and configure the task registry with all task types"""
    registry = TaskRegistry()

    # Register all task types with their corresponding queues
    registry.register_task(
        "EMAIL_CUSTOMER_POLICY_RENEWAL", PolicyRenewalEmailTask, "reminder_tasks:email:customer:policy_renewal"
    )
    registry.register_task(
        "EMAIL_BROKER_POLICY_RENEWAL", PolicyRenewalEmailTask, "reminder_tasks:email:broker:policy_renewal"
    )

    registry.register_task(
        "INBOX_CUSTOMER_POLICY_RENEWAL", PolicyRenewalInboxTask, "reminder_tasks:inbox:customer:policy_renewal"
    )
    registry.register_task(
        "INBOX_BROKER_POLICY_RENEWAL", PolicyRenewalInboxTask, "reminder_tasks:inbox:broker:policy_renewal"
    )
    return registry

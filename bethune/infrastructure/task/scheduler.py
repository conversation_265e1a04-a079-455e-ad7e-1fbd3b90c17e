from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from bethune.db.redis import get_redis
from bethune.infrastructure.task.generator import PolicyReminderTaskGenerator
from bethune.infrastructure.task.lead_expiration import daily_lead_expired_job
from bethune.infrastructure.task.task import RedisTaskFactory
from bethune.logging import logger


scheduler = None
redis_conn = None
task_factory = None


async def init_scheduler():
    global scheduler
    global redis_conn

    try:
        scheduler = AsyncIOScheduler()  # 使用异步调度器
        redis_conn = get_redis()
        task_factory = RedisTaskFactory()

        async def daily_reminder_job():
            generator = PolicyReminderTaskGenerator(redis_conn=redis_conn, task_factory=task_factory)
            await generator.generate_tasks()

        scheduler.add_job(daily_reminder_job, trigger=CronTrigger(hour=0, minute=0), id="daily_reminder_generation")
        logger.info("Scheduler daily_reminder_generation started successfully")

        scheduler.add_job(
            daily_lead_expired_job, trigger=IntervalTrigger(minutes=30), id="interval_lead_expired_generation"
        )
        scheduler.start()
        logger.info("Scheduler daily_reminder_generation, interval_lead_expired_generation started successfully")
    except Exception as e:
        logger.error(f"Failed to start scheduler: {e}", exc_info=True)


async def shutdown_scheduler():
    if scheduler and scheduler.running:
        scheduler.shutdown()  # 异步关闭
        logger.info("Scheduler stopped")

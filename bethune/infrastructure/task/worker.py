import asyncio

from .task import RedisTaskFactory
from bethune.logging import logger

worker_task = None
task_factory: RedisTaskFactory | None = None


async def worker_loop():
    try:
        while True:
            # 异步阻塞获取任务
            task_queues = task_factory.get_task_queues()
            if not task_queues:
                logger.info("No task queues available, waiting...")
                await asyncio.sleep(60)
                continue

            result = await task_factory.consume_task(task_queues)
            if not result:
                logger.info("No tasks available, waiting...")
                await asyncio.sleep(60)
                continue

            queue, task_data = result
            logger.info(f"Processing task from queue: {queue}")
            try:
                task = task_factory.create_task(task_data)
                if task:
                    await task.run()
                    logger.info(f"Task completed successfully: {queue}")
                else:
                    logger.warning(f"Failed to create task from queue: {queue}")
            except Exception as e:
                logger.error(f"Error processing task from queue {queue}: {str(e)}", exc_info=True)
    except Exception as e:
        logger.error(f"Worker loop error: {str(e)}", exc_info=True)


def start_worker():
    global worker_task
    global task_factory
    task_factory = RedisTaskFactory()
    if not worker_task or worker_task.done():
        worker_task = asyncio.create_task(worker_loop())
        logger.info("Background worker started")


def stop_worker():
    if worker_task and not worker_task.done():
        worker_task.cancel()
        logger.info("Background worker stopped")

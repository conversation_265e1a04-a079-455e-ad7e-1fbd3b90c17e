import inspect
import logging
import sys
from logging.handlers import TimedRotatingFileHandler

import pretty_errors
from loguru import logger
from starlette_context import context
from starlette_context.errors import ContextDoesNotExistError
from starlette_context.plugins import RequestIdPlugin

from bethune.settings import settings


def request_id_filter(record: dict) -> dict:
    """Add request ID to log record if available."""
    try:
        record["request_id"] = context.get(RequestIdPlugin.key)
    except ContextDoesNotExistError:
        record["request_id"] = ""
    return record


class InterceptHandler(logging.Handler):
    def emit(self, record: logging.LogRecord) -> None:
        # Get corresponding Loguru level if it exists.
        level: str | int
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message.
        frame, depth = inspect.currentframe(), 0
        while frame and (depth == 0 or frame.f_code.co_filename == logging.__file__):
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
logging.getLogger("dogpile.cache").setLevel(logging.DEBUG)

pretty_errors.configure(
    separator_character="*",
    filename_display=pretty_errors.FILENAME_EXTENDED,
    line_number_first=True,
    display_link=True,
    lines_before=5,
    lines_after=2,
    line_color=pretty_errors.RED + "> " + pretty_errors.default_config.line_color,
    code_color="  " + pretty_errors.default_config.line_color,
    truncate_code=True,
    display_locals=True,
)

log_file_handler = TimedRotatingFileHandler(
    filename=settings.LOGGING_FILE_PATH,
    when="midnight",
    interval=1,
    backupCount=7,
    encoding="utf-8",
)

logger.configure(
    handlers=[
        {
            "sink": sys.stdout,
            "filter": request_id_filter,
            "format": "[{time}][{level}][{name}][{process.name}][{thread.name}][{line}][{request_id}][{message}][{exception}]",
            "level": settings.LOG_LEVEL,
            "enqueue": True,
            "colorize": True,
        },
        {
            "sink": log_file_handler,
            "filter": request_id_filter,
            "format": "[{time}][{level}][{name}][{process.name}][{thread.name}][{line}][{request_id}][{message}][{exception}]",
            "level": settings.LOG_LEVEL,
            "enqueue": True,
            "colorize": True,
        },
    ],
)

from collections.abc import Callable
from datetime import datetime
from datetime import timezone
from enum import StrEnum
from typing import Any
from typing import TypeVar

from pydantic import BaseModel as PydanticBaseModel
from pydantic.fields import FieldInfo
from sqlalchemy import TIMESTAMP
from sqlalchemy.sql.elements import BinaryExpression
from sqlmodel import Field
from sqlmodel import Relationship
from sqlmodel import SQLModel
from sqlmodel.sql.expression import Select
from sqlmodel.sql.expression import SelectOfScalar


class ConsultationStatus(StrEnum):
    PENDING = "PENDING"  # 待处理
    COMPLETED = "COMPLETED"  # 完成


class ReferralFeeTypeEnum(StrEnum):
    FIXED = "FIXED"
    PREMIUM_PERCENTAGE = "PREMIUM_PERCENTAGE"


class GenderEnum(StrEnum):
    MALE = "MALE"
    FEMALE = "FEMALE"
    OTHER = "OTHER"


class InsuranceType(StrEnum):
    """保险类型"""

    """房屋险"""
    HOUSE_INSURANCE = "HOUSE_INSURANCE"
    """租客险"""
    RENTERS_INSURANCE = "RENTERS_INSURANCE"
    """车险"""
    AUTO_INSURANCE = "AUTO_INSURANCE"
    """寿险"""
    LIFE_INSURANCE = "LIFE_INSURANCE"


class BaseModel(SQLModel):
    id: int | None = Field(default=None, primary_key=True)


class WithReferenceNumber:
    ref_code: str | None = Field(default=None, unique=True)


class AuditMixin:
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), sa_column=TIMESTAMP)
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), sa_column=TIMESTAMP)


class ExtendedAuditMixin(AuditMixin):
    is_deleted: bool = False
    deleted_at: datetime = Field(default=None, sa_column=TIMESTAMP)


BusinessModel = TypeVar("BusinessModel", bound=BaseModel)


class QueryFilterFieldInfo(FieldInfo):
    def __init__(
        self,
        *,
        filter_func: Callable[[Any], BinaryExpression | bool],
        convert_func: Callable[[Any], Any] = lambda x: x,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.filter_func = filter_func
        self.convert_func = convert_func


class BaseQueryFilters(PydanticBaseModel):

    def add_condition(self, statement: Select | SelectOfScalar, field_name: str) -> Select | SelectOfScalar:
        field_info = self.__class__.model_fields[field_name]
        if not isinstance(field_info, QueryFilterFieldInfo) or getattr(self, field_name) is None:
            return statement
        field_value = getattr(self, field_name)
        converted_value = field_info.convert_func(field_value)
        return statement.where(field_info.filter_func(converted_value))  # type: ignore

    def add_conditions(
        self,
        statement: Select | SelectOfScalar,
    ) -> Select | SelectOfScalar:
        for field_name in self.__class__.model_fields.keys():
            print(field_name)
            statement = self.add_condition(statement, field_name)
        return statement


def QueryFilterField(  # noqa: N802,  follow pydantic style
    filter_func: Callable[[Any], BinaryExpression | bool],
    *,
    convert_func: Callable[[Any], Any] = lambda x: x,
    default: Any = None,
    alias: str | None = None,
    title: str | None = None,
    description: str | None = None,
) -> QueryFilterFieldInfo:
    return QueryFilterFieldInfo(
        filter_func=filter_func,
        convert_func=convert_func,
        default=default,
        alias=alias,
        title=title,
        description=description,
    )


def OneToOne(  # noqa: N802,  follow pydantic style
    back_populates: str, foreign_keys: Any, primary_join: Any, **kwargs
) -> Any:
    """简化一对一关系字段的定义"""
    return Relationship(
        sa_relationship_kwargs={
            "uselist": False,
            "foreign_keys": foreign_keys,
            "primaryjoin": primary_join,
        },
        back_populates=back_populates,
        **kwargs,
    )

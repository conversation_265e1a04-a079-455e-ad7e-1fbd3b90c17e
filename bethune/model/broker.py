from pydantic import EmailStr
from sqlalchemy.dialects.mysql import SET
from sqlmodel import Column
from sqlmodel import Field
from sqlmodel import Relationship

from bethune.model.base import AuditMixin
from bethune.model.base import BaseModel
from bethune.model.base import ConsultationStatus
from bethune.model.base import GenderEnum
from bethune.model.base import InsuranceType
from bethune.model.base import OneToOne
from bethune.model.base import ReferralFeeTypeEnum
from bethune.model.brokerage import Brokerage
from bethune.model.insurance import InsuranceApplication
from bethune.model.payment import AccountTypeEnum
from bethune.model.system import User


class BrokerBase(BaseModel, AuditMixin):
    uid: str = Field(index=True, description="代理人唯一标识符")
    user_id: int | None = Field(foreign_key="user.id")
    name: str
    gender: GenderEnum | None = None
    address: str = ""
    province: str = ""
    city: str = ""
    postal_code: str = ""
    phone: str = ""
    insurance_type: InsuranceType = InsuranceType.HOUSE_INSURANCE
    insurance_company: str = ""
    description: str | None = Field(default=None, description="代理人描述信息")
    brokerage_id: int | None = Field(default=None, foreign_key="brokerage.id", description="经纪公司ID(如果有的话)")


class Broker(BrokerBase, table=True):  # type: ignore

    lead_config: "BrokerLeadConfig" = OneToOne(
        "broker",
        "BrokerLeadConfig.broker_id",
        "Broker.id == BrokerLeadConfig.broker_id",
    )
    payment_method: "BrokerPaymentMethod" = OneToOne(
        "broker",
        "BrokerPaymentMethod.broker_id",
        "Broker.id == BrokerPaymentMethod.broker_id",
    )
    profile: "BrokerProfile" = OneToOne(
        "broker",
        "BrokerProfile.broker_id",
        "Broker.id == BrokerProfile.broker_id",
    )
    user: User = Relationship()
    brokerage: Brokerage = Relationship()


class BrokerLeadConfig(BaseModel, AuditMixin, table=True):  # type: ignore
    """线索配置核心字段"""

    __tablename__ = "broker_lead_config"

    broker_id: int = Field(foreign_key="broker.id", description="关联代理人ID")
    insurance_type: str = Field(..., sa_column=Column(SET(*[InsuranceType]), nullable=False, comment="保险类型"))
    allow_leads: bool = Field(default=False, description="是否允许接受线索")
    willing_pay_for_leads: bool = Field(default=True, description="是否愿意为最终能成交线索进行事后付费")

    broker: "Broker" = OneToOne("lead_config", "BrokerLeadConfig.broker_id", "BrokerLeadConfig.broker_id == Broker.id")

    class Config:
        arbitrary_types_allowed = True

    @property
    def insurance_type_list(self) -> list[str]:
        if isinstance(self.insurance_type, set):
            return list(self.insurance_type)
        return self.insurance_type.split(",") if self.insurance_type else []

    @insurance_type_list.setter
    def insurance_type_list(self, value: list[str]):
        if value is not None and isinstance(value, list):
            self.insurance_type = ",".join(value)
        else:
            self.insurance_type = f"{InsuranceType.HOUSE_INSURANCE.value}"  # set default value


class BrokerLeadFee(BaseModel, AuditMixin, table=True):  # type: ignore
    """代理人线索付费配置"""

    __tablename__ = "broker_lead_fee"

    broker_id: int = Field(foreign_key="broker.id", description="关联代理人ID")
    insurance_type: InsuranceType = InsuranceType.HOUSE_INSURANCE
    referral_fee_type: ReferralFeeTypeEnum = Field(
        default=ReferralFeeTypeEnum.PREMIUM_PERCENTAGE, description="推荐费类型"
    )
    referral_fee_value: float = Field(default=0, description="推荐费的值（类型不同则含义不同）")


class BrokerPaymentMethod(BaseModel, AuditMixin, table=True):  # type: ignore
    """支付配置核心字段"""

    __tablename__ = "broker_payment_method"

    broker_id: int = Field(foreign_key="broker.id", description="关联代理人ID")
    account_type: AccountTypeEnum = Field(default=AccountTypeEnum.E_TRANSFER, description="账户类型")
    account_number: str = Field(max_length=255, default="")
    is_default: bool = Field(default=True, description="是否是默认支付方式")

    broker: "Broker" = OneToOne(
        "payment_method", "BrokerPaymentMethod.broker_id", "BrokerPaymentMethod.broker_id == Broker.id"
    )


class BrokerProfile(BaseModel, AuditMixin, table=True):  # type: ignore
    """代理人档案核心字段"""

    __tablename__ = "broker_profile"

    broker_id: int = Field(foreign_key="broker.id", description="关联代理人ID")
    public_fields: str = Field(default="", description="开放允许别人查看的个人档案信息字段")

    broker: "Broker" = OneToOne("profile", "BrokerProfile.broker_id", "BrokerProfile.broker_id == Broker.id")
    qualification: "BrokerQualification" = OneToOne(
        "profile",
        "BrokerQualification.broker_profile_id",
        "BrokerProfile.id == BrokerQualification.broker_profile_id",
    )  # 暂时一对一（一个代理人可以拥有多种不同认证的资质）


class BrokerQualification(BaseModel, AuditMixin, table=True):  # type: ignore
    """资质认证核心字段（后续随需添加资质类型等新的字段）"""

    __tablename__ = "broker_qualification"

    broker_profile_id: int = Field(foreign_key="broker_profile.id", description="关联档案ID")
    is_qualified: bool = Field(default=False, description="是否是认证的代理人")

    profile: "BrokerProfile" = OneToOne(
        "qualification",
        "BrokerQualification.broker_profile_id",
        "BrokerQualification.broker_profile_id == BrokerProfile.id",
    )


class BrokerRichInfoComposite(BrokerBase):
    email: EmailStr | None = None
    avatar: str | None = None
    profile_public_fields: str | None = None
    lead_config_referral_fee_type: ReferralFeeTypeEnum | None = None
    lead_config_referral_fee_value: float | None = None
    lead_config_willing_pay_for_leads: bool | None = None
    brokerage_name: str | None = None


class PromotionMaterial(BaseModel, AuditMixin, table=True):  # type: ignore
    """推广素材表"""

    __tablename__ = "promotion_material"

    broker_id: int = Field(foreign_key="broker.id", description="关联代理人ID")
    image: str = Field(default="", description="推广图片")


class InsuranceConsultationApplication(BaseModel, AuditMixin, table=True):  # type: ignore
    """保险咨询申请表"""

    __tablename__ = "insurance_consultation_application"

    insurance_consultation: int = Field(foreign_key="insurance_consultation.id", description="咨询ID")
    application_id: int = Field(foreign_key="insurance_application.id", description="申请ID")


class InsuranceConsultationCustomer(BaseModel, AuditMixin, table=True):  # type: ignore
    """保险咨询客户表"""

    __tablename__ = "insurance_consultation_customer"

    insurance_consultation: int = Field(foreign_key="insurance_consultation.id", description="咨询ID")
    customer_id: int = Field(foreign_key="customer.id", description="客户ID")


class InsuranceConsultation(BaseModel, AuditMixin, table=True):  # type: ignore
    """保险咨询表"""

    __tablename__ = "insurance_consultation"

    broker_id: int = Field(foreign_key="broker.id", description="关联代理人ID")
    source: str = Field(default="", description="咨询来源")
    status: ConsultationStatus | None = Field(default=ConsultationStatus.PENDING, description="咨询状态")
    province: str = ""
    city: str = ""
    postal_code: str = ""
    phone: str = ""
    name: str = Field(default="", description="姓名")
    address: str = Field(default="", description="地址")
    email: str | None = Field(default="", description="邮箱")
    remark: str | None = Field(default="", description="备注")

    insurance_application: InsuranceApplication = Relationship(link_model=InsuranceConsultationApplication)

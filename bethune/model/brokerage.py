from sqlmodel import Field

from .base import AuditMixin
from .base import BaseModel


class Brokerage(BaseModel, AuditMixin, table=True):  # type: ignore
    """经纪公司模型"""

    __tablename__ = "brokerage"

    name: str = Field(index=True, description="经纪公司名称")
    province: str = Field(default="", description="省份")
    city: str = Field(default="", description="城市")
    address: str = Field(default="", description="经纪公司地址")
    postal_code: str = Field(default="", description="邮政编码")
    contact_name: str = Field(default="", description="联系人姓名")
    contact_email: str = Field(default="", description="联系邮箱")
    contact_phone: str = Field(default="", description="联系电话")
    logo: str | None = Field(default=None, description="经纪公司logo")
    website: str | None = Field(default=None, description="经纪公司website")
    description: str | None = Field(default=None, description="经纪公司描述")


class BrokerageTrialApplication(BaseModel, AuditMixin, table=True):  # type: ignore
    """经纪行试用申请模型"""

    __tablename__ = "brokerage_trial_application"

    name: str = Field(index=True, description="经纪行名称")
    contact_name: str = Field(default="", description="联系人姓名")
    contact_email: str = Field(index=True, default="", description="联系邮箱")
    contact_phone: str = Field(default="", description="联系电话")

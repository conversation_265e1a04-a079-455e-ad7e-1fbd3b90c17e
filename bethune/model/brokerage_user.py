from pydantic import BaseModel as PydanticBaseModel
from sqlmodel import Field
from sqlmodel import Relationship

from .base import AuditMixin
from .base import BaseModel
from .brokerage import Brokerage
from .system import User


class BrokerageUserBase(BaseModel, AuditMixin):
    """
    A class representing a user in a brokerage company.
    """

    __tablename__ = "brokerage_user"

    user_id: int = Field(foreign_key="user.id")
    brokerage_id: int = Field(foreign_key="brokerage.id", description="经纪公司ID")
    name: str = Field(description="姓名")
    phone: str = Field(description="电话")


class BrokerageUser(BrokerageUserBase, table=True):  # type: ignore

    # Relationships
    user: User = Relationship()
    brokerage: Brokerage = Relationship()


class BrokerageUserQueryFilters(PydanticBaseModel):

    brokerage_id: int = Field(description="经纪行ID")
    name: str | None = Field(default=None, description="姓名")
    email: str | None = Field(default=None, description="Email")
    role: str = Field(description="角色（broker、broker-support、brokerage-admin）")


class BrokerageUserRichInfoComposite(BrokerageUserBase):
    broker_id: int | None

    user: User
    brokerage: Brokerage

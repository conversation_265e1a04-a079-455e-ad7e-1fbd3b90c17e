from datetime import date
from datetime import timed<PERSON>ta
from typing import ClassVar
from typing import Optional
from typing import TYPE_CHECKING

from pydantic import EmailStr
from sqlmodel import col
from sqlmodel import Field
from sqlmodel import func
from sqlmodel import Relationship

from bethune.model.base import BaseModel
from bethune.model.base import BaseQueryFilters
from bethune.model.base import ExtendedAuditMixin
from bethune.model.base import GenderEnum
from bethune.model.base import QueryFilterField
from bethune.util.date import get_current_date

# 添加类型检查保护
if TYPE_CHECKING:
    from bethune.model.broker import Broker


def _mix_date(year: date | None, month_and_day: date | None) -> date:
    return func.str_to_date(
        func.concat(
            func.year(year),
            "-",
            func.month(month_and_day),
            "-",
            func.day(month_and_day),
        ),
        "%Y-%m-%d",
    )


class Customer(BaseModel, ExtendedAuditMixin, table=True):
    broker_id: int | None = Field(foreign_key="broker.id")
    name: str | None = None
    gender: GenderEnum | None = None
    address: str = ""
    province: str = ""
    city: str = ""
    postal_code: str = ""
    email: EmailStr | None = None
    phone: str | None = None
    birthday: date | None = None
    move_in_date: date | None = None
    tags: str = ""
    wechat: str = ""
    memo: str = ""
    referer_id: int | None = Field(default=None, foreign_key="broker.id")  # 确保有外键定义

    referer: Optional["Broker"] = Relationship(  # type: ignore
        sa_relationship_kwargs={
            "uselist": False,  # 一对一关系
            "primaryjoin": "Customer.referer_id == Broker.id",
            "lazy": "selectin",  # 自动加载关联对象
            "remote_side": "Broker.id",
        },
    )


class CustomerQueryFilters(BaseQueryFilters):
    """客户查询条件"""

    model_class: ClassVar[type[Customer]] = Customer

    broker_id: int = QueryFilterField(filter_func=col(model_class.broker_id).__eq__)
    is_deleted: bool = QueryFilterField(filter_func=col(model_class.is_deleted).is_, default=False)
    name: str | None = QueryFilterField(
        filter_func=col(model_class.name).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    email: str | None = QueryFilterField(
        filter_func=col(model_class.email).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    phone: str | None = QueryFilterField(
        filter_func=col(model_class.phone).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    province: str | None = QueryFilterField(filter_func=col(model_class.province).__eq__, default=None)
    city: str | None = QueryFilterField(filter_func=col(model_class.city).__eq__, default=None)
    address: str | None = QueryFilterField(
        filter_func=col(model_class.address).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    keyword: str | None = QueryFilterField(
        filter_func=lambda x: col(CustomerQueryFilters.model_class.name).like(x)
        | col(CustomerQueryFilters.model_class.email).like(x)
        | col(CustomerQueryFilters.model_class.phone).like(x),
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    tag: str | None = QueryFilterField(
        filter_func=col(model_class.tags).like,
        convert_func=lambda x: f"%|{x}|%",
        default=None,
    )
    birthday_in_days: int | None = QueryFilterField(
        filter_func=lambda x: CustomerQueryFilters.__add_birthday_condition(x),
        default=None,
    )

    @classmethod
    def __add_birthday_condition(cls, birthday_in_days: int):
        min_date = get_current_date()
        max_date = min_date + timedelta(days=birthday_in_days)
        min_date_func = _mix_date(cls.model_class.birthday, min_date)
        max_date_func = _mix_date(cls.model_class.birthday, max_date)
        return (
            col(cls.model_class.birthday).between(min_date_func, max_date_func)
            if min_date.year == max_date.year
            else (col(cls.model_class.birthday) >= min_date_func) | (col(cls.model_class.birthday) <= max_date_func)
        )

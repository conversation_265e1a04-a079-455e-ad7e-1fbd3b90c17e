from typing import Any

from pydantic import BaseModel
from pydantic import EmailStr


class Email(BaseModel):
    subject: str
    recipients: list[EmailStr]
    template_name: str
    template_context: dict | None = None


class ReceivedEmail(BaseModel):
    uid: str
    subject: str
    sender: str
    recipients: list[str]
    body_plain: str | None
    body_html: str | None
    attachments: list[dict[str, Any]]
    raw_headers: dict[str, str]
    date: str

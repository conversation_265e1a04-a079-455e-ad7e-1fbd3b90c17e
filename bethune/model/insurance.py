from datetime import date
from datetime import datetime
from decimal import Decimal
from enum import StrEnum
from typing import ClassVar

from pydantic import EmailStr
from sqlalchemy import Column
from sqlalchemy import DECIMAL
from sqlalchemy import TIMESTAMP
from sqlmodel import col
from sqlmodel import Field
from sqlmodel import func

from bethune.model.base import BaseModel
from bethune.model.base import BaseQueryFilters
from bethune.model.base import ExtendedAuditMixin
from bethune.model.base import GenderEnum
from bethune.model.base import InsuranceType
from bethune.model.base import QueryFilterField
from bethune.model.base import WithReferenceNumber
from bethune.util.date import get_current_date as current_date
from bethune.util.date import subtract_days


class InsuranceApplicationStatus(StrEnum):
    PENDING_QUOTE = "PENDING_QUOTE"  # 待算费
    QUOTING = "QUOTING"  # 报价中
    PENDING_UNDERWRITTEN = "PENDING_UNDERWRITTEN"  # 待承保
    UNDERWRITTEN = "UNDERWRITTEN"  # 已承保


class InsuranceApplication(BaseModel, WithReference<PERSON>umber, ExtendedAuditMixin, table=True):  # type: ignore
    __tablename__ = "insurance_application"

    broker_id: int = Field(foreign_key="broker.id")
    customer_id: int = Field(foreign_key="customer.id")
    serial_number: str | None = None
    insurance_type: InsuranceType = InsuranceType.HOUSE_INSURANCE
    address: str = ""
    province: str = ""
    city: str = ""
    postal_code: str = ""
    customer_name: str | None = None
    customer_gender: GenderEnum | None = None
    email: EmailStr | None = None
    phone: str | None = None
    birthday: date | None = None
    is_first_apply: bool = False
    has_rejection_record: bool = False
    expected_start_date: date | None = None
    policy_no: str | None = Field(default=None, description="保险单号(如果有的话)")
    premium: Decimal | None = Field(default=None, sa_column=Column(DECIMAL(10, 2)))
    start_date: date | None = None
    end_date: date | None = None
    status: InsuranceApplicationStatus
    brokerage_id: int | None = Field(default=None, foreign_key="brokerage.id", description="经纪公司ID(如果有的话)")
    operator_id: int | None = Field(default=None, foreign_key="brokerage_user.id", description="操作人ID(如果有的话)")
    operator_name: str | None = Field(
        description="操作人姓名(如果有的话)",
        default=None,
    )
    request_quote_at: datetime | None = Field(default=None, sa_column=TIMESTAMP, description="请求报价时间")
    quote_at: datetime | None = Field(default=None, sa_column=TIMESTAMP, description="报价时间")
    underwrite_at: datetime | None = Field(default=None, sa_column=TIMESTAMP, description="承保时间")
    memo: str | None = Field(
        default=None,
        description="备注信息(如果有的话)",
    )


class InsuranceApplicationQueryFilters(BaseQueryFilters):
    """保险单查询条件"""

    model_class: ClassVar[type[InsuranceApplication]] = InsuranceApplication

    broker_id: int = QueryFilterField(filter_func=col(model_class.broker_id).__eq__, default=None)
    brokerage_id: int | None = QueryFilterField(filter_func=col(model_class.brokerage_id).__eq__, default=None)
    is_deleted: bool = QueryFilterField(filter_func=col(model_class.is_deleted).is_, default=False)
    customer_id: int | None = QueryFilterField(filter_func=col(model_class.customer_id).__eq__, default=None)
    insurance_type: InsuranceType | None = QueryFilterField(
        filter_func=col(model_class.insurance_type).__eq__, default=None
    )
    customer_name: str | None = QueryFilterField(
        filter_func=col(model_class.customer_name).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    gender: GenderEnum | None = QueryFilterField(
        filter_func=col(model_class.customer_gender).__eq__,
        default=None,
    )
    email: str | None = QueryFilterField(
        filter_func=func.lower(model_class.email).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    phone: str | None = QueryFilterField(
        filter_func=col(model_class.phone).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    province: str | None = QueryFilterField(filter_func=col(model_class.province).__eq__, default=None)
    city: str | None = QueryFilterField(filter_func=col(model_class.city).__eq__, default=None)
    address: str | None = QueryFilterField(
        filter_func=col(model_class.address).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    operator_id: int | None = QueryFilterField(
        filter_func=col(model_class.operator_id).__eq__,
        default=None,
    )
    created_in_days: int | None = QueryFilterField(
        filter_func=col(model_class.created_at).__ge__,
        convert_func=lambda x: subtract_days(current_date(), x),
        default=None,
    )
    ref_code: str | None = QueryFilterField(
        filter_func=col(model_class.ref_code).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    # deprecated should be removed, use status_group instead
    status: InsuranceApplicationStatus | None = QueryFilterField(
        filter_func=col(model_class.status).__eq__, default=None
    )
    status_group: set[InsuranceApplicationStatus] | None = QueryFilterField(
        filter_func=col(model_class.status).in_, default=None
    )


class InsurancePolicySourceType(StrEnum):
    """保险单来源类型"""

    """线上申请"""
    ONLINE = "ONLINE"

    """导入"""
    IMPORTED = "IMPORTED"

    """手动添加"""
    MANUAL = "MANUAL"


class InsurancePolicy(BaseModel, ExtendedAuditMixin, table=True):  # type: ignore
    __tablename__ = "insurance_policy"

    broker_id: int = Field(foreign_key="broker.id")
    customer_id: int = Field(foreign_key="customer.id")
    policy_no: str | None = Field(
        default=None,
        description="保险单号(如果有的话)",
    )
    insurance_type: InsuranceType | None = None
    customer_name: str | None = None
    customer_gender: GenderEnum | None = None
    email: EmailStr | None = None
    phone: str | None = None
    address: str | None = None
    province: str | None = None
    city: str | None = None
    postal_code: str | None = None
    premium: Decimal | None = Field(default=None, sa_column=Column(DECIMAL(10, 2)))
    start_date: date | None = None
    end_date: date | None = None
    source_type: InsurancePolicySourceType = InsurancePolicySourceType.ONLINE
    application_id: int | None = Field(default=None, foreign_key="insurance_application.id")
    brokerage_id: int | None = Field(default=None, foreign_key="brokerage.id", description="经纪公司ID(如果有的话)")
    ref_code: str | None = Field(default=None, description="单据编号")

    @classmethod
    def from_application(cls, application: InsuranceApplication) -> "InsurancePolicy":
        return cls(
            broker_id=application.broker_id,
            brokerage_id=application.brokerage_id,
            customer_id=application.customer_id,
            customer_name=application.customer_name,
            customer_gender=application.customer_gender,
            email=application.email.strip().lower() if application.email else application.email,
            phone=application.phone,
            address=application.address,
            province=application.province,
            city=application.city,
            postal_code=application.postal_code,
            insurance_type=application.insurance_type,
            policy_no=application.policy_no,  # type: ignore
            premium=application.premium,
            start_date=application.start_date,
            end_date=application.end_date,
            source_type=InsurancePolicySourceType.ONLINE,
            application_id=application.id,
        )


class InsurancePolicyQueryFilters(BaseQueryFilters):
    """房屋保险单查询条件"""

    model_class: ClassVar[type[InsurancePolicy]] = InsurancePolicy

    broker_id: int = QueryFilterField(filter_func=col(model_class.broker_id).__eq__)
    customer_id: int | None = QueryFilterField(filter_func=col(model_class.customer_id).__eq__, default=None)
    brokerage_id: int | None = QueryFilterField(filter_func=col(model_class.brokerage_id).__eq__, default=None)
    is_deleted: bool = QueryFilterField(filter_func=col(model_class.is_deleted).is_, default=False)
    name: str | None = QueryFilterField(
        filter_func=col(model_class.customer_name).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    email: str | None = QueryFilterField(
        filter_func=func.lower(model_class.email).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    phone: str | None = QueryFilterField(
        filter_func=col(model_class.phone).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    province: str | None = QueryFilterField(filter_func=col(model_class.province).__eq__, default=None)
    city: str | None = QueryFilterField(filter_func=col(model_class.city).__eq__, default=None)
    address: str | None = QueryFilterField(
        filter_func=col(model_class.address).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    insurance_type: InsuranceType | None = QueryFilterField(
        filter_func=col(model_class.insurance_type).__eq__, default=None
    )
    source_type: InsurancePolicySourceType | None = QueryFilterField(
        filter_func=col(model_class.source_type).__eq__, default=None
    )
    policy_no: str | None = QueryFilterField(
        filter_func=col(model_class.policy_no).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    ref_code: str | None = QueryFilterField(
        filter_func=col(model_class.ref_code).like,
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    year: int | None = QueryFilterField(
        filter_func=lambda x: col(InsurancePolicyQueryFilters.model_class.start_date).__le__(x[1])
        & col(InsurancePolicyQueryFilters.model_class.end_date).__ge__(x[0]),
        convert_func=lambda x: (f"{x}-01-01", f"{x}-12-31"),
        default=None,
    )
    keyword: str | None = QueryFilterField(
        filter_func=lambda x: col(InsurancePolicyQueryFilters.model_class.customer_name).like(x)
        | col(InsurancePolicyQueryFilters.model_class.email).like(x)
        | col(InsurancePolicyQueryFilters.model_class.phone).like(x),
        convert_func=lambda x: f"%{x}%",
        default=None,
    )
    min_premium: Decimal | None = QueryFilterField(
        filter_func=col(model_class.premium).__ge__,
        default=None,
    )
    max_premium: Decimal | None = QueryFilterField(
        filter_func=col(model_class.premium).__le__,
        default=None,
    )

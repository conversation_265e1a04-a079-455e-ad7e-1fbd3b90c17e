from datetime import datetime

from pydantic import BaseModel

from bethune.model import Broker
from bethune.model import InsuranceApplication


class InsuranceApplicationReport(BaseModel):
    """订单信息实体类"""

    ref_code: str | None
    status: str | None
    insurance_type: str | None
    customer_name: str | None
    customer_email: str | None
    customer_phone: str | None
    created_at: datetime | None
    updated_at: datetime | None
    broker_name: str | None
    broker_email: str | None
    brokerage_name: str | None
    premium: str | None

    @classmethod
    def from_application_broker(cls, application: InsuranceApplication, broker: Broker, brokerage=None):
        return cls(
            ref_code=application.ref_code,
            status=application.status,
            insurance_type=application.insurance_type,
            customer_name=application.customer_name,
            customer_email=application.email,
            customer_phone=application.phone,
            created_at=application.created_at.isoformat(),
            updated_at=application.updated_at.isoformat(),
            broker_name=broker.name,
            broker_email=broker.user.email,
            brokerage_name=brokerage.name if brokerage else None,
            premium=str(application.premium) if application.premium is not None else None,
        )

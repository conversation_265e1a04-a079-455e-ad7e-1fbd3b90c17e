from datetime import date
from enum import StrEnum
from typing import Any

from pydantic import BaseModel as PydanticBaseModel
from pydantic import EmailStr
from sqlmodel import Field

from bethune.model.base import AuditMixin
from bethune.model.base import BaseModel
from bethune.model.base import GenderEnum
from bethune.model.base import InsuranceType
from bethune.model.base import ReferralFeeTypeEnum
from bethune.model.base import WithReferenceNumber
from bethune.model.broker import Broker
from bethune.model.payment import PaymentChannelEnum
from bethune.model.payment import PaymentCurrencyEnum
from bethune.model.payment import PaymentStatusEnum


class LeadStatusEnum(StrEnum):
    DRAFT = "DRAFT"
    PENDING = "PENDING"
    ACCEPTED = "ACCEPTED"
    PROCESSING = "PROCESSING"
    PENDING_LEAD_PAYMENT = "PENDING_LEAD_PAYMENT"
    COMPLETED = "COMPLETED"
    EXPIRED = "EXPIRED"
    WITHDRAWN = "WITHDRAWN"
    REJECTED = "REJECTED"


class LeadListQueryGroupEnum(StrEnum):
    ALL = "ALL"
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"


class LeadBase(BaseModel, WithReferenceNumber, AuditMixin):
    """线索表核心字段"""

    insurance_type: InsuranceType = InsuranceType.HOUSE_INSURANCE
    created_by: int = Field(description="创建线索的（泛）代理人ID")
    customer_id: int = Field(description="客户ID")
    customer_province: str = Field(description="客户所在省")
    customer_city: str | None = Field(description="客户所在市")
    customer_address: str | None = Field(default="", description="客户住址")
    customer_postal_code: str | None = Field(description="客户邮编")
    customer_name: str = Field(description="客户姓名")
    customer_gender: GenderEnum | None = Field(default=None, description="客户性别")
    customer_birthday: date | None = Field(description="客户出生日期")
    customer_email: EmailStr | None = Field(description="客户Email")
    customer_phone: str | None = Field(description="客户电话")
    assign_to: int | None = Field(default=None, description="指派给目标认证代理人ID")
    status: LeadStatusEnum = Field(default=LeadStatusEnum.PENDING, description="线索状态")
    referral_fee_type: ReferralFeeTypeEnum = Field(
        default=ReferralFeeTypeEnum.FIXED, description="线索推荐费的类型（来自broker）"
    )
    referral_fee_value: float = Field(default=0, description="推荐费的值（来自broker）")
    additional_info: str | None = Field(default=None, description="其他补充信息")
    is_anonymous: bool | None = Field(default=False, description="是否匿名")
    serial_number: str | None = Field(default="", description="线索编码")


class Lead(LeadBase, table=True): ...  # type: ignore[call-arg]


class LeadReferralFeePayment(BaseModel, AuditMixin, table=True):  # type: ignore
    """线索推荐费支付表核心字段"""

    __tablename__: str = "lead_referral_fee_payment"

    payee: int = Field(description="收款人ID")
    payer: int = Field(description="付款人ID")
    lead_id: int = Field(description="关联线索ID")
    payment_currency: PaymentCurrencyEnum = Field(default=PaymentCurrencyEnum.CAD, description="支付币种")
    payment_channel: PaymentChannelEnum = Field(default=PaymentChannelEnum.OFFLINE, description="支付渠道")
    payment_amount_in_cents: int = Field(default=0, description="以分为单位的金额")
    payment_status: PaymentStatusEnum = Field(default=PaymentStatusEnum.CREATED, description="支付状态")
    serial_number: str = Field(max_length=128)


class LeadQueryFilters(PydanticBaseModel):
    """线索查询条件"""

    id: int | None = Field(default=None, description="主键ID")
    status_group: LeadListQueryGroupEnum | None = Field(default=LeadListQueryGroupEnum.ALL, description="查询分组编码")
    customer_id: int | None = Field(default=None, description="客户ID")


class LeadApplication(BaseModel, AuditMixin, table=True):  # type: ignore
    """线索-保险申请关联表核心字段"""

    __tablename__ = "lead_application"

    lead_id: int = Field(description="线索ID")
    application_id: int = Field(description="申请ID")


class LeadRichInfoComposite(LeadBase):
    created_by_name: str
    created_by_phone: str | None = None
    assign_to_name: str | None = None
    application_id: int | None = None
    premium: float | None = None
    lead_referral_fee_in_cents: int | None = None
    brokerage_name: str | None = None

    assign_to_broker: Broker | None = None
    # 推荐人Broker
    created_by_broker: Broker | None = None
    insurance_info: dict[str, Any] | None = None

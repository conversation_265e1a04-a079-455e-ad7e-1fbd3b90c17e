from datetime import datetime
from enum import StrEnum

from sqlmodel import Column
from sqlmodel import Field
from sqlmodel import Relationship
from sqlmodel import TIMES<PERSON><PERSON>

from .base import AuditMixin
from .base import BaseModel
from bethune.settings import settings


ROLE_SUPER_ADMIN_ID = 1
ROLE_BROKER_ID = 2
ROLE_REFERRAL_BROKER_ID = 3
ROLE_BROKER_SUPPORT_ID = 4
ROLE_BROKERAGE_ADMIN_ID = 5

ROLE_SUPER_ADMIN_NAME = "administrator"
ROLE_BROKER_NAME = "broker"
ROLE_REFERRAL_BROKER_NAME = "referral-broker"
ROLE_BROKER_SUPPORT_NAME = "broker-support"
ROLE_BROKERAGE_ADMIN_NAME = "brokerage-admin"


class UserType(StrEnum):
    SAAS = "SAAS"
    SYSTEM = "SYSTEM"


class UserStatus(StrEnum):
    ACTIVE = "active"
    INACTIVE = "inactive"


class OAuthProvider(StrEnum):
    GOOGLE = "GOOGLE"
    APPLE = "APPLE"


class RolePermission(BaseModel, table=True):  # type: ignore
    __tablename__ = "role_permission"
    role_id: int = Field(foreign_key="role.id")
    permission_id: int = Field(foreign_key="permission.id")


class UserRole(BaseModel, table=True):  # type: ignore
    __tablename__ = "user_role"
    user_id: int = Field(foreign_key="user.id")
    role_id: int = Field(foreign_key="role.id")


class User(BaseModel, AuditMixin, table=True):  # type: ignore
    email: str = Field(unique=True)
    mobile: str | None = None
    name: str | None = None
    password: str
    user_type: UserType = UserType.SAAS
    status: UserStatus = UserStatus.ACTIVE
    avatar: str | None = None
    referer_id: int | None = None
    language: str = "zh"

    roles: list["Role"] = Relationship(link_model=UserRole)

    def __check_specific_role(self, role_name: str) -> bool:
        return next((True for role in self.roles if role.name == role_name), False)

    @property
    def all_permissions(self) -> set[str]:
        """获取用户所有权限的集合（去重）"""
        return {permission.code for role in self.roles for permission in role.permissions}

    @property
    def is_broker_role(self) -> bool:
        return self.__check_specific_role(ROLE_BROKER_NAME)

    @property
    def is_broker_support_role(self) -> bool:
        return self.__check_specific_role(ROLE_BROKER_SUPPORT_NAME)

    @property
    def is_brokerage_admin_role(self) -> bool:
        return self.__check_specific_role(ROLE_BROKERAGE_ADMIN_NAME)

    @property
    def is_referral_broker_role(self) -> bool:
        return self.__check_specific_role(ROLE_REFERRAL_BROKER_NAME)

    @property
    def avatar_url(self) -> str | None:
        if self.avatar:
            return f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{self.avatar}"
        return None


class Role(BaseModel, AuditMixin, table=True):  # type: ignore
    name: str
    description: str | None = None

    permissions: list["Permission"] = Relationship(link_model=RolePermission)


class Permission(BaseModel, AuditMixin, table=True):  # type: ignore
    code: str = Field(unique=True)
    name: str
    description: str | None = None

    roles: list["Role"] = Relationship(link_model=RolePermission)


class OAuthUserInfo(BaseModel, AuditMixin, table=True):  # type: ignore
    __tablename__ = "oauth_user_info"
    user_id: int = Field(foreign_key="user.id")
    provider: OAuthProvider = Field(default=OAuthProvider.GOOGLE)
    open_id: str
    refresh_token: str
    expires_at: datetime = Field(sa_column=Column(TIMESTAMP))

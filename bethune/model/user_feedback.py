from enum import StrEnum

from .base import AuditMixin
from .base import BaseModel


class FeedbackTypeEnum(StrEnum):
    """反馈类型枚举"""

    FEATURE_SUGGESTION = "FEATURE_SUGGESTION"
    """功能建议"""
    USAGE_ISSUE = "USAGE_ISSUE"
    """使用问题"""
    INTERFACE_OPTIMIZATION = "INTERFACE_OPTIMIZATION"
    """界面优化"""
    CONTENT_ERROR = "CONTENT_ERROR"
    """内容错误"""
    OTHER = "OTHER"
    """其他反馈"""


class UserFeedback(BaseModel, AuditMixin, table=True):  # type: ignore
    """用户反馈"""

    __tablename__ = "user_feedback"

    user_id: int
    """用户ID"""
    contact: str
    """联系方式"""
    feedback_type: FeedbackTypeEnum
    """反馈类型"""
    content: str
    """反馈内容"""

from sqlalchemy import ColumnExpressionArgument  # type: ignore
from sqlmodel import func
from sqlmodel import select

from bethune.db import session
from bethune.error import DataValidationError
from bethune.error import NotFoundError
from bethune.model import AuditMixin
from bethune.model import BaseModel
from bethune.model import BusinessModel


# default return 1000 records, it is big enough for most cases
DEFAULT_LIMIT = 1000


def to_column_expression_argument(
    model: BaseModel,
) -> list[ColumnExpressionArgument[bool]]:
    """
    根据SQLModel对象生成ColumnExpressionArgument列表， 只增加unset的字段到查询条件中
    """
    return [getattr(model.__class__, key) == value for key, value in model.model_dump(exclude_unset=True).items()]


class BaseRepository[T: BaseModel]:
    """
    BaseRepository class provides basic CRUD operations for SQLModel objects.

    Methods:
        create(obj: T) -> T:
            Adds a new object to the database and refreshes it.

        update(obj: T) -> T:
            Updates an existing object in the database with new values.

        get_by_id(id: int, safe_mode: bool = False) -> T:
            Retrieves an object by its ID. Raises NotFoundError if the object is not found and safe_mode is False.

        delete(obj: T) -> None:
            Deletes an object from the database.

        delete_by_id(id: int) -> None:
            Deletes an object from the database by its ID.
    """

    def __init__(self, model_class: type[T]):
        self.model_class = model_class

    def create(self, obj: T) -> T:
        session().add(obj)
        session().flush()
        return obj

    def create_all(self, objs: list[T]) -> list[T]:
        session().add_all(objs)
        session().flush()
        return objs

    def update(self, obj: T) -> T:
        if obj.id is None:
            raise DataValidationError(detail={"id": obj.id})
        existed = self.get_by_id(obj.id)
        existed.sqlmodel_update(obj.model_dump(exclude_unset=True))
        session().flush()
        return existed

    def get_by_id(self, id: int) -> T:
        obj: T = session().get(self.model_class, id)
        if obj is None:
            raise NotFoundError(f"{self.model_class.__name__} not found", detail={"id": id})
        return obj

    def get_by_id_safely(self, id: int) -> T | None:
        return session().get(self.model_class, id)

    def delete(self, to_be_deleted: T) -> None:
        session().delete(to_be_deleted)
        session().flush()

    def delete_by_id(self, id: int) -> None:
        self.delete(self.get_by_id(id))

    def count_by_example(self, example: T | None = None) -> int:
        statement = select(func.count(self.model_class.id))
        if example is not None:
            statement = statement.where(*to_column_expression_argument(example))
        return session().exec(statement).one()

    def get_by_example(
        self,
        example: T | None = None,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> list[T]:
        statement = select(self.model_class)
        if example is not None:
            statement = statement.where(*to_column_expression_argument(example))
        if isinstance(example, AuditMixin):
            statement = statement.order_by(self.model_class.updated_at.desc())  # type: ignore
        else:
            statement = statement.order_by(self.model_class.id.desc())  # type: ignore
        statement = statement.offset(offset).limit(limit)
        return session().exec(statement).all()


class GenericRepository:

    def create(self, obj: BusinessModel) -> BusinessModel:
        session().add(obj)
        session().flush()
        return obj

    def create_all(self, objs: list[BusinessModel]) -> list[BusinessModel]:
        session().add_all(objs)
        session().flush()
        return objs

    def update(self, obj: BusinessModel) -> BusinessModel:
        if obj.id is None:
            raise DataValidationError(detail={"id": obj.id})
        existed = self.get_by_id(type(obj), obj.id)
        existed.sqlmodel_update(obj.model_dump(exclude_unset=True))
        session().flush()
        return existed

    def get_by_id(self, model_class: type[BusinessModel], id: int) -> BusinessModel:
        obj: BusinessModel | None = session().get(model_class, id)
        if obj is None:
            raise NotFoundError(f"{model_class.__name__} not found", detail={"id": id})
        return obj

    def get_by_id_safely(self, model_class: type[BusinessModel], id: int) -> BusinessModel | None:
        return session().get(model_class, id)

    def delete(self, obj: BaseModel) -> None:
        session().delete(obj)
        session().flush()

    def delete_by_id(self, model_class: type[BusinessModel], id: int) -> None:
        self.delete(self.get_by_id(model_class, id))

    def get_by_example(
        self,
        example: BusinessModel | type[BusinessModel],
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> list[BusinessModel]:
        model_class = example if isinstance(example, type) else type(example)
        statement = select(model_class)
        if not isinstance(example, type):
            statement = statement.where(*to_column_expression_argument(example)).limit(limit).offset(offset)
        return list(session().exec(statement).all())

    def count_by_example(self, example: BusinessModel | type[BusinessModel]) -> int:
        model_class = example if isinstance(example, type) else type(example)
        statement = select(func.count()).select_from(model_class)
        if not isinstance(example, type):
            statement = statement.where(*to_column_expression_argument(example))
        return session().exec(statement).one()

    def flush(self) -> None:
        """
        Flush the current session to apply all changes to the database.
        """
        session().flush()

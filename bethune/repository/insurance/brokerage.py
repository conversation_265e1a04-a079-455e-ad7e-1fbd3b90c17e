from sqlalchemy.exc import NoResultFound
from sqlmodel import select

from bethune.db.session_context import session
from bethune.error.errors import NotFoundError
from bethune.model.brokerage import Brokerage
from bethune.repository.base import BaseRepository


class BrokerageRepository(BaseRepository[Brokerage]):

    def __init__(self):
        super().__init__(model_class=Brokerage)

    def get_by_broker_id(self, broker_id: int):
        try:
            return session().exec(select(Brokerage).where(Brokerage.broker_id == broker_id)).one()
        except NoResultFound:
            raise NotFoundError("BrokerProfile not found", detail={"broker_id": broker_id})

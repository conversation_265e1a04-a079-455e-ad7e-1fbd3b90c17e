from datetime import date
from datetime import timedelta

from pydantic import EmailStr
from sqlmodel import col
from sqlmodel import func
from sqlmodel import select
from sqlmodel.sql.expression import Select
from sqlmodel.sql.expression import SelectOfScalar

from bethune.db.session_context import session
from bethune.model import Customer
from bethune.model import CustomerQueryFilters
from bethune.model import InsurancePolicy
from bethune.model.lead import Lead
from bethune.model.lead import LeadApplication
from bethune.repository.base import BaseRepository
from bethune.repository.base import DEFAULT_LIMIT
from bethune.util.date import get_current_datetime


class CustomerRepository(BaseRepository[Customer]):

    def __init__(self):
        super().__init__(model_class=Customer)

    def mark_as_deleted(self, id: int):
        existed = self.get_by_id(id)
        existed.is_deleted = True
        existed.deleted_at = get_current_datetime()
        return self.update(existed)

    def get_customers(
        self,
        filters: CustomerQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[Customer]]:
        count_statement = filters.add_conditions(select(func.count(Customer.id)))
        total = session().exec(count_statement).one()
        query_statement = (
            filters.add_conditions(select(Customer)).order_by(col(Customer.id).desc()).offset(offset).limit(limit)
        )
        return total, session().exec(query_statement).all()

    def get_expiring_insurance_customers(
        self,
        broker_id: int,
        expired_in_days: int,
        broker_is_qualified: bool = True,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[tuple[Customer, date]]]:
        def add_common_filter(
            statement: Select | SelectOfScalar,
        ) -> Select | SelectOfScalar:
            min_date = get_current_datetime().date()
            max_date = min_date + timedelta(days=expired_in_days)

            if not broker_is_qualified:
                statement = (
                    statement.join(Lead, (Lead.customer_id == Customer.id) & (Lead.created_by == Customer.broker_id))
                    .join(LeadApplication, LeadApplication.lead_id == Lead.id)
                    .join(
                        InsurancePolicy,
                        InsurancePolicy.application_id == LeadApplication.application_id,
                    )
                )
            else:
                statement = statement.join(
                    InsurancePolicy,
                    (Customer.id == InsurancePolicy.customer_id) & (Customer.broker_id == InsurancePolicy.broker_id),
                )

            return (
                statement.where(col(Customer.broker_id) == broker_id)
                .where(col(Customer.is_deleted).is_(False))
                .where(col(InsurancePolicy.end_date).between(min_date, max_date))
            )

        count_statement = add_common_filter(select(func.count(func.distinct(Customer.id))))
        total = session().exec(count_statement).one()
        query_statement = (
            add_common_filter(
                select(
                    Customer,
                    func.min(InsurancePolicy.end_date).label("earliest_expiry_date"),
                )
            )
            .group_by(Customer.id)
            .order_by(col(Customer.id).desc())
            .offset(offset)
            .limit(limit)
        )
        return total, session().exec(query_statement).all()

    def count_new_customers(self, broker_id: int, min_date: date):
        return (
            session()
            .exec(
                select(func.count(Customer.id))
                .where(col(Customer.is_deleted).is_(False))
                .where(Customer.broker_id == broker_id)
                .where(Customer.created_at >= min_date)
            )
            .one()
        )

    def get_unique_customer(self, broker_id: int, name: str, email: str | EmailStr) -> Customer | None:
        return (
            session()
            .exec(
                select(Customer)
                .where(Customer.broker_id == broker_id)
                .where(Customer.name == name)
                .where(func.lower(Customer.email) == email.strip().lower())
            )
            .one_or_none()
        )

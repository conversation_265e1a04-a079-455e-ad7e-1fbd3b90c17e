from datetime import date
from datetime import timedelta

from sqlalchemy.exc import NoResultFound
from sqlmodel import col
from sqlmodel import func
from sqlmodel import select

from bethune.db import session
from bethune.error.errors import NotFoundError
from bethune.model import Broker
from bethune.model import Brokerage
from bethune.model import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationQueryFilters
from bethune.model.insurance_application import InsuranceApplicationReport
from bethune.model.lead import Lead
from bethune.model.lead import LeadApplication
from bethune.repository.base import BaseRepository
from bethune.repository.base import DEFAULT_LIMIT
from bethune.util.date import get_current_datetime


class InsuranceApplicationRepository(BaseRepository[InsuranceApplication]):

    def __init__(self):
        super().__init__(model_class=InsuranceApplication)

    def mark_as_deleted(self, id: int) -> InsuranceApplication:
        existed = self.get_by_id(id)
        existed.is_deleted = True
        existed.deleted_at = get_current_datetime()
        return self.update(existed)

    def get_by_broker_id(self, id: int, broker_id: int):
        try:
            return (
                session()
                .exec(
                    select(InsuranceApplication)
                    .where(InsuranceApplication.id == id)
                    .where(InsuranceApplication.broker_id == broker_id)
                    .where(col(InsuranceApplication.is_deleted).is_(False))
                )
                .one()
            )
        except NoResultFound:
            raise NotFoundError(
                "InsuranceApplication not found",
                detail={"id": id, "broker_id": broker_id},
            )

    def get_latest_application(
        self, broker_id: int, customer_ids: list[int], broker_is_qualified: bool = True, expired_in_days: int = 0
    ):
        max_ids_sub_query = (
            select(func.max(InsuranceApplication.id))
            .where(InsuranceApplication.broker_id == broker_id)
            .where(col(InsuranceApplication.customer_id).in_(customer_ids))
            .where(InsuranceApplication.end_date)
        )

        if expired_in_days:
            if not broker_is_qualified:
                max_ids_sub_query = (
                    select(func.max(InsuranceApplication.id))
                    .join(LeadApplication, LeadApplication.application_id == InsuranceApplication.id)
                    .join(Lead, Lead.id == LeadApplication.lead_id)
                    .where(Lead.created_by == broker_id)
                    .where(col(InsuranceApplication.customer_id).in_(customer_ids))
                    .where(InsuranceApplication.end_date)
                )

            min_date = get_current_datetime().date()
            max_date = min_date + timedelta(days=expired_in_days)
            max_ids_sub_query = max_ids_sub_query.where(col(InsuranceApplication.end_date).between(min_date, max_date))
        max_ids_sub_query = max_ids_sub_query.group_by(InsuranceApplication.customer_id)
        return (
            session()
            .exec(select(InsuranceApplication).where(col(InsuranceApplication.id).in_(max_ids_sub_query)))
            .all()
        )

    def count_new_applications(self, broker_id: int, min_date: date):
        return (
            session()
            .exec(
                select(func.count(InsuranceApplication.id))
                .where(col(InsuranceApplication.is_deleted).is_(False))
                .where(InsuranceApplication.broker_id == broker_id)
                .where(InsuranceApplication.created_at >= min_date)
            )
            .one()
        )

    def get_by_query_filters(
        self,
        filters: InsuranceApplicationQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[InsuranceApplication]]:
        count_statement = filters.add_conditions(select(func.count(InsuranceApplication.id)))
        total = session().exec(count_statement).one()
        statement = filters.add_conditions(select(InsuranceApplication))
        statement = statement.order_by(col(InsuranceApplication.created_at).desc())
        statement = statement.limit(limit).offset(offset)
        return total, session().exec(statement).all()

    def get_by_ref_code_and_broker_id(self, ref_code: str, broker_id: int):
        try:
            return (
                session()
                .exec(
                    select(InsuranceApplication)
                    .where(InsuranceApplication.ref_code == ref_code)
                    .where(InsuranceApplication.broker_id == broker_id)
                    .where(col(InsuranceApplication.is_deleted).is_(False))
                )
                .one()
            )
        except NoResultFound:
            raise NotFoundError(
                "InsuranceApplication not found",
                detail={"ref_code": ref_code, "broker_id": broker_id},
            )

    def get_by_ref_code(self, ref_code: str):
        try:
            return (
                session()
                .exec(
                    select(InsuranceApplication)
                    .where(InsuranceApplication.ref_code == ref_code)
                    .where(col(InsuranceApplication.is_deleted).is_(False))
                )
                .one()
            )
        except NoResultFound:
            raise NotFoundError(
                "InsuranceApplication not found",
                detail={"ref_code": ref_code},
            )

    def get_all_application_info(self) -> list[InsuranceApplicationReport]:
        results = (
            session()
            .exec(
                select(InsuranceApplication, Broker, Brokerage)
                .join(Broker, InsuranceApplication.broker_id == Broker.id)
                .outerjoin(Brokerage, Broker.brokerage_id == Brokerage.id)
            )
            .all()
        )
        return [
            InsuranceApplicationReport.from_application_broker(application, broker, brokerage)
            for application, broker, brokerage in results
        ]

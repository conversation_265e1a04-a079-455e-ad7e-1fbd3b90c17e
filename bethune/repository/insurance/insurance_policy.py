from datetime import date

from sqlmodel import col
from sqlmodel import func
from sqlmodel import select

from bethune.db.session_context import session
from bethune.model.insurance import InsurancePolicy
from bethune.model.insurance import InsurancePolicyQueryFilters
from bethune.repository.base import BaseRepository
from bethune.repository.base import DEFAULT_LIMIT
from bethune.util.date import get_current_datetime

# from sqlmodel.sql.expression import Select
# from sqlmodel.sql.expression import SelectOfScalar


# def add_conditions(  # noqa: C901
#     filters: HouseInsurancePolicyQueryFilters, statement: Select | SelectOfScalar
# ) -> Select | SelectOfScalar:
#     statement = statement.where(col(HouseInsurancePolicy.broker_id) == filters.broker_id)
#     statement = statement.where(col(HouseInsurancePolicy.is_deleted).is_(False))
#     if filters.name:
#         statement = statement.where(col(HouseInsurancePolicy.name).ilike(f"%{filters.name}%"))
#     if filters.email:
#         statement = statement.where(col(HouseInsurancePolicy.email).ilike(f"%{filters.email}%"))
#     if filters.phone:
#         statement = statement.where(col(HouseInsurancePolicy.phone).ilike(f"%{filters.phone}%"))
#     if filters.province:
#         statement = statement.where(col(HouseInsurancePolicy.province) == filters.province)
#     if filters.city:
#         statement = statement.where(col(HouseInsurancePolicy.city) == filters.city)
#     if filters.address:
#         statement = statement.where(col(HouseInsurancePolicy.address).ilike(f"%{filters.address}%"))
#     if filters.keyword:
#         keyword = f"%{filters.keyword}%"
#         statement = statement.where(
#             col(HouseInsurancePolicy.customer_name).ilike(keyword)
#             | col(HouseInsurancePolicy.email).ilike(keyword)
#             | col(HouseInsurancePolicy.phone).ilike(keyword)
#         )
#     if filters.source_type:
#         statement = statement.where(col(HouseInsurancePolicy.source_type) == filters.source_type)
#     if filters.year:
#         begin_of_the_year = f"{filters.year}-01-01"
#         end_of_the_year = f"{filters.year}-12-31"
#         statement = statement.where(HouseInsurancePolicy.start_date <= func.str_to_date(end_of_the_year))
#         statement = statement.where(HouseInsurancePolicy.end_date >= func.str_to_date(begin_of_the_year))
#     if filters.min_premium:
#         statement = statement.where(col(HouseInsurancePolicy.premium) >= filters.min_premium)
#     if filters.max_premium:
#         return statement.where(col(HouseInsurancePolicy.premium) <= filters.max_premium)
#     return statement


class InsurancePolicyRepository(BaseRepository[InsurancePolicy]):

    def __init__(self):
        super().__init__(model_class=InsurancePolicy)

    def mark_as_deleted(self, id: int):
        existed = self.get_by_id(id)
        existed.is_deleted = True
        existed.deleted_at = get_current_datetime()
        return self.update(existed)

    def get_by_query_filters(
        self,
        filters: InsurancePolicyQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[InsurancePolicy]]:
        """
        Get house insurance policies by query filters.
        """
        count_statement = select(func.count(InsurancePolicy.id))
        count_statement = filters.add_conditions(count_statement)
        total = session().exec(count_statement).one()
        statement = select(InsurancePolicy)
        statement = filters.add_conditions(statement)
        statement = statement.order_by(col(InsurancePolicy.created_at).desc())
        statement = statement.limit(limit).offset(offset)
        return total, session().exec(statement).all()

    def get_policies_by_expiration(
        self, broker_id: int, end_date: date, last_processed_policy_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT
    ):
        statement = select(InsurancePolicy)
        statement = statement.where(col(InsurancePolicy.is_deleted).is_(False))
        statement = statement.where(col(InsurancePolicy.broker_id) == broker_id)
        statement = statement.where(col(InsurancePolicy.id) > last_processed_policy_id)
        statement = statement.where(col(InsurancePolicy.end_date) == end_date)

        statement = statement.order_by(col(InsurancePolicy.id).asc())
        statement.limit(limit).offset(offset)
        return session().exec(statement).all()

    def get_by_application_id(self, application_id: int) -> InsurancePolicy | None:
        statement = select(InsurancePolicy)
        statement = statement.where(col(InsurancePolicy.application_id) == application_id)
        return session().exec(statement).one_or_none()

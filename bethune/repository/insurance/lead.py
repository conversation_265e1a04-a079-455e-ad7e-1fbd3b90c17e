from datetime import datetime

from sqlalchemy.orm import aliased
from sqlmodel import col
from sqlmodel import func
from sqlmodel import select
from sqlmodel import update
from sqlmodel.sql.expression import label
from sqlmodel.sql.expression import Select
from sqlmodel.sql.expression import SelectOfScalar

from bethune.db.session_context import session
from bethune.model import Broker
from bethune.model import Brokerage
from bethune.model import InsuranceApplication
from bethune.model.lead import Lead
from bethune.model.lead import LeadApplication
from bethune.model.lead import LeadListQueryGroupEnum
from bethune.model.lead import LeadQueryFilters
from bethune.model.lead import LeadReferralFeePayment
from bethune.model.lead import LeadRichInfoComposite
from bethune.model.lead import LeadStatusEnum
from bethune.repository.base import BaseRepository, GenericRepository
from bethune.repository.base import DEFAULT_LIMIT

_GROUP_CODE_STATUS_MAPPINGS = {
    LeadListQueryGroupEnum.ALL: None,
    LeadListQueryGroupEnum.PENDING: [LeadStatusEnum.DRAFT, LeadStatusEnum.PENDING],
    LeadListQueryGroupEnum.PROCESSING: [
        LeadStatusEnum.ACCEPTED,
        LeadStatusEnum.PROCESSING,
        LeadStatusEnum.PENDING_LEAD_PAYMENT,
    ],
    LeadListQueryGroupEnum.COMPLETED: [
        LeadStatusEnum.COMPLETED,
        LeadStatusEnum.EXPIRED,
        LeadStatusEnum.WITHDRAWN,
        LeadStatusEnum.REJECTED,
    ],
}


def _basic_statement() -> Select | SelectOfScalar:
    created_by_broker = aliased(Broker, name="created_to_broker")
    assign_to_broker = aliased(Broker, name="assign_to_broker")
    brokerage = aliased(Brokerage, name="brokerage")
    created_by_broker_direct = aliased(Broker, name="created_by_broker_direct")
    return (
        select(
            Lead,
            label("created_by_name", created_by_broker.name),
            label("created_by_phone", created_by_broker.phone),
            label("assign_to_name", assign_to_broker.name),
            LeadApplication.application_id,
            label("application_premium", InsuranceApplication.premium),
            label("lead_referral_fee_in_cents", LeadReferralFeePayment.payment_amount_in_cents),
            label("brokerage_name", brokerage.name),
            assign_to_broker,
            created_by_broker_direct,
        )
        .join(created_by_broker, Lead.created_by == created_by_broker.id, isouter=True)
        .join(assign_to_broker, Lead.assign_to == assign_to_broker.id, isouter=True)
        .join(LeadApplication, Lead.id == LeadApplication.lead_id, isouter=True)
        .outerjoin(brokerage, assign_to_broker.brokerage_id == brokerage.id)
        .join(
            InsuranceApplication,
            LeadApplication.application_id == InsuranceApplication.id,
            isouter=True,
        )
        .join(
            LeadReferralFeePayment,
            Lead.id == LeadReferralFeePayment.lead_id,
            isouter=True,
        )
        .outerjoin(created_by_broker_direct, Lead.created_by == created_by_broker_direct.id)
    )


def _add_query_conditions(
    broker: Broker, filters: LeadQueryFilters, stmt: Select | SelectOfScalar
) -> Select | SelectOfScalar:
    if filters.status_group:
        status = _GROUP_CODE_STATUS_MAPPINGS[filters.status_group]
        if status:
            stmt = stmt.where(col(Lead.status).in_(status))
    if filters.customer_id:
        stmt = stmt.where(Lead.customer_id == filters.customer_id)

    return stmt.where(
        col(Lead.assign_to if broker.profile.qualification.is_qualified else Lead.created_by) == broker.id
    )


class LeadRepository1(GenericRepository):


class LeadRepository(BaseRepository[Lead]):
    def __init__(self):
        super().__init__(model_class=Lead)

    def get_by_id_with_rich_info(self, id: int) -> LeadRichInfoComposite:
        stmt = _basic_statement().where(Lead.id == id)
        (
            lead,
            created_by_name,
            created_by_phone,
            assign_to_name,
            app_id,
            application_premium,
            lead_referral_fee_in_cents,
            brokerage_name,
            assign_to_broker,
            created_by_broker_direct,
        ) = (
            session().exec(stmt).one()
        )
        return LeadRichInfoComposite(
            **lead.model_dump(),
            application_id=app_id,
            created_by_name=created_by_name,
            created_by_phone=created_by_phone,
            assign_to_name=assign_to_name,
            premium=application_premium,
            lead_referral_fee_in_cents=lead_referral_fee_in_cents,
            brokerage_name=brokerage_name,
            assign_to_broker=assign_to_broker,
            created_by_broker=created_by_broker_direct,
        )

    def get_by_broker_id(self, broker_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> list[Lead]:
        stmt = select(Lead)
        stmt = stmt.where(Lead.created_by == broker_id)
        stmt = stmt.order_by(col(Lead.id).asc())
        stmt.limit(limit).offset(offset)
        return session().exec(stmt).all()

    def get_by_query_filters(
        self,
        broker: Broker,
        filters: LeadQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[LeadRichInfoComposite]]:
        """
        Get leads by query filters.
        """
        count_stmt = select(func.count(Lead.id))
        count_stmt = _add_query_conditions(broker, filters, count_stmt)
        total = session().exec(count_stmt).one()

        stmt = _basic_statement()
        stmt = _add_query_conditions(broker, filters, stmt)
        stmt = stmt.order_by(col(Lead.created_at).desc())
        stmt = stmt.limit(limit).offset(offset)

        results = [
            LeadRichInfoComposite(
                **lead.model_dump(),
                application_id=app_id,
                created_by_name=created_by_name,
                created_by_phone=created_by_phone,
                assign_to_name=assign_to_name,
                premium=application_premium,
                lead_referral_fee_in_cents=lead_referral_fee_in_cents,
                brokerage_name=brokerage_name,
                assign_to_broker=assign_to_broker,
                created_by_broker=created_by_broker_direct,
            )
            for lead, created_by_name, created_by_phone, assign_to_name, app_id, application_premium, lead_referral_fee_in_cents, brokerage_name, assign_to_broker, created_by_broker_direct in session()
            .exec(stmt)
            .all()
        ]

        return total, results

    async def update_leads_by_expiration(self, expired_datetime: datetime):
        stmt = (
            update(Lead)
            .where(Lead.created_at <= expired_datetime)
            .where(Lead.status == LeadStatusEnum.PENDING)
            .values(**{"status": LeadStatusEnum.EXPIRED.value})
        )

        result = session().exec(stmt)
        session().flush()
        return result.rowcount

    def get_all_lead_info(self) -> list[LeadRichInfoComposite]:
        stmt = _basic_statement()
        query_results = session().exec(stmt).all()
        # 使用列表推导式替代循环和append
        return [
            LeadRichInfoComposite(
                **lead.model_dump(),
                application_id=app_id,
                created_by_name=created_by_name,
                created_by_phone=created_by_phone,
                assign_to_name=assign_to_name,
                premium=application_premium,
                lead_referral_fee_in_cents=lead_referral_fee_in_cents,
                brokerage_name=brokerage_name,
                assign_to_broker=assign_to_broker,
                created_by_broker=created_by_broker,
            )
            for (
                lead,
                created_by_name,
                created_by_phone,
                assign_to_name,
                app_id,
                application_premium,
                lead_referral_fee_in_cents,
                brokerage_name,
                assign_to_broker,
                created_by_broker,
            ) in query_results
        ]


class LeadReferralFeePaymentRepository(BaseRepository[LeadReferralFeePayment]):

    def __init__(self):
        super().__init__(model_class=LeadReferralFeePayment)

    def get_referral_fee_payment_by_lead(self, lead_id: int) -> LeadReferralFeePayment | None:
        stmt = select(LeadReferralFeePayment).where(LeadReferralFeePayment.lead_id == lead_id)

        return session().exec(stmt).one_or_none()

    def get_total_lead_referral_fee(self, broker: Broker) -> float:
        """
        Get total lead referral fee of current referral broker.
        """
        sum_stmt = (
            select(func.coalesce(func.sum(LeadReferralFeePayment.payment_amount_in_cents), 0))
            .select_from(Lead)
            .join(
                LeadReferralFeePayment,
                Lead.id == LeadReferralFeePayment.lead_id,
            )
        ).where(Lead.created_by == broker.id)

        return session().exec(sum_stmt).one()

    def get_total_lead_count(self, broker: Broker) -> int:
        """
        Get the total count of lead of current referral broker.
        """
        total_stmt = select(func.count(Lead.id)).where(Lead.created_by == broker.id)

        return session().exec(total_stmt).one()

    def get_by_lead_ids(self, lead_ids: list[int]) -> list[LeadReferralFeePayment]:
        """
        Get referral fee payments by lead IDs.
        """
        stmt = select(LeadReferralFeePayment).where(col(LeadReferralFeePayment.lead_id).in_(lead_ids))
        return session().exec(stmt).all()


class LeadApplicationRepository(BaseRepository[LeadApplication]):

    def __init__(self):
        super().__init__(model_class=LeadApplication)

    def get_by_application_id(self, application_id: int) -> LeadApplication | None:
        statement = select(LeadApplication).where(LeadApplication.application_id == application_id)
        return session().exec(statement).one_or_none()

    def get_by_application_ids(self, application_ids: list[int]) -> list[LeadApplication]:
        statement = select(LeadApplication).where(col(LeadApplication.application_id).in_(application_ids))
        return session().exec(statement).all()

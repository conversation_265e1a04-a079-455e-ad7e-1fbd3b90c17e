from sqlalchemy.exc import NoResultFound
from sqlmodel import col
from sqlmodel import select

from bethune.db import session
from bethune.error import NotFoundError
from bethune.model.reminder import ReminderConfig
from bethune.repository.base import BaseRepository


class ReminderConfigRepository(BaseRepository[ReminderConfig]):
    def __init__(self):
        super().__init__(model_class=ReminderConfig)

    def get_by_broker(self, broker_id: int) -> ReminderConfig:
        return (
            session()
            .exec(
                select(ReminderConfig)
                .where(ReminderConfig.broker_id == broker_id)
                .order_by(col(ReminderConfig.created_at).desc())
            )
            .all()
        )

    def get_by_id_and_broker(self, id: int, broker_id: int) -> ReminderConfig:
        try:
            return (
                session()
                .exec(
                    select(ReminderConfig).where(ReminderConfig.broker_id == broker_id).where(ReminderConfig.id == id)
                )
                .one()
            )
        except NoResultFound:
            raise NotFoundError("ReminderConfig not found", detail={"id": id, "broker_id": broker_id})

    def get_by_unique_name(self, config: ReminderConfig) -> ReminderConfig:
        return (
            session()
            .exec(
                select(ReminderConfig)
                .where(ReminderConfig.broker_id == config.broker_id)
                .where(ReminderConfig.business_type == config.business_type.value)
                .where(ReminderConfig.reminder_type == config.reminder_type.value)
            )
            .one_or_none()
        )

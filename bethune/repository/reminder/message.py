from sqlalchemy import ColumnExpressionArgument  # type: ignore
from sqlmodel import col
from sqlmodel import func
from sqlmodel import select
from sqlmodel import update

from bethune.db import session
from bethune.error.errors import NotFoundError
from bethune.model.reminder import ReminderMessage
from bethune.repository.base import BaseRepository
from bethune.repository.base import DEFAULT_LIMIT


class ReminderMessageRepository(BaseRepository[ReminderMessage]):
    def __init__(self):
        super().__init__(model_class=ReminderMessage)

    def paged_list(self, example: ReminderMessage | None = None, offset: int = 0, limit: int = DEFAULT_LIMIT):

        total = self.count_by_example(example)
        if total == 0:
            return total, []

        statement = select(ReminderMessage)
        if example is not None:
            statement = statement.where(*self.__to_column_expression_argument(example))

        statement = statement.order_by(
            col(ReminderMessage.read_status).asc(),
            col(ReminderMessage.created_at).desc(),
        )

        statement = statement.offset(offset).limit(limit)
        return total, session().exec(statement).all()

    def get_by_receiver(self, receiver_id: int, read_status: int | None = None) -> list[ReminderMessage]:
        query = select(ReminderMessage).where(ReminderMessage.receiver_id == receiver_id)
        if read_status is not None:
            query = query.where(ReminderMessage.read_status == read_status)
        return session().exec(query.order_by(col(ReminderMessage.created_at).desc())).all()

    def get_unread_count(self, receiver_id: int) -> int:
        query = select(func.count(ReminderMessage.id)).where(
            ReminderMessage.receiver_id == receiver_id, ReminderMessage.read_status == 0
        )
        return session().exec(query).one()

    def mark_as_read(self, message_id: int, broker_id: int) -> ReminderMessage:
        message = self.get_by_id(message_id)
        if not message:
            raise NotFoundError("Reminder message not found", detail={"message_id": message_id})

        if message.receiver_id != broker_id:
            raise NotFoundError("Reminder message not found", detail={"message_id": message_id})

        message.read_status = 1
        return self.update(message)

    async def mark_all_as_read(self, broker_id: int):
        stmt = (
            update(ReminderMessage)
            .where(ReminderMessage.receiver_id == broker_id)
            .where(ReminderMessage.read_status == 0)
            .values(**{"read_status": 1})
        )

        result = session().exec(stmt)
        session().flush()
        return result.rowcount

    def __to_column_expression_argument(
        self,
        render_message: ReminderMessage,
    ) -> list[ColumnExpressionArgument[bool]]:
        return [
            getattr(render_message.__class__, key) == value
            for key, value in render_message.model_dump(exclude_unset=True).items()
        ]

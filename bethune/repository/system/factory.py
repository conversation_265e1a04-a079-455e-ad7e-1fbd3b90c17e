from .oauth_user_info import OAuthUserInfoRepository
from .permission import PermissionRepository
from .role import RoleRepository
from .user import UserRepository


class SystemRepositoryFactory:

    @staticmethod
    def create_user_repository() -> UserRepository:
        return UserRepository()

    @staticmethod
    def create_role_repository() -> RoleRepository:
        return RoleRepository()

    @staticmethod
    def create_permission_repository() -> PermissionRepository:
        return PermissionRepository()

    @staticmethod
    def create_oauth_user_info_repository() -> OAuthUserInfoRepository:
        return OAuthUserInfoRepository()

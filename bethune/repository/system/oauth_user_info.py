from sqlmodel import select

from bethune.db import session
from bethune.model.system import OAuthProvider
from bethune.model.system import OAuthUserInfo
from bethune.repository import BaseRepository


class OAuthUserInfoRepository(BaseRepository[OAuthUserInfo]):

    def __init__(self):
        super().__init__(model_class=OAuthUserInfo)

    def get_by_open_id_and_provider(self, open_id: str, provider: OAuthProvider) -> OAuthUserInfo | None:
        return (
            session()
            .exec(
                select(OAuthUserInfo).where(OAuthUserInfo.open_id == open_id).where(OAuthUserInfo.provider == provider)
            )
            .one_or_none()
        )

from sqlalchemy.exc import NoResultFound
from sqlmodel import select

from ..base import BaseRepository
from bethune.db import session
from bethune.error import NotFoundError
from bethune.model.system import Permission


class PermissionRepository(BaseRepository[Permission]):

    def __init__(self):
        super().__init__(model_class=Permission)

    def get_by_code(self, code: str) -> Permission:
        try:
            return session().exec(select(Permission).where(Permission.code == code)).one()
        except NoResultFound:
            raise NotFoundError("Permission not found", detail={"code": code})

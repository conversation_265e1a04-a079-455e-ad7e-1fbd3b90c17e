from sqlalchemy import distinct
from sqlmodel import delete
from sqlmodel import func
from sqlmodel import select

from bethune.db import session
from bethune.model.system import Permission
from bethune.model.system import Role
from bethune.model.system import RolePermission
from bethune.repository.base import BaseRepository
from bethune.repository.base import DEFAULT_LIMIT


class RoleRepository(BaseRepository[Role]):

    def __init__(self):
        super().__init__(model_class=Role)

    def count_permissions(self, role_id: int) -> int:
        return (
            session()
            .exec(select(func.count(distinct(RolePermission.permission_id))).where(RolePermission.role_id == role_id))
            .one()
        )

    def get_permissions(self, role_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> list[Permission]:
        return (
            session()
            .exec(
                select(Permission)
                .join(RolePermission, Permission.id == RolePermission.permission_id)
                .where(RolePermission.role_id == role_id)
                .offset(offset)
                .limit(limit)
                .order_by(Permission.id.asc())  # type: ignore
            )
            .all()
        )

    def set_permissions(self, role_id: int, permission_ids: list[int]):
        session().exec(delete(RolePermission).where(RolePermission.role_id == role_id))
        for permission_id in permission_ids:
            session().add(RolePermission(role_id=role_id, permission_id=permission_id))
        session().flush()

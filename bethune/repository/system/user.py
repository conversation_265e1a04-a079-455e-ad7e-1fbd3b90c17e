from sqlalchemy import distinct
from sqlalchemy.exc import NoResultFound
from sqlmodel import delete
from sqlmodel import func
from sqlmodel import select
from sqlmodel.sql.expression import Select
from sqlmodel.sql.expression import SelectOfScalar

from bethune.db import session
from bethune.error import NotFoundError
from bethune.model.system import Permission
from bethune.model.system import Role
from bethune.model.system import RolePermission
from bethune.model.system import User
from bethune.model.system import UserRole
from bethune.repository import BaseRepository
from bethune.repository import DEFAULT_LIMIT
from bethune.util import get_text


def _email_where_clause(stmt: Select | SelectOfScalar, email: str) -> Select | SelectOfScalar:
    return stmt.where(func.lower(User.email) == email.strip().lower())


class UserRepository(BaseRepository[User]):

    def __init__(self):
        super().__init__(model_class=User)

    def get_by_email(self, email: str) -> User:
        try:
            return session().exec(_email_where_clause(select(User), email)).one()
        except NoResultFound:
            raise NotFoundError(get_text("User not found"), detail={"email": email})

    def get_user_by_email(self, email: str) -> User:
        return session().exec(_email_where_clause(select(User), email)).one_or_none()

    def count_roles(self, user_id: int) -> int:
        return session().exec(select(func.count(Role.id)).join(UserRole).where(UserRole.user_id == user_id)).one()

    def get_roles(self, user_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> list[Role]:
        return (
            session()
            .exec(
                select(Role)
                .join(UserRole)
                .where(UserRole.user_id == user_id)
                .offset(offset)
                .limit(limit)
                .order_by(Role.id.asc())  # type: ignore
            )
            .all()
        )

    def count_permission_code(self, user_id: int) -> int:
        return (
            session()
            .exec(
                select(func.count(distinct(Permission.code)))
                .join(RolePermission, Permission.id == RolePermission.permission_id)
                .join(UserRole, RolePermission.role_id == UserRole.role_id)
                .where(UserRole.user_id == user_id)
            )
            .one()
        )

    def get_permission_code(self, user_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> list[str]:
        return (
            session()
            .exec(
                select(distinct(Permission.code))
                .join(RolePermission, Permission.id == RolePermission.permission_id)
                .join(UserRole, RolePermission.role_id == UserRole.role_id)
                .where(
                    UserRole.user_id == user_id,
                )
                .offset(offset)
                .limit(limit)
                .order_by(Permission.code.asc())  # type: ignore
            )
            .all()
        )

    def set_roles(self, user_id: int, role_ids: list[int]):
        session().exec(delete(UserRole).where(UserRole.user_id == user_id))
        for role_id in role_ids:
            session().add(UserRole(user_id=user_id, role_id=role_id))
        session().flush()

    def get_referer_ids(self, referer_id: int):
        return session().exec(select(User.id).where(User.referer_id == referer_id)).all()

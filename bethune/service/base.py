from bethune.model import BaseModel
from bethune.model.base import BusinessModel
from bethune.repository import BaseRepository
from bethune.repository.base import GenericRepository


class BaseService[T: BaseModel, R: BaseRepository[T]]:  # type: ignore
    """
    deprecated: Use BaseGenericService instead.
    """

    def __init__(self, repository: R):
        self.repository = repository

    def create(self, obj: T) -> T:
        return self.repository.create(obj)

    def create_all(self, objs: list[T]) -> list[T]:
        return self.repository.create_all(objs)

    def update(self, obj: T) -> T:
        return self.repository.update(obj)

    def get_by_id(self, id: int) -> T:
        return self.repository.get_by_id(id)

    def delete(self, obj: T) -> None:
        return self.repository.delete(obj)

    def delete_by_id(self, id: int) -> None:
        return self.repository.delete_by_id(id)

    def get_by_example(self, example: T | None = None, offset: int = 0, limit: int = 20) -> list[T]:
        return self.repository.get_by_example(example, offset, limit)

    def get_by_example_with_total(
        self, example: T | None = None, offset: int = 0, limit: int = 20
    ) -> tuple[int, list[T]]:
        total = self.repository.count_by_example(example)
        if total == 0:
            return total, []
        return total, self.repository.get_by_example(example, offset, limit)


class BaseGenericService[R: GenericRepository]:  # type: ignore

    def __init__(self, repository: R):
        self.repository = repository

    def create(self, obj: BusinessModel) -> BusinessModel:
        return self.repository.create(obj)

    def create_all(self, objs: list[BusinessModel]) -> list[BusinessModel]:
        return self.repository.create_all(objs)

    def update(self, obj: BusinessModel) -> BusinessModel:
        return self.repository.update(obj)

    def get_by_id(self, model_class: type[BusinessModel], id: int) -> BusinessModel:
        return self.repository.get_by_id(model_class, id)

    def delete(self, obj: BusinessModel) -> None:
        return self.repository.delete(obj)

    def delete_by_id(self, model_class: type[BusinessModel], id: int) -> None:
        return self.repository.delete_by_id(model_class, id)

    def get_by_example(
        self,
        example: BusinessModel | type[BusinessModel],
        offset: int = 0,
        limit: int = 20,
    ) -> list[BusinessModel]:
        return self.repository.get_by_example(example, offset, limit)

    def get_by_example_with_total(
        self,
        example: BusinessModel | type[BusinessModel],
        offset: int = 0,
        limit: int = 20,
    ) -> tuple[int, list[BusinessModel]]:
        total = self.repository.count_by_example(example)
        if total == 0:
            return total, []
        return total, self.repository.get_by_example(example, offset, limit)

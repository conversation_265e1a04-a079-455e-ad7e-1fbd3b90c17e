import imaplib

from tenacity import retry
from tenacity import retry_if_exception_type
from tenacity import stop_after_attempt
from tenacity import wait_exponential
from tenacity import wait_fixed

from bethune.infrastructure.email.email_client import EmailClient
from bethune.model.email import Email
from bethune.model.email import ReceivedEmail


class EmailService:
    def __init__(self):
        self._client = EmailClient()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type((TimeoutError, ConnectionError)),
        reraise=True,
    )
    async def send_email(self, subject: str, recipients: list, template_name: str, template_context: dict):
        """
        通用邮件发送方法
        """
        email = Email(
            subject=subject,
            recipients=recipients,
            template_name=template_name,
            template_context=template_context,
        )
        await self._client.send_email(
            subject=email.subject,
            recipients=email.recipients,
            template_name=email.template_name,
            template_context=email.template_context,
        )

    async def send_welcome_email(self, recipient_email: str, recipient_name: str):
        """
        发送欢迎邮件
        """
        await self.send_email(
            subject="Welcome to Our Service",
            recipients=[recipient_email],
            template_name="welcome_email.html",
            template_context={"name": recipient_name},
        )

    async def send_reset_password_email(self, recipient_email: str, verification_code: str):
        await self.send_email(
            subject="Reset Your Password",
            recipients=[recipient_email],
            template_name="reset_password.html",
            template_context={"verification_code": verification_code},
        )

    async def send_verification_code_email(self, recipient_email: str, verification_code: str):
        await self.send_email(
            subject="Verify Your Email",
            recipients=[recipient_email],
            template_name="send_verification_code.html",
            template_context={"verification_code": verification_code},
        )

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2), retry=retry_if_exception_type(imaplib.IMAP4.error))
    async def fetch_emails(self, limit: int = 10, unseen_only: bool = False) -> list[ReceivedEmail]:
        """
        批量获取邮件并解析
        """
        raw_emails = await self._client.fetch_raw_emails(limit, unseen_only)
        return [self._client.parse_email(raw) for raw in raw_emails]

from bethune.db.redis import get_redis
from bethune.service.core import EmailService
from bethune.service.core import VerificationCodeService
from bethune.service.core.ref_code import ReferenceCodeService


class CoreServiceFactory:

    @staticmethod
    def create_email_service() -> EmailService:
        return EmailService()

    @staticmethod
    def create_verification_code_service() -> VerificationCodeService:
        return VerificationCodeService(get_redis(), CoreServiceFactory.create_email_service())

    @staticmethod
    def create_reference_code_service() -> ReferenceCodeService:
        return ReferenceCodeService()

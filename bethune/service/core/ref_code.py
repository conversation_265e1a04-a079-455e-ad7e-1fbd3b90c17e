from enum import StrEnum

from bethune.db import redis
from bethune.settings import settings


class ReferenceTypeEnum(StrEnum):
    LEAD = "Lead"
    INSURANCE_APPLICATION = "InsuranceApplication"
    BROKER = "Broker"
    INSURANCE_POLICY = "InsurancePolicy"


class StealthCodeGenerator:
    # 混淆参数 (可自定义)
    _A = 1664525  # 乘数 (LCG参数)
    _C = 1013904223  # 增量 (LCG参数)
    _M = 2**32  # 模数 (确保32位内可逆)

    # 最大支持的序列ID (2^32 - 1)
    _MAX_SEQ_ID = 2**32 - 1

    # 字符集 (29个字符，不含易混淆字符)
    _CHAR_SET = "2345679ACDEFGHJKMNPQRSTUVWXYZ"
    _CHECK_SET = "ABCDEFGHJKLMNPQRSTUVWXYZ"  # 校验字符集

    _PREFIX_MAP = {
        ReferenceTypeEnum.LEAD: "LD",
        ReferenceTypeEnum.INSURANCE_APPLICATION: "IA",
        ReferenceTypeEnum.BROKER: "BR",
        ReferenceTypeEnum.INSURANCE_POLICY: "IP",
    }

    def __init__(self):
        self.redis = redis.get_sync_redis()

    def _obfuscate_id(self, id):
        """混淆ID: 将顺序ID转换为看似随机的数字"""
        return (self._A * id + self._C) % self._M

    def _restore_id(self, obf_id):
        """还原ID: 从混淆值还原原始ID"""
        inv_a = pow(self._A, -1, self._M)  # 使用内置pow函数求模逆元
        return (obf_id - self._C) * inv_a % self._M

    def _number_to_code(self, num, length=7):
        """将数字转换为定长编码"""
        base = len(self._CHAR_SET)
        code = []

        for _ in range(length):
            num, idx = divmod(num, base)
            code.append(self._CHAR_SET[idx])

        return "".join(code[::-1])  # 反转字符串

    def _calculate_checksum(self, code):
        """计算校验位"""
        total = sum(ord(c) for c in code)
        return self._CHECK_SET[total % len(self._CHECK_SET)]

    def generate(self, uid_type: ReferenceTypeEnum):
        """生成UID编号"""
        if uid_type not in self._PREFIX_MAP:
            raise ValueError(f"无效UID类型: {uid_type}")

        # 获取Redis计数器键
        counter_key = f"uid_counter:{uid_type}"

        # 原子操作获取自增ID
        seq_id = self.redis.incr(counter_key)

        # 检查序列ID是否超过最大值
        if seq_id > self._MAX_SEQ_ID:
            raise OverflowError(f"序列ID已达到最大值: {self._MAX_SEQ_ID}")

        # 混淆ID
        obf_id = self._obfuscate_id(seq_id)

        # 转换为7位编码
        code_body = self._number_to_code(obf_id, 7)

        # 计算校验位
        checksum = self._calculate_checksum(code_body)

        # 组合完整编号
        prefix = self._PREFIX_MAP[uid_type]
        return f"{settings.REF_CODE_ENV_PREFIX}{prefix}{code_body}{checksum}"


class ReferenceCodeService:

    def __init__(self):
        self._stealth_code_generator = StealthCodeGenerator()

    def gen_code(self, bill_type: ReferenceTypeEnum):
        return self._stealth_code_generator.generate(bill_type)

from redis.asyncio import Redis

from bethune.error.errors import VERIFICATION_CODE_EXPIRED_ERROR
from bethune.error.errors import VERIFICATION_CODE_INCORRECT_ERROR
from bethune.error.errors import VerificationCodeError
from bethune.service.core import EmailService
from bethune.settings import settings
from bethune.util.captcha import generate_captcha_text

_VERIFICATION_CODE_KEY_PREFIX = "bethune:core:verification_code"


class VerificationCodeService:
    def __init__(self, redis: Redis, email: EmailService):
        self._redis = redis
        self._email = email

    async def send_verification_email(self, email: str, length: int = 6, is_reset_password: bool = False) -> str:
        verification_code = generate_captcha_text(length)
        await self._redis.setex(f"{_VERIFICATION_CODE_KEY_PREFIX}:{email}", 600, verification_code)
        if is_reset_password is True:
            await self._email.send_reset_password_email(email, verification_code)
        else:
            await self._email.send_verification_code_email(email, verification_code)
        return email

    async def verify_verification_code(self, email: str, verification_code: str):
        if not settings.AUTH_SKIP_VERIFICATION:
            sent_verification_code = await self._redis.get(f"{_VERIFICATION_CODE_KEY_PREFIX}:{email}")
            if sent_verification_code is None:
                raise VerificationCodeError(VERIFICATION_CODE_EXPIRED_ERROR, "The verification code has expired.")

            if sent_verification_code.decode("utf-8") != verification_code:
                raise VerificationCodeError(VERIFICATION_CODE_INCORRECT_ERROR, "The verification code is incorrect.")

    async def delete_verification_code(self, email: str):
        await self._redis.delete(f"{_VERIFICATION_CODE_KEY_PREFIX}:{email}")

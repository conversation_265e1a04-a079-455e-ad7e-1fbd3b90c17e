from collections.abc import Callable
from enum import StrEnum
from typing import Any

from transitions import Machine
from transitions import State
from transitions.core import EventData

from bethune.api.error import UnauthorizedError
from bethune.error import DataValidationError
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus


class OperatorRole(StrEnum):
    """Defines the roles of operators in the state machine."""

    BROKER = "BROKER"  # 代理人
    BROKER_SUPPORT = "BROKER_SUPPORT"  # 内勤
    CUSTOMER = "CUSTOMER"  # 客户


class TriggerEnum(StrEnum):
    """Defines the triggers for state transitions."""

    EDIT = "edit"  # 编辑
    UNDERWRITE = "underwrite"  # 承保
    PAY_REFERRAL_FEE = "pay_referral_fee"  # 支付线索单手续费
    REQUEST_QUOTE = "request_quote"  # 申请算费
    QUOTE = "quote"  # 确认保费
    WITHDRAW = "withdraw"  # 撤回
    REJECT = "reject"  # 退回


REGULAR_APPLICATION_STATES = [
    State(name=InsuranceApplicationStatus.PENDING_UNDERWRITTEN),  # 待承保
    State(name=InsuranceApplicationStatus.UNDERWRITTEN),  # 已承保
]


REGULAR_APPLICATION_TRANSITIONS = [
    {
        "trigger": TriggerEnum.EDIT,  # 编辑
        "source": InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        "dest": InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        "conditions": "is_broker_or_customer",
    },
    {
        "trigger": TriggerEnum.UNDERWRITE,
        "source": InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        "dest": InsuranceApplicationStatus.UNDERWRITTEN,
        "conditions": [
            "is_broker",
            "has_premium",
            "has_start_end_date",
        ],
    },
]

BROKERAGE_APPLICATION_STATES = [
    State(name=InsuranceApplicationStatus.PENDING_QUOTE),  # 待算费
    State(name=InsuranceApplicationStatus.QUOTING),  # 算费中
    State(name=InsuranceApplicationStatus.PENDING_UNDERWRITTEN),  # 待承保
    State(name=InsuranceApplicationStatus.UNDERWRITTEN),  # 已承保
]

BROKERAGE_APPLICATION_TRANSITIONS = [
    {
        "trigger": TriggerEnum.REQUEST_QUOTE,  # 申请算费
        "source": InsuranceApplicationStatus.PENDING_QUOTE,
        "dest": InsuranceApplicationStatus.QUOTING,
        "conditions": "is_broker",
    },
    {
        "trigger": TriggerEnum.QUOTE,  # 确认保费
        "source": [
            InsuranceApplicationStatus.QUOTING,
            InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        ],
        "dest": InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        "conditions": [
            "is_broker_support",
            "has_premium",
        ],
    },
    {
        "trigger": TriggerEnum.WITHDRAW,  # 撤回
        "source": InsuranceApplicationStatus.QUOTING,
        "dest": InsuranceApplicationStatus.PENDING_QUOTE,
        "conditions": "is_broker",
    },
    {
        "trigger": TriggerEnum.EDIT,  # 编辑
        "source": [
            InsuranceApplicationStatus.PENDING_QUOTE,
        ],
        "dest": InsuranceApplicationStatus.PENDING_QUOTE,
        "conditions": "is_broker_or_customer",
    },
    {
        "trigger": TriggerEnum.EDIT,  # 编辑
        "source": [
            InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        ],
        "dest": InsuranceApplicationStatus.QUOTING,
        "conditions": "is_broker",
    },
    {
        "trigger": TriggerEnum.REJECT,  # 退回
        "source": [
            InsuranceApplicationStatus.QUOTING,
            InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        ],
        "dest": InsuranceApplicationStatus.PENDING_QUOTE,
        "conditions": "is_broker_support",
    },
    {
        "trigger": TriggerEnum.UNDERWRITE,  # 承保
        "source": [
            InsuranceApplicationStatus.PENDING_QUOTE,
        ],
        "dest": InsuranceApplicationStatus.UNDERWRITTEN,
        "conditions": [
            "is_broker",
            "has_premium",
            "has_start_end_date",
        ],
    },
    {
        "trigger": TriggerEnum.UNDERWRITE,  # 承保
        "source": [
            InsuranceApplicationStatus.QUOTING,
            InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        ],
        "dest": InsuranceApplicationStatus.UNDERWRITTEN,
        "conditions": [
            "is_broker_or_broker_support",
            "has_premium",
            "has_start_end_date",
        ],
    },
]


type StateListener = Callable[[InsuranceApplication], Any]


class ApplicationStateMachine:
    """State machine for regular insurance applications."""

    def __init__(
        self,
        application: InsuranceApplication,
        is_lead_application: bool,
        operator: OperatorRole,
        states: list,
        transitions: list,
        listeners: dict[InsuranceApplicationStatus, list[StateListener]] | None = None,
    ):
        self.application = application
        self.is_lead_application = is_lead_application
        self.operator = operator
        self._machine = Machine(
            model=self,
            states=states,
            transitions=transitions,
            initial=self.application.status,
            ignore_invalid_triggers=False,
            after_state_change=["state_updated"],
            auto_transitions=False,
            send_event=True,
        )
        self.listeners = listeners or {}

    def state_updated(self, event_data: EventData = None):
        """Update the application status based on the current state."""
        self.application.status = self.state  # type: ignore
        for listener in self.listeners.get(self.application.status, []):
            listener(self.application)

    def has_premium(self, event_data: EventData = None):
        if self.application.premium is not None:
            return True
        raise DataValidationError(detail={"detail": "premium is required."})

    def has_start_end_date(self, event_data: EventData = None):
        if self.application.start_date is None:
            raise DataValidationError(detail={"detail": "start date is required."})
        if self.application.end_date is None:
            raise DataValidationError(detail={"detail": "end date is required."})
        if self.application.start_date >= self.application.end_date:
            raise DataValidationError(detail={"detail": "start date must be before end date."})
        return True

    def is_broker(self, event_data: EventData = None):
        if self.operator == OperatorRole.BROKER:
            return True
        raise UnauthorizedError(detail={"detail": "only broker can perform this action."})

    def is_customer(self, event_data: EventData = None):
        if self.operator == OperatorRole.CUSTOMER:
            return True
        raise UnauthorizedError(detail={"detail": "only customer can perform this action."})

    def is_broker_support(self, event_data: EventData = None):
        if self.operator == OperatorRole.BROKER_SUPPORT:
            return True
        raise UnauthorizedError(detail={"detail": "only broker support can perform this action."})

    def is_broker_or_customer(self, event_data: EventData = None):
        if self.operator in {OperatorRole.BROKER, OperatorRole.CUSTOMER}:
            return True
        raise UnauthorizedError(detail={"detail": "only broker or customer can perform this action."})

    def is_broker_or_broker_support(self, event_data: EventData = None):
        if self.operator in {OperatorRole.BROKER, OperatorRole.BROKER_SUPPORT}:
            return True
        raise UnauthorizedError(detail={"detail": "only broker or broker support can perform this action."})

    @staticmethod
    def create_state_machine(
        application: InsuranceApplication,
        is_lead_application: bool,
        operator: OperatorRole,
        listeners: dict[InsuranceApplicationStatus, list[StateListener]] | None = None,
    ) -> "ApplicationStateMachine":
        """Create a state machine for the insurance application."""
        states, transitions = (
            (REGULAR_APPLICATION_STATES, REGULAR_APPLICATION_TRANSITIONS)
            if application.brokerage_id is None
            else (BROKERAGE_APPLICATION_STATES, BROKERAGE_APPLICATION_TRANSITIONS)
        )
        return ApplicationStateMachine(
            application=application,
            is_lead_application=is_lead_application,
            operator=operator,
            states=states,
            transitions=transitions,
            listeners=listeners,
        )

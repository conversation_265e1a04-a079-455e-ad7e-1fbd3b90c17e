from typing import Any

from bethune.api.dto.base import BusinessType as DtoBusinessType
from bethune.api.dto.reminder import ReminderConfigCreate as ReminderConfigCreateDto
from bethune.api.dto.reminder import ReminderType as DtoReminderType
from bethune.cache import cache
from bethune.db.redis import get_redis
from bethune.model.base import InsuranceType
from bethune.model.broker import Broker
from bethune.model.broker import BrokerLeadConfig
from bethune.model.broker import BrokerLeadFee
from bethune.model.broker import BrokerPaymentMethod
from bethune.model.broker import BrokerProfile
from bethune.model.broker import BrokerQualification
from bethune.model.broker import BrokerRichInfoComposite
from bethune.model.reminder import NotifyMethod
from bethune.repository.base import DEFAULT_LIMIT
from bethune.repository.insurance import BrokerRepository
from bethune.repository.insurance.brokerage_user import BrokerageUserRepository
from bethune.service.base import BaseGenericService

_REMINDER_FIRST_REMINDER_DAYS = 30
_BROKER_NOTIFY_METHODS = [NotifyMethod.EMAIL.value, NotifyMethod.INBOX.value]
_CUSTOMER_NOTIFY_METHODS = [NotifyMethod.EMAIL.value]


_BROKER_REMINDER_CONFIG_POLICY_RENEWAL = ReminderConfigCreateDto(
    business_type=DtoBusinessType.POLICY_RENEWAL,
    reminder_type=DtoReminderType.BROKER,
    first_reminder_days=_REMINDER_FIRST_REMINDER_DAYS,
    notify_methods=_BROKER_NOTIFY_METHODS,
)

_CUSTOMER_REMINDER_CONFIG_POLICY_RENEWAL = ReminderConfigCreateDto(
    business_type=DtoBusinessType.POLICY_RENEWAL,
    reminder_type=DtoReminderType.CUSTOMER,
    first_reminder_days=_REMINDER_FIRST_REMINDER_DAYS,
    notify_methods=_CUSTOMER_NOTIFY_METHODS,
)


class BrokerService(BaseGenericService[BrokerRepository]):

    def __init__(
        self,
        repository: BrokerRepository,
        brokerage_user_repository: BrokerageUserRepository,
    ):
        super().__init__(repository)
        self.brokerage_user_repository = brokerage_user_repository

    def __broker_tags_key(self, broker_id: int) -> str:
        return f"bethune:broker:tags:{broker_id}"

    def create_broker(
        self,
        broker: Broker,
        is_qualified: bool,
    ) -> Broker:
        created_broker = self.create(broker)  # type: ignore
        broker_id: int = created_broker.id  # type: ignore
        self.repository.create(BrokerPaymentMethod(broker_id=broker_id))
        created_broker_profile = self.repository.create(BrokerProfile(broker_id=broker_id))
        self.repository.create(
            BrokerQualification(
                is_qualified=is_qualified,
                broker_profile_id=created_broker_profile.id,
            )
        )
        self.repository.create(
            BrokerLeadConfig(
                broker_id=broker_id,
                insurance_type=[insurance_type.value for insurance_type in InsuranceType],
            )
        )
        self.repository.create_all(
            [
                BrokerLeadFee(broker_id=broker_id, insurance_type=insurance_type.value, fee=0.0)
                for insurance_type in InsuranceType
            ]
        )
        broker_reminder_configs = [
            _BROKER_REMINDER_CONFIG_POLICY_RENEWAL.to_model(broker_id),  # type: ignore
            _CUSTOMER_REMINDER_CONFIG_POLICY_RENEWAL.to_model(broker_id),  # type: ignore
        ]
        self.repository.create_all(broker_reminder_configs)
        return created_broker

    def update_broker(
        self,
        broker_id: int,
        broker: dict[str, Any],
        payment_method: dict[str, Any] | None = None,
        is_qualified: bool | None = None,
    ) -> Broker:
        existed_broker = self.repository.get_broker_by_id(broker_id)
        existed_broker.sqlmodel_update(broker)
        if payment_method:
            existed_broker.payment_method.sqlmodel_update(payment_method)
        if is_qualified is not None:
            existed_broker.profile.qualification.is_qualified = is_qualified

        if ("name" in broker or "phone" in broker) and existed_broker.brokerage is not None:
            if brokerage_user := self.brokerage_user_repository.get_by_user_id(existed_broker.user_id, allow_none=True):  # type: ignore
                brokerage_user.name = str(broker.get("name"))
                brokerage_user.phone = str(broker.get("phone"))
                self.brokerage_user_repository.update(brokerage_user)
        self.repository.flush()
        return existed_broker

    @cache.cache_on_arguments(namespace="broker-cache", expiration_time=60 * 60 * 12)
    def get_broker_cache_by_id(self, id: int) -> Broker:
        return self.repository.get_broker_by_id(id)

    def get_broker_by_id(self, id: int) -> Broker:
        return self.repository.get_broker_by_id(id)

    def get_by_uid(self, uid: str) -> Broker:
        return self.repository.get_broker_by_uid(uid)

    def get_by_ids(self, ids: list[int]) -> list[Broker]:
        return self.repository.get_broker_by_ids(ids)

    def get_by_user_id(self, user_id: int, allow_none: bool = False) -> Broker | None:
        return self.repository.get_broker_by_user_id(user_id, allow_none)

    async def add_tags(self, broker_id: int, tags: set[str]) -> int:
        if not tags:
            return 0
        return await get_redis().sadd(self.__broker_tags_key(broker_id), *tags)

    async def get_tags(self, broker_id: int) -> set[str]:
        tags = await get_redis().smembers(self.__broker_tags_key(broker_id))
        return set(tags) if tags else set()

    def my_friends(self, user_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT):
        return self.repository.get_broker_friends(user_id, offset, limit)

    def find_candidate_brokers_for_creating_lead(
        self,
        current_broker: Broker,
        insurance_type: InsuranceType,
        customer_province: str,
        customer_city: str,
        keyword: str | None = None,
        page_no: int = 1,
        page_size: int = 20,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[BrokerRichInfoComposite], BrokerRichInfoComposite | None]:
        return self.repository.get_lead_candidate_brokers(
            current_broker,
            insurance_type,
            customer_province,
            customer_city,
            keyword,
            page_no,
            page_size,
            offset,
            limit,
        )

    def update_lead_config(
        self,
        broker_id: int,
        lead_config: BrokerLeadConfig,
        lead_fees: list[BrokerLeadFee] | None = None,
        profile: BrokerProfile | None = None,
    ) -> tuple[BrokerLeadConfig, list[BrokerLeadFee] | None, BrokerProfile | None]:
        existed_lead_config = self.repository.update(lead_config)

        if profile is not None:
            profile = self.repository.update(profile)

        if lead_fees is not None:
            self.repository.delete_lead_fee_by_broker_id(broker_id)
            lead_fees = self.repository.create_lead_fees(lead_fees)
        return (
            existed_lead_config,
            lead_fees,
            profile,
        )

    def get_lead_fee_by_broker_id(self, broker_id: int) -> list[BrokerLeadFee]:
        return self.repository.get_lead_fee_by_broker_id(broker_id)

    def get_lead_fee_by_broker_id_and_insurance_type(
        self,
        broker_id: int,
        insurance_type: InsuranceType,
    ) -> BrokerLeadFee | None:
        return self.repository.get_lead_fee_by_broker_id_and_insurance_type(broker_id, insurance_type)

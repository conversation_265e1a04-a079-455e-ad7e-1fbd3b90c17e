from fastapi import UploadFile

from bethune.model.brokerage import Brokerage
from bethune.repository.insurance.brokerage import BrokerageRepository
from bethune.service.base import BaseService
from bethune.settings import settings
from bethune.util.file import upload_file


class BrokerageService(BaseService[Brokerage, BrokerageRepository]):

    def __init__(self, repository: BrokerageRepository):
        super().__init__(repository)

    async def upload_company_logo(self, file: UploadFile, logo: str | None = None) -> tuple[str, str]:
        return upload_file(file, settings.LOGOS_FOLDER, logo)

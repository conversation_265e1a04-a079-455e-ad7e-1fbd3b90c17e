from bethune.model.brokerage import BrokerageTrialApplication
from bethune.repository.insurance.brokerage_trial_application import BrokerageTrialApplicationRepository
from bethune.service.base import BaseService


class BrokerageTrialApplicationService(BaseService[BrokerageTrialApplication, BrokerageTrialApplicationRepository]):

    def __init__(self, repository: BrokerageTrialApplicationRepository):
        super().__init__(repository)

from collections.abc import Iterable

from bethune.model.brokerage_user import BrokerageUser
from bethune.model.brokerage_user import BrokerageUserQueryFilters
from bethune.repository import DEFAULT_LIMIT
from bethune.repository.insurance.brokerage_user import BrokerageUserRepository
from bethune.service.base import BaseService


class BrokerageUserService(BaseService[BrokerageUser, BrokerageUserRepository]):

    def __init__(self, repository: BrokerageUserRepository):
        super().__init__(repository)

    def get_by_user_id(self, user_id: int, allow_none: bool = False) -> BrokerageUser | None:
        """
        Retrieve a BrokerageUser by the user ID.

        :param user_id: The ID of the user.
        :return: A BrokerageUser instance or None if not found.
        """
        return self.repository.get_by_user_id(user_id, allow_none)

    def get_by_query_filters(
        self, filters: BrokerageUserQueryFilters, offset: int = 0, limit: int = DEFAULT_LIMIT
    ) -> tuple[int, list[BrokerageUser]]:
        return self.repository.get_by_query_filters(filters, offset, limit)

    def validate_brokerage_user_ids(self, ids: Iterable[int], brokerage_id: int) -> bool:  # 接受任何可迭代的整数集合
        return self.repository.validate_brokerage_user_ids(ids, brokerage_id)

    def batch_update_enabled_status(self, ids: Iterable[int], enabled: bool, brokerage_id: int) -> int:
        return self.repository.batch_update_enabled_status(ids, enabled, brokerage_id)

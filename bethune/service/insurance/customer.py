from bethune.error.errors import DataValidationError
from bethune.model.customer import Customer
from bethune.model.customer import CustomerQueryFilters
from bethune.model.insurance import InsurancePolicy
from bethune.repository.base import DEFAULT_LIMIT
from bethune.repository.insurance import CustomerRepository
from bethune.repository.insurance import InsuranceApplicationRepository
from bethune.service.base import BaseService
from bethune.util.date import get_current_date
from bethune.util.date import subtract_days


class CustomerService(BaseService[Customer, CustomerRepository]):

    def __init__(
        self,
        repository: CustomerRepository,
        policy_repository: InsuranceApplicationRepository,
    ):
        super().__init__(repository)
        self.insurance_poicy_repository = policy_repository

    def mark_as_deleted(self, id: int):
        return self.repository.mark_as_deleted(id)

    def _merge_customer_and_policy(
        self,
        customers: list[Customer],
        insurance_policies: list[InsurancePolicy],
    ):
        policies_dict = {policy.customer_id: policy for policy in insurance_policies}
        return [(customer, policies_dict.get(customer.id, None)) for customer in customers if customer.id is not None]

    def get_customers(
        self,
        filters: CustomerQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ):
        return self.repository.get_customers(filters, offset, limit)

    def get_customers_with_expiring_end_date(
        self,
        broker_id: int,
        expired_in_days: int,
        broker_is_qualified: bool,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ):
        return self.repository.get_expiring_insurance_customers(
            broker_id, expired_in_days, broker_is_qualified, offset, limit
        )

    def count_new_customers(self, broker_id: int, created_in_days: int = 30):
        return self.repository.count_new_customers(broker_id, subtract_days(get_current_date(), created_in_days))

    def check_existing_customer(self, candidate_customer: Customer) -> Customer:
        """
        检查既有客户实体
        用姓名和Email检查是否存在既有实体
        如果没有就创建
        :param candidate_customer:
        :return:
        """
        if candidate_customer.id:
            return self.repository.get_by_id(candidate_customer.id)  # type: ignore

        if candidate_customer.broker_id is None or candidate_customer.name is None or candidate_customer.email is None:
            raise DataValidationError("Broker ID, Name and EMAIL are required for new customers.")

        existing_customer = self.repository.get_unique_customer(
            candidate_customer.broker_id,
            candidate_customer.name,
            candidate_customer.email,
        )
        return existing_customer if existing_customer else self.repository.create(candidate_customer)

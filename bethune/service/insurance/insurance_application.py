import os
from pathlib import Path

from bethune.db import transaction
from bethune.logging import logger
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationQueryFilters
from bethune.model.insurance_application import InsuranceApplicationReport
from bethune.repository.insurance import InsuranceApplicationRepository
from bethune.repository.insurance.lead import LeadRepository
from bethune.service.base import BaseService
from bethune.settings import settings
from bethune.util.date import get_current_date
from bethune.util.date import subtract_days
from bethune.util.json_handler import JSONHandler


class InsuranceApplicationService(BaseService[InsuranceApplication, InsuranceApplicationRepository]):

    def __init__(
        self,
        repository: InsuranceApplicationRepository,
        lead_repository: LeadRepository,
    ):
        super().__init__(repository)

        self.lead_repository = lead_repository

        json_folder = Path(settings.DATA_DIR, settings.JSON_DATA_FOLDER, "house-insurance")
        json_folder.mkdir(parents=True, exist_ok=True)
        self.json_folder = json_folder

    @transaction
    def mark_as_deleted(self, id: int):
        return self.repository.mark_as_deleted(id)

    def get_by_broker_id(self, id: int, broker_id: int):
        return self.repository.get_by_broker_id(id, broker_id)

    def count_recent_applications(self, broker_id: int, created_in_days: int = 30):
        return self.repository.count_new_applications(broker_id, subtract_days(get_current_date(), created_in_days))

    def save_json(self, data, serial_number: str):
        file_path = os.path.join(str(self.json_folder), f"{serial_number}.json")
        handler = JSONHandler.create(file_path)
        handler.save_json(data)
        logger.info(f"数据已保存到 {file_path}")

    def merge_json(self, id: int, new_data: dict):
        insurance_application = self.get_by_id(id)
        serial_number = insurance_application.serial_number
        existing_data = self.read_json(insurance_application.insurance_type, serial_number)
        merged_data = {**existing_data, **new_data}
        self.save_json(merged_data, serial_number)  # type: ignore
        return merged_data

    def read_json(self, insurance_type, serial_number):
        file_path = os.path.join(str(self.json_folder), f"{serial_number}.json")
        handler = JSONHandler.create(file_path)
        return self.__fix_insurance_type(insurance_type, handler.read_json())

    def __fix_insurance_type(self, insurance_type, data):
        if "insurance_type" not in data:
            data["insurance_type"] = insurance_type.value
        return data

    def get_by_query_filters(
        self,
        filters: InsuranceApplicationQueryFilters,
        offset: int = 0,
        limit: int = 100,
    ):
        return self.repository.get_by_query_filters(filters, offset, limit)

    def is_lead_application(self, application: InsuranceApplication) -> bool:
        return self.lead_repository.get_by_application_id(application.id) is not None  # type: ignore

    def get_by_ref_code(self, ref_code: str) -> InsuranceApplication:
        return self.repository.get_by_ref_code(ref_code)

    def get_all_application_info(self) -> list[InsuranceApplicationReport]:
        return self.repository.get_all_application_info()

from bethune.model.insurance_company import InsuranceCompany
from bethune.repository.insurance.insurance_company import (
    InsuranceCompanyRepository,
)
from bethune.service.base import BaseService


class InsuranceCompanyService(BaseService[InsuranceCompany, InsuranceCompanyRepository]):

    def __init__(self, repository: InsuranceCompanyRepository):
        super().__init__(repository)

    def get_by_code(self, code: str) -> InsuranceCompany:
        return self.repository.get_by_code(code)

    def get_all(self) -> list[InsuranceCompany]:
        return self.repository.get_all()

from bethune.model import InsuranceConsultation
from bethune.repository.insurance.broker import InsuranceConsultationRepository
from bethune.service.base import BaseService


class InsuranceConsultationService(BaseService[InsuranceConsultation, InsuranceConsultationRepository]):

    def __init__(self, repository: InsuranceConsultationRepository):
        super().__init__(repository)

    def mark_as_completed(self, broker_id: int, id: int):
        return self.repository.mark_as_completed(broker_id, id)

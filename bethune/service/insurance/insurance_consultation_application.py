from bethune.model import InsuranceConsultationApplication
from bethune.repository.insurance.broker import InsuranceConsultationApplicationRepository
from bethune.service.base import BaseService


class InsuranceConsultationApplicationService(
    BaseService[InsuranceConsultationApplication, InsuranceConsultationApplicationRepository]
):

    def __init__(self, repository: InsuranceConsultationApplicationRepository):
        super().__init__(repository)

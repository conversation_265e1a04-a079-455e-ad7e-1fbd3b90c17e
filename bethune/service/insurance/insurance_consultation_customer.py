from bethune.model import InsuranceConsultationCustomer
from bethune.repository.insurance.broker import InsuranceConsultationCustomerRepository
from bethune.service.base import BaseService


class InsuranceConsultationCustomerService(
    BaseService[InsuranceConsultationCustomer, InsuranceConsultationCustomerRepository]
):

    def __init__(self, repository: InsuranceConsultationCustomerRepository):
        super().__init__(repository)

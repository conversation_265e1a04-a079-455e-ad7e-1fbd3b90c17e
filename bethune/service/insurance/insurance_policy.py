from datetime import date

from bethune.model.insurance import InsurancePolicy
from bethune.model.insurance import InsurancePolicyQueryFilters
from bethune.repository.base import DEFAULT_LIMIT
from bethune.repository.insurance.insurance_policy import InsurancePolicyRepository
from bethune.service.base import BaseService


class InsurancePolicyService(BaseService[InsurancePolicy, InsurancePolicyRepository]):

    def __init__(self, repository: InsurancePolicyRepository):
        super().__init__(repository)

    def mark_as_deleted(self, id: int) -> InsurancePolicy:
        return self.repository.mark_as_deleted(id)

    def get_by_query_filters(
        self,
        filters: InsurancePolicyQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[InsurancePolicy]]:
        return self.repository.get_by_query_filters(filters, offset, limit)

    def get_policies_by_expiration(
        self,
        broker_id: int,
        end_date: date,
        last_processed_policy_id: int,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> list[InsurancePolicy]:
        return self.repository.get_policies_by_expiration(
            broker_id, end_date, last_processed_policy_id, offset=offset, limit=limit
        )

    def get_by_application_id(self, application_id: int) -> InsurancePolicy | None:
        return self.repository.get_by_application_id(application_id)

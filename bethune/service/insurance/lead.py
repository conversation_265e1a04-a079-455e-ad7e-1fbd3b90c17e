import os
import uuid
from decimal import Decimal
from pathlib import Path

from bethune.error import NotFoundError
from bethune.logging import logger
from bethune.model import Broker
from bethune.model import InsuranceApplication
from bethune.model.lead import Lead
from bethune.model.lead import LeadApplication
from bethune.model.lead import LeadQueryFilters
from bethune.model.lead import LeadReferralFeePayment
from bethune.model.lead import LeadRichInfoComposite
from bethune.model.lead import LeadStatusEnum
from bethune.model.lead import ReferralFeeTypeEnum
from bethune.model.payment import PaymentChannelEnum
from bethune.model.payment import PaymentCurrencyEnum
from bethune.model.payment import PaymentStatusEnum
from bethune.repository import DEFAULT_LIMIT
from bethune.repository.insurance import CustomerRepository
from bethune.repository.insurance.lead import LeadRepository
from bethune.service.base import BaseGenericService
from bethune.settings import settings
from bethune.util.date import get_current_datetime
from bethune.util.date import subtract_days
from bethune.util.json_handler import JSONHandler


class LeadService(BaseGenericService[LeadRepository]):

    def __init__(self, repository: LeadRepository, customer_repository: CustomerRepository | None = None):
        super().__init__(repository)
        self.customer_repository = customer_repository
        json_folder = Path(settings.DATA_DIR, settings.JSON_DATA_FOLDER, "lead")
        json_folder.mkdir(parents=True, exist_ok=True)
        self.json_folder = json_folder

    def get_by_id_with_rich_info(self, id: int) -> LeadRichInfoComposite:
        return self.repository.get_by_id_with_rich_info(id)

    def get_by_query_filters(
        self,
        broker: Broker,
        filters: LeadQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[LeadRichInfoComposite]]:
        return self.repository.get_by_query_filters(broker, filters, offset, limit)

    async def get_leads_by_broker_id(self, broker_id: int, limit: int = DEFAULT_LIMIT):
        offset = 0
        while True:
            results = self.repository.get_by_broker_id(broker_id, offset=offset, limit=limit)

            if not results:
                break

            yield results

            if len(results) < limit:
                break
            offset += limit

    async def update_leads_by_expiration(self, expired_days: int):
        expired_datetime = subtract_days(get_current_datetime(), expired_days)
        return await self.repository.update_leads_by_expiration(expired_datetime)

    def get_all_lead_info(self):
        return self.repository.get_all_lead_info()

    def save_json(self, data, serial_number: str):
        file_path = os.path.join(str(self.json_folder), f"{serial_number}.json")
        handler = JSONHandler.create(file_path)
        handler.save_json(data)
        logger.info(f"数据已保存到 {file_path}")

    def merge_json(self, id: int, new_data: dict):
        lead = self.get_by_id(Lead, id)
        serial_number = lead.serial_number
        existing_data = self.read_json(serial_number)
        if existing_data is None:
            self.save_json(new_data, serial_number)  # type: ignore
            return new_data

        merged_data = {**existing_data, **new_data}
        self.save_json(merged_data, serial_number)  # type: ignore
        return merged_data

    def read_json(self, serial_number):
        file_path = os.path.join(str(self.json_folder), f"{serial_number}.json")
        return JSONHandler.create(file_path).read_json()

    def get_referral_fee_payment(self, lead_id: int) -> LeadReferralFeePayment | None:
        return self.repository.get_referral_fee_payment_by_lead(lead_id)

    def create_referral_fee_payment(self, lead: Lead, application: InsuranceApplication) -> LeadReferralFeePayment:
        lead.status = LeadStatusEnum.PENDING_LEAD_PAYMENT
        self.repository.update(lead)

        if ReferralFeeTypeEnum.PREMIUM_PERCENTAGE == lead.referral_fee_type:
            if application.premium is None:
                raise ValueError("Insurance premium is required for percentage referral fee.")
            referral_fee = int(
                application.premium * Decimal(str(lead.referral_fee_value)) / Decimal("100") * Decimal("100")
            )
        else:
            referral_fee = int(lead.referral_fee_value * 100)

        return self.repository.create(
            LeadReferralFeePayment(
                payee=lead.created_by,
                payer=application.broker_id,
                lead_id=lead.id,
                payment_currency=PaymentCurrencyEnum.CAD,
                payment_channel=PaymentChannelEnum.OFFLINE,
                payment_amount_in_cents=referral_fee,
                payment_status=PaymentStatusEnum.CREATED,
                serial_number=uuid.uuid4().hex,
            )
        )

    def pay_for_referral_fee_payment(
        self, _broker: Broker, lead: Lead, _application: InsuranceApplication
    ) -> LeadReferralFeePayment:
        if lead.id is not None:
            lead.status = LeadStatusEnum.COMPLETED
            self.repository.update(lead)
            referral_fee_payment = self.repository.get_referral_fee_payment_by_lead(lead.id)
            if referral_fee_payment:
                referral_fee_payment.payment_status = PaymentStatusEnum.PAID
                return self.repository.update(referral_fee_payment)
            raise NotFoundError(f"Referral fee payment not found for lead {lead.id}.")
        raise NotFoundError(f"Lead {lead.id} not found.")

    def get_total_lead_referral_fee(self, broker: Broker) -> float:
        return self.repository.get_total_lead_referral_fee(broker)

    def get_total_lead_count(self, broker: Broker) -> int:
        return self.repository.get_total_lead_count(broker)

    def get_referral_fee_payments_by_lead_ids(self, lead_ids: list[int]) -> list[LeadReferralFeePayment]:
        return self.repository.get_referral_fee_payments_by_lead_ids(lead_ids)

    def create_lead_application(self, lead_application: LeadApplication) -> LeadApplication:
        return self.repository.create(lead_application)

    def get_lead_applications_by_application_ids(self, application_ids: list[int]) -> list[LeadApplication]:
        return self.repository.get_by_application_ids(application_ids)

    def get_lead_application_by_application_id(self, application_id: int) -> LeadApplication | None:
        return self.repository.get_by_application_id(application_id)

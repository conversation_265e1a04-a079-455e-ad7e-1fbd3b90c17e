import base64
import uuid
from pathlib import Path

from fastapi import UploadFile

from bethune.logging import logger
from bethune.model.broker import PromotionMaterial
from bethune.repository.insurance.broker import PromotionMaterialRepository
from bethune.service.base import BaseService
from bethune.settings import settings


class PromotionMaterialService(BaseService[PromotionMaterial, PromotionMaterialRepository]):
    def __init__(self, repository: PromotionMaterialRepository):
        super().__init__(repository)

    def get_by_broker_and_id(self, broker_id: int, id: int) -> PromotionMaterial:
        return self.repository.get_by_broker_and_id(broker_id, id)

    def upload_material_file(self, file: UploadFile, infix: str = "promotion_material") -> tuple[str, str]:
        allowed_types = ["image/jpeg", "image/png", "image/gif"]
        if file.content_type not in allowed_types:
            raise ValueError("只支持上传JPEG, PNG和GIF格式的图片")

        max_size = 20 * 1024 * 1024  # 20MB
        if file.size > max_size:
            raise ValueError("文件大小不能超过20MB")

        files_path = Path(settings.DATA_DIR) / settings.UPLOADS_FOLDER / infix
        files_path.mkdir(parents=True, exist_ok=True)
        ext = file.filename.split(".")[-1]
        filename = f"{uuid.uuid4().hex}.{ext}"
        file_path = files_path / filename

        with file_path.open("wb") as buffer:
            buffer.write(file.file.read())

        return filename, f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{infix}/{filename}"

    def save_base64_image(self, base64_str: str, infix: str = "promotion_material"):
        """将Base64字符串解码为图片文件"""

        files_path = Path(settings.DATA_DIR) / settings.UPLOADS_FOLDER / infix
        files_path.mkdir(parents=True, exist_ok=True)

        if ";base64," in base64_str:
            base64_data = base64_str.split(";base64,")[1]
        else:
            base64_data = base64_str

        filename = f"{uuid.uuid4().hex}.png"
        file_path = files_path / filename

        image_data = base64.b64decode(base64_data)
        with open(file_path, "wb") as f:
            f.write(image_data)

        return filename, f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{infix}/{filename}"

    def delete_material_file(self, filename: str, infix: str = "promotion_material") -> None:
        files_path = Path(settings.DATA_DIR) / settings.UPLOADS_FOLDER / infix
        old_files_path = files_path / filename
        try:
            if old_files_path.exists():
                old_files_path.unlink()
        except Exception as e:
            logger.error(f"删除文件失败: {e}")

from bethune.model.reminder import ReminderConfig
from bethune.repository.reminder import ReminderConfigRepository
from bethune.service.base import BaseService


class ReminderConfigService(BaseService[ReminderConfig, ReminderConfigRepository]):

    def __init__(self, repository: ReminderConfigRepository):
        super().__init__(repository)

    def get_by_id_and_broker(self, id: int, broker_id: int) -> ReminderConfig:
        return self.repository.get_by_id_and_broker(id, broker_id)

    def get_by_broker(self, broker_id: int) -> list[ReminderConfig]:
        return self.repository.get_by_broker(broker_id)

    def create_or_update(self, config: ReminderConfig) -> ReminderConfig:
        record = self.repository.get_by_unique_name(config)
        if record is not None:
            update_id = record.id
            record = ReminderConfig(
                **config.model_dump(exclude_unset=True, exclude={"broker_id", "business_type", "reminder_type"})
            )
            record.id = update_id
            return self.repository.update(record)

        config.id = None
        return self.repository.create(config)

    def create_all(self, configs: list[ReminderConfig]) -> list[ReminderConfig]:
        return self.repository.create_all(configs)

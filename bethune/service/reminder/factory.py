from .config import ReminderConfigService
from .message import <PERSON>minderMessageService
from bethune.repository.reminder import ReminderRepositoryFactory


class ServiceFactory:
    @staticmethod
    def create_reminder_config_service() -> ReminderConfigService:
        return ReminderConfigService(ReminderRepositoryFactory.create_reminder_config_repository())

    @staticmethod
    def create_reminder_message_service() -> ReminderMessageService:
        return ReminderMessageService(ReminderRepositoryFactory.create_reminder_message_repository())

import datetime
import uuid
from datetime import datetime as dt
from datetime import timedelta

from ...util import verify_password
from .user import UserService
from bethune.api.dto.auth import Token
from bethune.api.dto.auth import TokenData
from bethune.api.error import UnauthenticatedError
from bethune.db.redis import get_redis
from bethune.logging import logger
from bethune.model import User
from bethune.model import UserStatus
from bethune.settings import settings
from bethune.util.password import hash_password


class AuthService:

    def __init__(self, user_service: UserService):
        self._user_service = user_service
        self._redis = get_redis()

    def authenticate_user(self, username: str, password: str) -> User:
        user = self._user_service.get_by_email(username)
        if user:
            if user.status == UserStatus.INACTIVE:
                raise UnauthenticatedError("user is inactive")
            if verify_password(password, user.password):
                return user
        raise UnauthenticatedError(err_msg="Invalid username or password")

    def _create_access_token(self, user: User) -> str:
        now = dt.now(datetime.UTC)
        data = TokenData(
            sub=user.email,
            iat=now,
            exp=now + timedelta(minutes=settings.AUTH_ACCESS_TOKEN_EXPIRE_MINUTES),
            jti=str(uuid.uuid4()),
        )
        return data.encode()

    def change_password(self, user_email: str, origin_password: str, new_password: str):
        user = self.authenticate_user(user_email, origin_password)
        if user:
            user.password = hash_password(new_password)
            user_updated = self._user_service.update(user)
            return user_updated is not None
        return False

    def create_token_by_user(self, user: User) -> Token:
        return Token(access_token=self._create_access_token(user), token_type="Bearer")

    def create_token(self, username: str, password: str) -> Token:
        return self.create_token_by_user(self.authenticate_user(username, password))

    async def logout(self, token: str) -> None:
        """Add token to blacklist."""
        try:
            token_data = TokenData.decode(token)
            await self._redis.setex(
                f"auth:token_blacklist:{token}",
                time=int(token_data.exp.timestamp()) - int(dt.now(datetime.UTC).timestamp()),
                value="True",
            )
        except UnauthenticatedError:
            logger.error("Invalid token: {}", token)

    async def get_current_user(self, token: str) -> User:
        if await self._is_token_blacklisted(token):
            raise UnauthenticatedError(err_msg="Token has expired")
        token_data = TokenData.decode(token)
        return self._user_service.get_by_email(token_data.sub)

    async def _is_token_blacklisted(self, token: str):
        return await self._redis.get(f"auth:token_blacklist:{token}") is not None

from bethune.model.system import OAuthProvider
from bethune.model.system import OAuthUserInfo
from bethune.repository.system.oauth_user_info import OAuthUserInfoRepository
from bethune.service import BaseService


class OAuthUserInfoService(BaseService[OAuthUserInfo, OAuthUserInfoRepository]):
    def __init__(self, repository: OAuthUserInfoRepository):
        self.repository = repository

    def get_by_open_id_and_provider(self, open_id: str, provider: OAuthProvider) -> OAuthUserInfo | None:
        return self.repository.get_by_open_id_and_provider(open_id, provider)

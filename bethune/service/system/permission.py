from bethune.model.system import Permission
from bethune.repository.system import PermissionRepository
from bethune.service import BaseService


class PermissionService(BaseService[Permission, PermissionRepository]):

    def __init__(self, repository: PermissionRepository):
        super().__init__(repository)

    def get_by_code(self, code: str) -> Permission:
        return self.repository.get_by_code(code)

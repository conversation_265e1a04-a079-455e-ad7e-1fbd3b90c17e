from bethune.cache import cache
from bethune.model.system import Permission
from bethune.model.system import Role
from bethune.repository import DEFAULT_LIMIT
from bethune.repository.system import RoleRepository
from bethune.service import BaseService


class RoleService(BaseService[Role, RoleRepository]):

    def __init__(self, repository: RoleRepository):
        super().__init__(repository)

    def get_permissions(self, role_id: int, offset: int = 0, limit: int = 20) -> tuple[int, list[Permission]]:
        total = self.repository.count_permissions(role_id)
        return total, self.repository.get_permissions(role_id, offset, limit)

    @cache.cache_on_arguments(namespace="role_permission", expiration_time=60 * 60 * 2)
    def get_all_permission_code(self, role_id: int) -> list[str]:
        permissions = self.repository.get_permissions(role_id, 0, DEFAULT_LIMIT)
        return [permission.code for permission in permissions]

    def set_permissions(self, role_id: int, permission_ids: list[int]):
        self.repository.set_permissions(role_id, permission_ids)
        # first argument is just a placeholder, the cache key is generated by the second argument
        self.get_all_permission_code.invalidate(cache, role_id)  # type: ignore

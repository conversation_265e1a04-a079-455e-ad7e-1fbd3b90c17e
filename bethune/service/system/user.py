from fastapi import UploadFile

from bethune.cache import cache
from bethune.error.errors import DataValidationError
from bethune.model.system import Role
from bethune.model.system import User
from bethune.repository import DEFAULT_LIMIT
from bethune.repository.system import UserRepository
from bethune.service import BaseService
from bethune.settings import settings
from bethune.util.file import upload_file


class UserService(BaseService[User, UserRepository]):

    def __init__(self, repository: UserRepository):
        super().__init__(repository)

    def get_by_email(self, email: str) -> User:
        return self.repository.get_by_email(email)

    def get_roles(self, user_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT) -> tuple[int, list[Role]]:
        total = self.repository.count_roles(user_id)
        return total, self.repository.get_roles(user_id, offset, limit)

    @cache.cache_on_arguments(namespace="user_role")
    def get_all_role_id(self, user_id: int) -> list[int]:
        roles = self.repository.get_roles(user_id)
        return [role.id for role in roles if role.id is not None]

    def set_roles(self, user_id: int, role_ids: list[int]):
        self.repository.set_roles(user_id, role_ids)
        # first argument is just a placeholder, the cache key is generated by the second argument
        self.get_all_role_id.invalidate(cache, user_id)  # type: ignore

    async def upload_avatar(self, file: UploadFile, avatar: str | None = None):
        return upload_file(file, settings.AVATARS_FOLDER, avatar)

    @cache.cache_on_arguments(namespace="user-cache", expiration_time=60 * 60 * 12)
    def get_user_cache_by_id(self, id: int):
        return self.repository.get_by_id(id)

    async def check_user_exists(self, email: str):
        if self.repository.get_user_by_email(email) is not None:
            raise DataValidationError(message="Email's user already exists", detail={"email": email})

    async def check_user_exists_no_error(self, email: str):
        return self.repository.get_user_by_email(email) is not None

from typing import Any

from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    PROJECT_NAME: str = "Home Insurance"
    VERSION: str = "0.1.0"
    ROOT_PATH: str = "/admin"
    API_PREFIX: str = "/api"
    API_VERSION: str = "/v1"

    CORS_ORIGINS: list = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list = ["*"]
    CORS_ALLOW_HEADERS: list = ["*"]

    DATETIME_FORMAT: str = "%Y-%m-%d %H:%M:%S"
    DATA_DIR: str = "./data"
    UPLOADS_FOLDER: str = "uploads"
    AVATARS_FOLDER: str = "avatars"
    LOGOS_FOLDER: str = "logos"

    ELECTRONIC_POLICY_FOLDER: str = "e-policy"
    ELECTRONIC_QUOTE_FOLDER: str = "e-quote"

    JSON_DATA_FOLDER: str = "json_data"

    # SMTP配置
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: str = "<EMAIL>"
    SMTP_SENDER: str = "<EMAIL>"
    SMTP_PASSWORD: str = "123456"
    SMTP_USE_TLS: bool = False
    SMTP_START_TLS: bool = True

    # 新增IMAP配置
    IMAP_HOST: str = "imap.gmail.com"
    IMAP_PORT: int = 993
    IMAP_USERNAME: str = "<EMAIL>"
    IMAP_PASSWORD: str = "123456"

    ENABLE_EMAIL: bool = True
    REDIS_URL: str = "redis://localhost:6379/0"

    DATABASE_URL: str = "mysql+pymysql://admin:Password!23@127.0.0.1:3306/bethune?charset=utf8mb4"

    # i18n
    I18N_CONFIG_ROOT_DIR: str = "."
    I18N_LOCALE_DIR: str = "bethune/locales"
    I18N_DEFAULT_LANGUAGE: str = "en"

    # auth
    AUTH_SECRET_KEY: str = "f2bae9b579a9ff0b379d5afec3de6d72a021443a71615ad4255c3a7415995bb3"
    AUTH_ALGORITHM: str = "HS256"
    AUTH_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30
    AUTH_SKIP_VERIFICATION: bool = False

    LOG_LEVEL: str = "DEBUG"
    LOGGING_FILE_PATH: str = "logs/bethune.log"

    CACHE_PREFIX: str = "BETHUNE_CACHE_REDIS_"

    BETHUNE_SITE_URL: str = "http://127.0.0.1:3000"
    BETHUNE_EXPORT_SERVICE_URL: str = "http://127.0.0.1:8000"

    LEAD_EXPIRED_DAYS: int = 1

    REF_CODE_ENV_PREFIX: str = ""

    # Google OAuth
    GOOGLE_CLIENT_TYPE: str = "web"
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URIS: list[str] = []
    GOOGLE_AUTH_URI: str = "https://accounts.google.com/o/oauth2/auth"
    GOOGLE_TOKEN_URI: str = "https://oauth2.googleapis.com/token"

    def google_client_config(self) -> dict[str, Any]:
        return {
            self.GOOGLE_CLIENT_TYPE: {
                "client_id": self.GOOGLE_CLIENT_ID,
                "client_secret": self.GOOGLE_CLIENT_SECRET,
                "redirect_uris": self.GOOGLE_REDIRECT_URIS,
                "auth_uri": self.GOOGLE_AUTH_URI,
                "token_uri": self.GOOGLE_TOKEN_URI,
            }
        }

    # 从 Pydantic 2.0 开始，类形式的 Config 被弃用，改为使用 ConfigDict
    model_config = ConfigDict(
        frozen=True,  # 禁止模型实例字段被修改（配置只读）
        extra="ignore",  # 忽略模型接受未定义字段
        env_file=".env",
    )

import random
import string
from typing import Any

from bethune.model import Broker
from bethune.model.lead import LeadBase
from bethune.model.lead import LeadStatusEnum

_LEAD_STATUS_NEEDS_TO_DESENSITIZE = [
    LeadStatusEnum.PENDING,
    LeadStatusEnum.EXPIRED,
    LeadStatusEnum.WITHDRAWN,
    LeadStatusEnum.REJECTED,
]


def _desensitize_string(dumped: dict[str, Any], key: str, hide_whole_field: bool = False) -> Any:
    value = dumped.get(key)
    if hide_whole_field:
        return "***"
    if value is None or type(value) is not str:
        return value
    value = str(value)
    if len(value) <= 2:
        return value
    return value[0] + "*" * (len(value) - 2) + value[-1]


def generate_captcha_text(length=6):
    characters = string.digits
    return "".join(random.choice(characters) for _ in range(length))


def desensitize_lead_model_dump(lead_model: LeadBase, broker_model: Broker) -> dict[str, Any]:
    dumped = lead_model.model_dump()
    if broker_model.id != lead_model.created_by and lead_model.status in _LEAD_STATUS_NEEDS_TO_DESENSITIZE:
        dumped["customer_address"] = _desensitize_string(dumped, "customer_address")
        dumped["customer_email"] = _desensitize_string(dumped, "customer_email")
        dumped["customer_phone"] = _desensitize_string(dumped, "customer_phone")
    return dumped


def hide_broker_non_public_fields(model_dumped: dict[str, Any], public_fields: set[str]) -> dict[str, Any]:
    if "NAME" not in public_fields:
        model_dumped["name"] = "***"
    if "PHONE" not in public_fields:
        model_dumped["phone"] = "***"
    if "EMAIL" not in public_fields:
        model_dumped["email"] = "***"
    return model_dumped

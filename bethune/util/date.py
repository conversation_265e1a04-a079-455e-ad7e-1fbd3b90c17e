import json
from datetime import date
from datetime import datetime
from datetime import timedelta
from datetime import timezone

_tzinfo = timezone.utc


class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


def get_current_date():
    """获取当前日期，格式为YYYY-MM-DD"""
    return datetime.now(_tzinfo).date()


def get_current_datetime():
    """获取当前日期和时间，格式为YYYY-MM-DD HH:MM:SS"""
    return datetime.now(_tzinfo)


def get_current_time():
    """获取当前时间，格式为HH:MM:SS"""
    return datetime.now(_tzinfo).time()


def format_date(dt: date, fmt: str = "%Y-%m-%d") -> str:
    """格式化日期

    Args:
        dt (date): 要格式化的日期对象
        fmt (str): 格式化字符串，默认是YYYY-MM-DD

    Returns:
        str: 格式化后的日期字符串
    """
    return dt.strftime(fmt)


def parse_date(date_str: str, fmt: str = "%Y-%m-%d") -> date:
    """将日期字符串解析为日期对象

    Args:
        date_str (str): 日期字符串
        fmt (str): 格式化字符串，默认是YYYY-MM-DD

    Returns:
        date: 解析后的日期对象
    """
    return datetime.strptime(date_str, fmt).replace(tzinfo=_tzinfo).date()


def get_days_between(start_date: date, end_date: date) -> int:
    """计算两个日期之间的天数

    Args:
        start_date (date): 起始日期
        end_date (date): 结束日期

    Returns:
        int: 天数差
    """
    return (end_date - start_date).days


def add_days(dt: date | datetime, days: int):
    """给定日期加上指定的天数

    Args:
        dt (date, datetime): 初始日期
        days (int): 要增加的天数

    Returns:
        date, datetime: 新日期
    """
    return dt + timedelta(days=days)


def subtract_days(dt: date | datetime, days: int):
    """给定日期减去指定的天数

    Args:
        dt (date, datetime): 初始日期
        days (int): 要减少的天数

    Returns:
        date, datetime: 新日期
    """
    return dt - timedelta(days=days)


def get_date_range(start_date: date, end_date: date):
    """生成两个日期之间的日期范围

    Args:
        start_date (date): 起始日期
        end_date (date): 结束日期

    Returns:
        list[date]: 日期列表
    """
    delta = end_date - start_date
    return [start_date + timedelta(days=i) for i in range(delta.days + 1)]


def is_weekend(dt: date) -> bool:
    """判断某日期是否是周末

    Args:
        dt (date): 要判断的日期

    Returns:
        bool: 是否是周末
    """
    return dt.weekday() >= 5  # 5是周六，6是周日


def get_start_of_month(dt: date) -> date:
    """获取某日期所在月的第一天

    Args:
        dt (date): 任意日期

    Returns:
        date: 所在月的第一天
    """
    return dt.replace(day=1)


def get_end_of_month(dt: date) -> date:
    """获取某日期所在月的最后一天

    Args:
        dt (date): 任意日期

    Returns:
        date: 所在月的最后一天
    """
    next_month = dt.replace(day=28) + timedelta(days=4)  # 确保跳到下个月
    return next_month.replace(day=1) - timedelta(days=1)

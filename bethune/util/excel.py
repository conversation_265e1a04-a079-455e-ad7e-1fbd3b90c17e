from collections.abc import Generator
from io import BytesIO

from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet


def iter_excel_rows(
    file: str | BytesIO, sheet_name: str | None = None, skip_header: bool = False
) -> Generator[tuple, None, None]:
    """
    Generator function to iterate over rows in an Excel file.

    :param file_path: Path to the Excel file.
    :param sheet_name: Name of the sheet to iterate over. If None, the first sheet is used.
    :return: A generator that yields rows from the specified sheet.
    """
    workbook = load_workbook(file, data_only=True)
    try:
        # Check if the file is empty
        if not workbook.sheetnames:
            raise ValueError("The Excel file is empty or does not contain any sheets.")
        sheet: Worksheet = workbook.active
        if sheet_name:
            # Check if the specified sheet exists
            if sheet_name not in workbook.sheetnames:
                raise ValueError(f"Sheet '{sheet_name}' does not exist in the Excel file.")
            sheet = workbook[sheet_name]
        rows = sheet.iter_rows(values_only=True)
        if skip_header:
            # Skip the header row
            next(rows, None)
        yield from rows
    finally:
        workbook.close()

import uuid
from pathlib import Path

from fastapi import UploadFile

from bethune.logging import logger
from bethune.settings import settings


def upload_file(file: UploadFile, infix: str, old_filename: str | None = None) -> tuple[str, str]:
    allowed_types = ["image/jpeg", "image/png", "image/gif"]
    if file.content_type not in allowed_types:
        raise ValueError("只支持上传JPEG, PNG和GIF格式的图片")

    max_size = 10 * 1024 * 1024  # 10MB
    if file.size > max_size:
        raise ValueError("文件大小不能超过10MB")

    files_path = Path(settings.DATA_DIR) / settings.UPLOADS_FOLDER / infix

    if old_filename:
        old_file_path = files_path / old_filename
        try:
            if old_file_path.exists():
                old_file_path.unlink()
        except Exception as e:
            logger.error(f"删除旧头像文件失败: {e}")

    files_path.mkdir(parents=True, exist_ok=True)
    if "." in file.filename:
        ext = file.filename.split(".")[-1]
    else:
        ext = file.content_type.split("/")[-1]
    filename = f"{uuid.uuid4().hex}.{ext}"
    file_path = files_path / filename

    with file_path.open("wb") as buffer:
        buffer.write(file.file.read())

    return filename, f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{infix}/{filename}"

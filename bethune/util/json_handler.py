import json
import os

from bethune.logging import logger

# import threading


class JSONHandler:
    def __init__(self, file_path):
        self.file_path = file_path

    def save_json(self, data):
        with open(self.file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    def read_json(self, default=None):
        if not os.path.exists(self.file_path):
            return default if default is not None else {}

        with open(self.file_path, encoding="utf-8") as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                logger.exception(f"Failed to read json file: {self.file_path}")
                return default if default is not None else {}

    @staticmethod
    def create(file_path):
        return JSO<PERSON><PERSON><PERSON><PERSON>(file_path)

# 在应用入口文件（如 main.py）的最顶部添加
import bcrypt
from passlib.context import CryptContext

# 伪造一个 __about__ 对象，包含必要的属性（如 __version__）
bcrypt.__about__ = type("FakeAbout", (), {"__version__": bcrypt.__version__})()  # 使用真实版本号

pass_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def hash_password(password: str) -> str:
    return pass_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pass_context.verify(plain_password, hashed_password)


def is_empty_password(hashed_password: str) -> bool:
    """
    Check if the hashed password is empty or not.
    :param hashed_password: The hashed password to check.
    :return: True if the hashed password is empty, False otherwise.
    """
    return pass_context.verify("", hashed_password)


if __name__ == "__main__":
    password = "Password!23"
    print("Password!23: ", hash_password(password))
    password = ""
    print("empty: ", hash_password(password))

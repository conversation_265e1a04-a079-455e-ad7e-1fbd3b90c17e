from pathlib import PurePosixPath


def concat_path(*args) -> str:
    """
    Concatenate path segments into a single path string.
    :param args: Path segments to concatenate.
    :return: Concatenated path string.
    """
    base_path = PurePosixPath("/")
    for arg in args:
        if not isinstance(arg, str):
            raise TypeError(f"Expected str, got {type(arg)}")
        base_path = base_path / arg.strip("/")
    return str(base_path)

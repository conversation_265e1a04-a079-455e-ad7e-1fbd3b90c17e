-- MySQL dump 10.13  Distrib 9.2.0, for macos14.7 (x86_64)
--
-- Host: 127.0.0.1    Database: ca_insurance_dev
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `broker`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `broker` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '姓名',
  `address` varchar(500) NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `business_kind` varchar(255) NOT NULL DEFAULT '房屋保险' COMMENT '业务类型',
  `insurance_company` varchar(255) DEFAULT NULL COMMENT '保险公司',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 COMMENT='保险代理人信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `broker`
--

LOCK TABLES `broker` WRITE;
/*!40000 ALTER TABLE `broker` DISABLE KEYS */;
INSERT INTO `broker` VALUES (1,2,'Test Broker','123 Main St, San Francisco, CA 94122','************','Home Insurance','test_ic','2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,3,'Test Broker','123 Main St, San Francisco, CA 94122','************','Home Insurance','test_ic','2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,4,'Test1 Broker','123 Main St, San Francisco, CA 94122','************','Home Insurance','test_ic','2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,5,'Test2 Broker','123 Main St, San Francisco, CA 94122','************','Home Insurance','test_ic','2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,6,'tianxin.alan','',NULL,'Home Insurance','','2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,7,'boriswys','',NULL,'Home Insurance','','2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,8,'kenny','123 Main St, San Francisco, CA 94122','************','Home Insurance','','2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `broker` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customer`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL COMMENT '代理人ID',
  `name` varchar(255) NOT NULL COMMENT '名字',
  `address` varchar(500) NOT NULL COMMENT '全地址，包括州、城市、街道地址(包括门牌号、街道名称、单元号等)和邮编',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `move_in_date` date DEFAULT NULL COMMENT '入住日期',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `province` varchar(100) NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(100) NOT NULL DEFAULT '' COMMENT '市',
  `postal_code` varchar(20) NOT NULL DEFAULT '' COMMENT '邮编',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer`
--

LOCK TABLES `customer` WRITE;
/*!40000 ALTER TABLE `customer` DISABLE KEYS */;
INSERT INTO `customer` VALUES (1,1,'John Doe','123 Main St, San Francisco, CA 94122','<EMAIL>','************','2020-01-01','1980-01-01',0,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL,'','',''),(2,1,'Jane Doe','456 Main St, San Francisco, CA 94122','<EMAIL>','416-555-1234','2021-01-01','1985-03-04',0,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL,'','','');
/*!40000 ALTER TABLE `customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `house_insurance_application`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `house_insurance_application` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint DEFAULT NULL COMMENT '关联的代理人ID',
  `customer_id` bigint NOT NULL COMMENT '关联的客户ID',
  `address` varchar(500) NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
  `customer_name` varchar(255) NOT NULL DEFAULT '' COMMENT '客户姓名',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `house_area` decimal(10,2) DEFAULT NULL COMMENT '房屋面积（平方米，不含地下室）',
  `year_built` year DEFAULT NULL COMMENT '房屋建造年份',
  `house_type` varchar(50) DEFAULT NULL COMMENT '房屋类型',
  `is_mortgage` tinyint(1) DEFAULT NULL COMMENT '是否存在抵押',
  `is_first_apply` tinyint(1) DEFAULT '0' COMMENT '是否首次投保',
  `has_rejection_record` tinyint(1) DEFAULT '0' COMMENT '是否有拒保记录',
  `expected_start_date` date DEFAULT NULL COMMENT '预计生效日期',
  `start_date` date DEFAULT NULL COMMENT '生效日期',
  `end_date` date DEFAULT NULL COMMENT '失效日期',
  `serial_number` varchar(100) NOT NULL COMMENT '唯一编码，可以根据这个编码在文件系统查询详细数据和附件',
  `status` enum('PENDING','UNDERWRITTEN') DEFAULT 'PENDING' COMMENT '状态：待承保和已承保',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `province` varchar(100) NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(100) NOT NULL DEFAULT '' COMMENT '市',
  `postal_code` varchar(20) NOT NULL DEFAULT '' COMMENT '邮编',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 COMMENT='家庭保险申请表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `house_insurance_application`
--

LOCK TABLES `house_insurance_application` WRITE;
/*!40000 ALTER TABLE `house_insurance_application` DISABLE KEYS */;
INSERT INTO `house_insurance_application` VALUES (1,1,1,'123 Main St, San Francisco, CA 94122','John Doe','<EMAIL>','************','1980-01-01',100.00,2000,'Single Family',0,1,0,'2025-01-01','2025-01-01','2026-01-01','serial_number_1','UNDERWRITTEN','2025-04-10 06:57:24','2025-04-10 06:57:24',0,NULL,'','',''),(2,1,2,'456 Main St, San Francisco, CA 94122','Jane Doe','<EMAIL>','416-555-1234','1985-03-04',200.00,2010,'Condo',1,1,0,'2025-09-01','2025-09-01','2026-09-01','serial_number_2','UNDERWRITTEN','2025-04-10 06:57:24','2025-04-10 06:57:24',0,NULL,'','',''),(3,1,2,'789 Main St, San Francisco, CA 94122','Jane Doe','<EMAIL>','416-555-1234','1985-03-04',200.00,2010,'Condo',1,1,0,'2025-09-01',NULL,NULL,'serial_number_3','PENDING','2025-04-10 06:57:24','2025-04-10 06:57:24',0,NULL,'','','');
/*!40000 ALTER TABLE `house_insurance_application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `insurance_company`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `insurance_company` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '保险公司名称',
  `code` varchar(20) DEFAULT NULL COMMENT '保司编码',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `insurance_company_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 COMMENT='保险公司信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `insurance_company`
--

LOCK TABLES `insurance_company` WRITE;
/*!40000 ALTER TABLE `insurance_company` DISABLE KEYS */;
INSERT INTO `insurance_company` VALUES (1,'保险公司1','BCS-001','2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `insurance_company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permission_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=22;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission`
--

LOCK TABLES `permission` WRITE;
/*!40000 ALTER TABLE `permission` DISABLE KEYS */;
INSERT INTO `permission` VALUES (1,'system:user:create','系统管理-用户-新建','创建用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,'system:user:update','系统管理-用户-编辑','编辑用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,'system:user:delete','系统管理-用户-删除','删除用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,'system:user:view','系统管理-用户-查看','查看用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,'system:role:create','系统管理-角色-新建','创建角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,'system:role:update','系统管理-角色-编辑','编辑角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,'system:role:delete','系统管理-角色-删除','删除角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(8,'system:role:view','系统管理-角色-查看','查看角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(9,'system:permission:view','系统管理-权限-查看','查看权限','2025-04-10 06:57:24','2025-04-10 06:57:24'),(10,'system:user:role:assign','系统管理-用户-角色分配','分配角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(11,'system:user:role:view','系统管理-用户-查看角色','查看角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(12,'system:role:permission:assign','系统管理-角色-权限分配','分配权限','2025-04-10 06:57:24','2025-04-10 06:57:24'),(13,'system:role:permission:view','系统管理-角色-查看撤销','查看权限','2025-04-10 06:57:24','2025-04-10 06:57:24'),(14,'insurance:customer:create','保险-客户-新建','创建客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(15,'insurance:customer:update','保险-客户-编辑','编辑客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(16,'insurance:customer:delete','保险-客户-删除','删除客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(17,'insurance:customer:view','保险-客户-查看','查看客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(18,'insurance:house-application:create','保险-房屋险-新建','创建房屋险','2025-04-10 06:57:24','2025-04-10 06:57:24'),(19,'insurance:house-application:update','保险-房屋险-编辑','编辑房屋险','2025-04-10 06:57:24','2025-04-10 06:57:24'),(20,'insurance:house-application:delete','保险-房屋险-删除','删除房屋险','2025-04-10 06:57:24','2025-04-10 06:57:24'),(21,'insurance:house-application:view','保险-房屋险-查看','查看房屋险','2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role`
--

LOCK TABLES `role` WRITE;
/*!40000 ALTER TABLE `role` DISABLE KEYS */;
INSERT INTO `role` VALUES (1,'administrator','系统管理员','2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,'broker','保险代理人','2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permission`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL,
  `permission_id` bigint NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_id_permission_id` (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permission_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`),
  CONSTRAINT `role_permission_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permission`
--

LOCK TABLES `role_permission` WRITE;
/*!40000 ALTER TABLE `role_permission` DISABLE KEYS */;
INSERT INTO `role_permission` VALUES (1,1,1,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,1,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,1,3,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,1,4,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,1,5,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,1,6,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,1,7,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(8,1,8,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(9,1,9,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(10,1,10,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(11,1,11,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(12,1,12,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(13,1,13,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(14,2,14,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(15,2,15,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(16,2,16,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(17,2,17,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(18,2,18,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(19,2,19,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(20,2,20,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(21,2,21,'2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `role_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `email` varchar(255) NOT NULL COMMENT '邮箱',
  `mobile` varchar(255) DEFAULT NULL COMMENT '手机号',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `user_type` enum('SAAS','SYSTEM') NOT NULL DEFAULT 'SAAS' COMMENT '用户类型',
  `status` enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `language` enum('zh','en','fr') NOT NULL DEFAULT 'zh' COMMENT '语言',
  `referer_id` bigint DEFAULT NULL COMMENT '推荐人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=9 COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (1,'<EMAIL>',NULL,'$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W','Admin','SYSTEM','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,'<EMAIL>',NULL,'$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W','Test Broker','SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,'<EMAIL>',NULL,'$2b$12$QjEcW6CD3UwJVBmEbxPCHeC/dPZn2Q9kQUSH/apOUmJVAs7gLZh8.','Test User','SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,'<EMAIL>',NULL,'$2b$12$KKqJsmkbjjvJTypw457fpOkpQYESoKOl0kyk1WRHlVgjqceEDA4Mm','Test1 User','SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,'<EMAIL>',NULL,'$2b$12$eiqeBt1x.yQ.1v1yW6dJkuYh0UwOf7bJIFN6Y/tp7I9oRGk0rYqwa','Test2 User','SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,'<EMAIL>',NULL,'$2b$12$QjEcW6CD3UwJVBmEbxPCHeC/dPZn2Q9kQUSH/apOUmJVAs7gLZh8.',NULL,'SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,'<EMAIL>',NULL,'$2b$12$KKqJsmkbjjvJTypw457fpOkpQYESoKOl0kyk1WRHlVgjqceEDA4Mm',NULL,'SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(8,'<EMAIL>',NULL,'$2b$12$QjEcW6CD3UwJVBmEbxPCHeC/dPZn2Q9kQUSH/apOUmJVAs7gLZh8.',NULL,'SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_role`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id_role_id` (`user_id`,`role_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_role_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `user_role_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_role`
--

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;
INSERT INTO `user_role` VALUES (1,1,1,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,2,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,3,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,4,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,5,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,6,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,7,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(8,8,2,'2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-04-10 15:46:30

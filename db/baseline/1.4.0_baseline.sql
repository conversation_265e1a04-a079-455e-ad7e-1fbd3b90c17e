-- MySQL dump 10.13  Distrib 8.4.5, for Win64 (x86_64)
--
-- Host: localhost    Database: ca_insurance_dev
-- ------------------------------------------------------
-- Server version	8.4.5

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `broker`
--

DROP TABLE IF EXISTS `broker`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `broker` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'unique identifier',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '姓名',
  `gender` enum('MALE','FEMALE','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '性别',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话',
  `insurance_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保险公司',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '市',
  `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '邮编',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT 'description of the broker',
  `insurance_type` enum('HOUSE_INSURANCE','RENTERS_INSURANCE','AUTO_INSURANCE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
  `brokerage_id` bigint DEFAULT NULL COMMENT '经纪行ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_broker_uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='保险代理人信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `broker`
--

LOCK TABLES `broker` WRITE;
/*!40000 ALTER TABLE `broker` DISABLE KEYS */;
INSERT INTO `broker` VALUES (1,'BR-00000001',2,'Test Broker',NULL,'123 Main St, San Francisco, CA 94122','415-555-1234','test_ic','2025-04-10 06:57:24','2025-07-03 08:00:21','','','',NULL,'HOUSE_INSURANCE',NULL),(2,'BR-00000002',3,'Test Broker',NULL,'123 Main St, San Francisco, CA 94122','415-555-1234','test_ic','2025-04-10 06:57:24','2025-07-03 08:00:21','','','',NULL,'HOUSE_INSURANCE',NULL),(3,'BR-00000003',4,'Test1 Broker',NULL,'123 Main St, San Francisco, CA 94122','415-555-1234','test_ic','2025-04-10 06:57:24','2025-07-03 08:00:21','','','',NULL,'HOUSE_INSURANCE',NULL),(4,'BR-00000004',5,'Test2 Broker',NULL,'123 Main St, San Francisco, CA 94122','415-555-1234','test_ic','2025-04-10 06:57:24','2025-07-03 08:00:21','','','',NULL,'HOUSE_INSURANCE',NULL),(5,'BR-00000005',6,'tianxin.alan',NULL,'',NULL,'','2025-04-10 06:57:24','2025-07-03 08:00:21','','','',NULL,'HOUSE_INSURANCE',NULL),(6,'BR-00000006',7,'boriswys',NULL,'',NULL,'','2025-04-10 06:57:24','2025-07-03 08:00:21','','','',NULL,'HOUSE_INSURANCE',NULL),(7,'BR-00000007',8,'kenny',NULL,'123 Main St, San Francisco, CA 94122','415-555-1234','','2025-04-10 06:57:24','2025-07-03 08:00:21','','','',NULL,'HOUSE_INSURANCE',NULL);
/*!40000 ALTER TABLE `broker` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `broker_lead_config`
--

DROP TABLE IF EXISTS `broker_lead_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `broker_lead_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL COMMENT '关联代理人ID',
  `allow_leads` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接受线索',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `willing_pay_for_leads` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否愿意为最终能成交线索进行事后付费',
  `insurance_type` set('HOUSE_INSURANCE','RENTERS_INSURANCE','AUTO_INSURANCE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_broker_id` (`broker_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代理人线索与佣金配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `broker_lead_config`
--

LOCK TABLES `broker_lead_config` WRITE;
/*!40000 ALTER TABLE `broker_lead_config` DISABLE KEYS */;
INSERT INTO `broker_lead_config` VALUES (1,1,1,'2025-06-03 09:56:28','2025-06-03 09:56:28',1,'HOUSE_INSURANCE'),(2,2,0,'2025-06-03 09:56:28','2025-06-03 09:56:28',1,'HOUSE_INSURANCE');
/*!40000 ALTER TABLE `broker_lead_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `broker_lead_fee`
--

DROP TABLE IF EXISTS `broker_lead_fee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `broker_lead_fee` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL COMMENT '代理人ID',
  `insurance_type` enum('HOUSE_INSURANCE','RENTERS_INSURANCE','AUTO_INSURANCE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
  `referral_fee_type` enum('PREMIUM_PERCENTAGE','FIXED') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'PREMIUM_PERCENTAGE' COMMENT '佣金类型',
  `referral_fee_value` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金值（固定金额或百分比，其含义依据佣金类型而定）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_broker_insurance_type` (`broker_id`,`insurance_type`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代理人线索付费配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `broker_lead_fee`
--

LOCK TABLES `broker_lead_fee` WRITE;
/*!40000 ALTER TABLE `broker_lead_fee` DISABLE KEYS */;
INSERT INTO `broker_lead_fee` VALUES (1,1,'HOUSE_INSURANCE','PREMIUM_PERCENTAGE',5.0000,'2025-06-18 08:32:18','2025-06-18 08:32:18'),(2,2,'HOUSE_INSURANCE','PREMIUM_PERCENTAGE',0.0000,'2025-06-18 08:32:18','2025-06-18 08:32:18');
/*!40000 ALTER TABLE `broker_lead_fee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `broker_payment_method`
--

DROP TABLE IF EXISTS `broker_payment_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `broker_payment_method` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL,
  `account_type` enum('E_TRANSFER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'E_TRANSFER' COMMENT '账户类型',
  `account_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '账户编码（加密存储）',
  `is_default` tinyint(1) DEFAULT '1' COMMENT '是否是默认支付账户',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_account_for_each_broker` (`broker_id`,`account_type`,`account_number`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代理人支付方式表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `broker_payment_method`
--

LOCK TABLES `broker_payment_method` WRITE;
/*!40000 ALTER TABLE `broker_payment_method` DISABLE KEYS */;
INSERT INTO `broker_payment_method` VALUES (1,1,'E_TRANSFER','**********',1,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(2,2,'E_TRANSFER','**********',1,'2025-06-03 09:56:28','2025-06-03 09:56:28');
/*!40000 ALTER TABLE `broker_payment_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `broker_profile`
--

DROP TABLE IF EXISTS `broker_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `broker_profile` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL COMMENT '关联代理人ID',
  `public_fields` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'NAME|EMAIL' COMMENT '公开的个人信息, 用｜分割，包括姓名、站内信、手机、邮箱等',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_broker_id` (`broker_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代理人档案表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `broker_profile`
--

LOCK TABLES `broker_profile` WRITE;
/*!40000 ALTER TABLE `broker_profile` DISABLE KEYS */;
INSERT INTO `broker_profile` VALUES (1,1,'NAME|EMAIL','2025-06-03 09:56:28','2025-06-03 09:56:28'),(2,2,'','2025-06-03 09:56:28','2025-06-03 09:56:28');
/*!40000 ALTER TABLE `broker_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `broker_qualification`
--

DROP TABLE IF EXISTS `broker_qualification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `broker_qualification` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_profile_id` bigint NOT NULL COMMENT '代理人档案ID（不是代理人，目前一对一，今后也许一对多，即一个代理人拥有多种资质，而不仅仅是‘有无资质’）',
  `is_qualified` tinyint(1) DEFAULT '0' COMMENT '是否认证',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='代理人资质认证表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `broker_qualification`
--

LOCK TABLES `broker_qualification` WRITE;
/*!40000 ALTER TABLE `broker_qualification` DISABLE KEYS */;
INSERT INTO `broker_qualification` VALUES (1,1,1,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(2,2,0,'2025-06-03 09:56:28','2025-06-03 09:56:28');
/*!40000 ALTER TABLE `broker_qualification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `brokerage`
--

DROP TABLE IF EXISTS `brokerage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brokerage` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '经纪行ID',
  `name` varchar(255) DEFAULT NULL COMMENT '经纪行名称',
  `province` varchar(100) NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(100) NOT NULL DEFAULT '' COMMENT '市',
  `address` varchar(500) NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
  `postal_code` varchar(20) NOT NULL DEFAULT '' COMMENT '邮编',
  `contact_name` varchar(255) NOT NULL DEFAULT '' COMMENT '联系人姓名',
  `contact_email` varchar(255) DEFAULT NULL COMMENT '联系人邮箱',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `logo` varchar(50) DEFAULT NULL COMMENT '经纪公司logo',
  `website` varchar(200) DEFAULT NULL COMMENT '经纪公司网站',
  `description` text COMMENT 'description of the brokerage',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='经纪行表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brokerage`
--

LOCK TABLES `brokerage` WRITE;
/*!40000 ALTER TABLE `brokerage` DISABLE KEYS */;
/*!40000 ALTER TABLE `brokerage` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `brokerage_trial_application`
--

DROP TABLE IF EXISTS `brokerage_trial_application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brokerage_trial_application` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `contact_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人姓名',
  `contact_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人邮箱',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人电话',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '经纪行名称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='经纪行试用申请表（包含一小部分经纪行和系统管理员信息）';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brokerage_trial_application`
--

LOCK TABLES `brokerage_trial_application` WRITE;
/*!40000 ALTER TABLE `brokerage_trial_application` DISABLE KEYS */;
/*!40000 ALTER TABLE `brokerage_trial_application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `brokerage_user`
--

DROP TABLE IF EXISTS `brokerage_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brokerage_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '经纪行用户ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `brokerage_id` bigint NOT NULL COMMENT '经纪行ID',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '电话',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='经纪行用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brokerage_user`
--

LOCK TABLES `brokerage_user` WRITE;
/*!40000 ALTER TABLE `brokerage_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `brokerage_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customer`
--

DROP TABLE IF EXISTS `customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL COMMENT '代理人ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名字',
  `gender` enum('MALE','FEMALE','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '性别',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '地址',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话',
  `move_in_date` date DEFAULT NULL COMMENT '入住日期',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '市',
  `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '邮编',
  `tags` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '标签, 用｜分割',
  `wechat` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '微信账号',
  `memo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '备注',
  `referer_id` bigint DEFAULT NULL COMMENT '推荐人ID（broker.id）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_broker_customer_name_and_email` (`broker_id`,`name`,`email`) COMMENT '代理、客户姓名和邮箱的唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='客户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer`
--

LOCK TABLES `customer` WRITE;
/*!40000 ALTER TABLE `customer` DISABLE KEYS */;
INSERT INTO `customer` VALUES (1,1,'John Doe',NULL,'123 Main St, San Francisco, CA 94122','<EMAIL>','415-555-1234','2020-01-01','1980-01-01',0,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL,'','','','','','',NULL),(2,1,'Jane Doe',NULL,'456 Main St, San Francisco, CA 94122','<EMAIL>','416-555-1234','2021-01-01','1985-03-04',0,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL,'','','','','','',NULL);
/*!40000 ALTER TABLE `customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `insurance_application`
--

DROP TABLE IF EXISTS `insurance_application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `insurance_application` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ref_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据编号',
  `broker_id` bigint DEFAULT NULL COMMENT '关联的代理人ID',
  `brokerage_id` bigint DEFAULT NULL COMMENT '经纪行ID',
  `customer_id` bigint NOT NULL COMMENT '关联的客户ID',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
  `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '客户姓名',
  `customer_gender` enum('MALE','FEMALE','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户性别',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `is_first_apply` tinyint(1) DEFAULT '0' COMMENT '是否首次投保',
  `has_rejection_record` tinyint(1) DEFAULT '0' COMMENT '是否有拒保记录',
  `expected_start_date` date DEFAULT NULL COMMENT '预计生效日期',
  `start_date` date DEFAULT NULL COMMENT '生效日期',
  `end_date` date DEFAULT NULL COMMENT '失效日期',
  `serial_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一编码，可以根据这个编码在文件系统查询详细数据和附件',
  `status` enum('PENDING_QUOTE','QUOTING','PENDING_UNDERWRITTEN','UNDERWRITTEN') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '状态:待算费、算费中、待承保、已承保',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作人姓名',
  `request_quote_at` timestamp NULL DEFAULT NULL COMMENT '提交时间',
  `quote_at` timestamp NULL DEFAULT NULL COMMENT '报价时间',
  `underwrite_at` timestamp NULL DEFAULT NULL COMMENT '承保时间',
  `memo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '市',
  `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '邮编',
  `premium` decimal(10,2) DEFAULT NULL COMMENT '保费',
  `insurance_type` enum('HOUSE_INSURANCE','RENTERS_INSURANCE','AUTO_INSURANCE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
  `policy_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保单号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ref_code_unique` (`ref_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='保险申请表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `insurance_application`
--

LOCK TABLES `insurance_application` WRITE;
/*!40000 ALTER TABLE `insurance_application` DISABLE KEYS */;
INSERT INTO `insurance_application` VALUES (1,'1',1,NULL,1,'123 Main St, San Francisco, CA 94122','John Doe',NULL,'<EMAIL>','415-555-1234','1980-01-01',1,0,'2025-01-01','2025-01-01','2026-01-01','serial_number_1','UNDERWRITTEN',NULL,NULL,NULL,NULL,NULL,NULL,'2025-04-10 06:57:24','2025-07-03 08:00:20',0,NULL,'','','',NULL,'HOUSE_INSURANCE',NULL),(2,'2',1,NULL,2,'456 Main St, San Francisco, CA 94122','Jane Doe',NULL,'<EMAIL>','416-555-1234','1985-03-04',1,0,'2025-09-01','2025-09-01','2026-09-01','serial_number_2','UNDERWRITTEN',NULL,NULL,NULL,NULL,NULL,NULL,'2025-04-10 06:57:24','2025-07-03 08:00:20',0,NULL,'','','',NULL,'HOUSE_INSURANCE',NULL),(3,'3',1,NULL,2,'789 Main St, San Francisco, CA 94122','Jane Doe',NULL,'<EMAIL>','416-555-1234','1985-03-04',1,0,'2025-09-01',NULL,NULL,'serial_number_3','PENDING_UNDERWRITTEN',NULL,NULL,NULL,NULL,NULL,NULL,'2025-04-10 06:57:24','2025-07-03 08:00:21',0,NULL,'','','',NULL,'HOUSE_INSURANCE',NULL);
/*!40000 ALTER TABLE `insurance_application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `insurance_company`
--

DROP TABLE IF EXISTS `insurance_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `insurance_company` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '保险公司名称',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保司编码',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `insurance_company_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='保险公司信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `insurance_company`
--

LOCK TABLES `insurance_company` WRITE;
/*!40000 ALTER TABLE `insurance_company` DISABLE KEYS */;
INSERT INTO `insurance_company` VALUES (1,'保险公司1','BCS-001','2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `insurance_company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `insurance_consultation`
--

DROP TABLE IF EXISTS `insurance_consultation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `insurance_consultation` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint unsigned NOT NULL COMMENT '经纪人ID',
  `source` varchar(100) DEFAULT NULL COMMENT '咨询来源',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `name` varchar(100) NOT NULL COMMENT '客户姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `phone` varchar(20) NOT NULL COMMENT '电话号码',
  `postal_code` varchar(20) NOT NULL DEFAULT '' COMMENT '邮编',
  `address` varchar(500) NOT NULL COMMENT '客户地址(包括门牌号、街道名称、单元号等)',
  `remark` text COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` enum('PENDING','COMPLETED') NOT NULL DEFAULT 'PENDING' COMMENT '咨询状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='保险咨询表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `insurance_consultation`
--

LOCK TABLES `insurance_consultation` WRITE;
/*!40000 ALTER TABLE `insurance_consultation` DISABLE KEYS */;
/*!40000 ALTER TABLE `insurance_consultation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `insurance_consultation_application`
--

DROP TABLE IF EXISTS `insurance_consultation_application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `insurance_consultation_application` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `insurance_consultation` bigint NOT NULL COMMENT '咨询ID',
  `application_id` bigint NOT NULL COMMENT '保险申请ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='线索-保险申请关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `insurance_consultation_application`
--

LOCK TABLES `insurance_consultation_application` WRITE;
/*!40000 ALTER TABLE `insurance_consultation_application` DISABLE KEYS */;
/*!40000 ALTER TABLE `insurance_consultation_application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `insurance_consultation_customer`
--

DROP TABLE IF EXISTS `insurance_consultation_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `insurance_consultation_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `insurance_consultation` bigint NOT NULL COMMENT '咨询ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='线索-保险申请关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `insurance_consultation_customer`
--

LOCK TABLES `insurance_consultation_customer` WRITE;
/*!40000 ALTER TABLE `insurance_consultation_customer` DISABLE KEYS */;
/*!40000 ALTER TABLE `insurance_consultation_customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `insurance_policy`
--

DROP TABLE IF EXISTS `insurance_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `insurance_policy` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL COMMENT '关联的代理人ID',
  `brokerage_id` bigint DEFAULT NULL COMMENT '经纪行ID',
  `customer_id` bigint NOT NULL COMMENT '关联的客户ID',
  `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '客户姓名',
  `customer_gender` enum('MALE','FEMALE','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户性别',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '市',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
  `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '邮编',
  `premium` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '保费',
  `start_date` date DEFAULT NULL COMMENT '生效日期',
  `end_date` date DEFAULT NULL COMMENT '失效日期',
  `source_type` enum('ONLINE','IMPORTED','MANUAL') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据来源: ONLINE: 在线承保, IMPORTED: 批量导入, MANUAL: 手动添加',
  `policy_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保单号',
  `application_id` bigint DEFAULT NULL COMMENT '申请ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `insurance_type` enum('HOUSE_INSURANCE','RENTERS_INSURANCE','AUTO_INSURANCE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
  `ref_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '单据编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='保险保单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `insurance_policy`
--

LOCK TABLES `insurance_policy` WRITE;
/*!40000 ALTER TABLE `insurance_policy` DISABLE KEYS */;
/*!40000 ALTER TABLE `insurance_policy` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lead`
--

DROP TABLE IF EXISTS `lead`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ref_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据编号',
  `created_by` bigint NOT NULL COMMENT '创建者的用户ID',
  `customer_province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户所在省',
  `customer_city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户所在市',
  `customer_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户地址(包括门牌号、街道名称、单元号等)',
  `customer_postal_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户邮编',
  `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户姓名',
  `customer_gender` enum('MALE','FEMALE','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户性别',
  `customer_birthday` date DEFAULT NULL COMMENT '客户出生日期',
  `customer_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户邮箱',
  `customer_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户电话',
  `assign_to` bigint DEFAULT NULL COMMENT '目标认证代理人ID',
  `status` enum('PENDING','ACCEPTED','PROCESSING','PENDING_LEAD_PAYMENT','COMPLETED','EXPIRED','WITHDRAWN','REJECTED','DRAFT') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'PENDING' COMMENT '状态',
  `referral_fee_type` enum('FIXED','PREMIUM_PERCENTAGE') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'FIXED' COMMENT '线索推荐费的类型（来自broker）',
  `referral_fee_value` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推荐费的值（来自broker）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `customer_id` bigint NOT NULL DEFAULT '-1' COMMENT '客户ID',
  `additional_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '补充信息',
  `insurance_type` enum('HOUSE_INSURANCE','RENTERS_INSURANCE','AUTO_INSURANCE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ref_code_unique` (`ref_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='线索表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lead`
--

LOCK TABLES `lead` WRITE;
/*!40000 ALTER TABLE `lead` DISABLE KEYS */;
/*!40000 ALTER TABLE `lead` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lead_application`
--

DROP TABLE IF EXISTS `lead_application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_application` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `application_id` bigint NOT NULL COMMENT '保险申请ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='线索-保险申请关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lead_application`
--

LOCK TABLES `lead_application` WRITE;
/*!40000 ALTER TABLE `lead_application` DISABLE KEYS */;
/*!40000 ALTER TABLE `lead_application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lead_referral_fee_payment`
--

DROP TABLE IF EXISTS `lead_referral_fee_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lead_referral_fee_payment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `payee` bigint NOT NULL COMMENT '收款人',
  `payer` bigint NOT NULL COMMENT '付款人',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `payment_currency` enum('CAD','USD','CNY') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'CAD' COMMENT '支付货币',
  `payment_channel` enum('STRIPE','OFFLINE') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'OFFLINE' COMMENT '支付渠道',
  `payment_amount_in_cents` bigint DEFAULT NULL COMMENT '推荐费（分）',
  `payment_status` enum('CREATED','PAID') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'CREATED' COMMENT '支付状态',
  `serial_number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '支付机构流水号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='线索推荐费支付表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lead_referral_fee_payment`
--

LOCK TABLES `lead_referral_fee_payment` WRITE;
/*!40000 ALTER TABLE `lead_referral_fee_payment` DISABLE KEYS */;
/*!40000 ALTER TABLE `lead_referral_fee_payment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission`
--

DROP TABLE IF EXISTS `permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permission_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission`
--

LOCK TABLES `permission` WRITE;
/*!40000 ALTER TABLE `permission` DISABLE KEYS */;
INSERT INTO `permission` VALUES (1,'system:user:create','系统管理-用户-新建','创建用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,'system:user:update','系统管理-用户-编辑','编辑用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,'system:user:delete','系统管理-用户-删除','删除用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,'system:user:view','系统管理-用户-查看','查看用户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,'system:role:create','系统管理-角色-新建','创建角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,'system:role:update','系统管理-角色-编辑','编辑角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,'system:role:delete','系统管理-角色-删除','删除角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(8,'system:role:view','系统管理-角色-查看','查看角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(9,'system:permission:view','系统管理-权限-查看','查看权限','2025-04-10 06:57:24','2025-04-10 06:57:24'),(10,'system:user:role:assign','系统管理-用户-角色分配','分配角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(11,'system:user:role:view','系统管理-用户-查看角色','查看角色','2025-04-10 06:57:24','2025-04-10 06:57:24'),(12,'system:role:permission:assign','系统管理-角色-权限分配','分配权限','2025-04-10 06:57:24','2025-04-10 06:57:24'),(13,'system:role:permission:view','系统管理-角色-查看撤销','查看权限','2025-04-10 06:57:24','2025-04-10 06:57:24'),(14,'insurance:customer:create','保险-客户-新建','创建客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(15,'insurance:customer:update','保险-客户-编辑','编辑客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(16,'insurance:customer:delete','保险-客户-删除','删除客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(17,'insurance:customer:view','保险-客户-查看','查看客户','2025-04-10 06:57:24','2025-04-10 06:57:24'),(18,'insurance:insurance-application:create','保险-保单申请-新建','创建房屋险','2025-04-10 06:57:24','2025-06-18 08:32:18'),(19,'insurance:insurance-application:update','保险-保单申请-新建','编辑房屋险','2025-04-10 06:57:24','2025-06-18 08:32:18'),(20,'insurance:insurance-application:delete','保险-保单申请-新建','删除房屋险','2025-04-10 06:57:24','2025-06-18 08:32:18'),(21,'insurance:insurance-application:view','保险-保单申请-新建','查看房屋险','2025-04-10 06:57:24','2025-06-18 08:32:18'),(22,'insurance:reminder:create','保险-提醒设置-新建','创建保险提醒设置','2025-06-03 09:56:28','2025-06-03 09:56:28'),(23,'insurance:reminder:update','保险-提醒设置-编辑','编辑保险提醒设置','2025-06-03 09:56:28','2025-06-03 09:56:28'),(24,'insurance:reminder:delete','保险-提醒设置-删除','删除保险提醒设置','2025-06-03 09:56:28','2025-06-03 09:56:28'),(25,'insurance:reminder:view','保险-提醒设置-查看','查看保险提醒设置','2025-06-03 09:56:28','2025-06-03 09:56:28'),(26,'insurance:policy:create','保险-保单-新建','创建保单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(27,'insurance:policy:update','保险-保单-编辑','编辑保单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(28,'insurance:policy:delete','保险-保单-删除','删除保单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(29,'insurance:policy:view','保险-保单-查看','查看保单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(30,'insurance:lead:create','保险-线索单-新建','创建线索单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(31,'insurance:lead:update','保险-线索单-编辑','编辑线索单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(32,'insurance:lead:delete','保险-线索单-删除','删除线索单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(33,'insurance:lead:view','保险-线索单-查看','查看线索单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(34,'insurance:referred-lead:view','保险-推荐线索单-查看','查看推荐的线索单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(35,'insurance:referred-lead:edit','保险-推荐线索单-编辑','编辑推荐的线索单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(36,'broker-profile:setting:my-policy','代理人-设置-我的保单','我的保单','2025-06-03 09:56:28','2025-06-03 09:56:28'),(37,'broker-profile:setting:my-friends-list','代理人-设置-我的好友','查看我的好友','2025-06-03 09:56:28','2025-06-03 09:56:28'),(38,'broker-profile:setting:poliy-renew-reminder','代理人-设置-续保通知提醒','设置续保通知提醒','2025-06-03 09:56:28','2025-06-03 09:56:28'),(39,'broker-profile:setting:accept-lead-pushing','代理人-设置-接收销售线索推送','接收线索推送设置','2025-06-03 09:56:28','2025-06-03 09:56:28'),(40,'broker-profile:setting:message-reminder','代理人-设置-消息提醒','我的消息提醒','2025-06-03 09:56:28','2025-06-03 09:56:28'),(41,'broker-profile:setting:basic-infomation','代理人-设置-基础信息','代理人基础信息','2025-06-03 09:56:28','2025-06-03 09:56:28'),(42,'brokerage:personnel:create','经纪行-用户-新建','创建用户','2025-07-03 08:00:20','2025-07-03 08:00:20'),(43,'brokerage:personnel:edit','经纪行-用户-编辑','编辑用户','2025-07-03 08:00:20','2025-07-03 08:00:20'),(44,'brokerage:company:create','经纪行-公司-创建','创建公司','2025-07-03 08:00:20','2025-07-03 08:00:20'),(45,'brokerage:company:edit','经纪行-公司-编辑','编辑公司','2025-07-03 08:00:20','2025-07-03 08:00:20'),(46,'brokerage:company:delete','经纪行-公司-删除','删除公司','2025-07-03 08:00:20','2025-07-03 08:00:20'),(47,'brokerage:company:view','经纪行-公司-查看','查看公司','2025-07-03 08:00:20','2025-07-03 08:00:20'),(48,'brokerage:personnel:view','经纪行-用户-查看','查看用户','2025-07-03 08:00:20','2025-07-03 08:00:20'),(49,'brokerage:personnel:delete','经纪行-用户-删除','删除用户','2025-07-03 08:00:20','2025-07-03 08:00:20'),(50,'brokerage:personnel-menu:view','经纪行-职员菜单-查看','查看到职员管理菜单','2025-07-03 08:00:21','2025-07-03 08:00:21'),(51,'brokerage:application-menu:view','经纪行-订单菜单-查看','查看到订单菜单','2025-07-03 08:00:21','2025-07-03 08:00:21'),(52,'brokerage:company-menu:view','经纪行-企业信息菜单-查看','查看到企业信息菜单','2025-07-03 08:00:21','2025-07-03 08:00:21');
/*!40000 ALTER TABLE `permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `promotion_material`
--

DROP TABLE IF EXISTS `promotion_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `promotion_material` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `broker_id` bigint NOT NULL COMMENT '关联代理人ID',
  `image` varchar(500) NOT NULL COMMENT '素材URL',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='推广素材表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `promotion_material`
--

LOCK TABLES `promotion_material` WRITE;
/*!40000 ALTER TABLE `promotion_material` DISABLE KEYS */;
/*!40000 ALTER TABLE `promotion_material` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reminder_config`
--

DROP TABLE IF EXISTS `reminder_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reminder_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `broker_id` bigint NOT NULL COMMENT '代理人ID',
  `reminder_type` enum('CUSTOMER','BROKER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '提醒类型: CUSTOMER: 客户, BROKER: 代理人',
  `first_reminder_days` int NOT NULL COMMENT '第一次提前天数',
  `second_reminder_days` int DEFAULT NULL COMMENT '第二次提前天数',
  `notify_methods` set('INBOX','EMAIL','SMS') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '提醒方式（多选）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `business_type` set('POLICY_RENEWAL','PREMIUM_CALCULATED','POLICY_REJECT','POLICY_ISSUED','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '业务类型',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_broker_reminder_business` (`broker_id`,`reminder_type`,`business_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='提醒配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reminder_config`
--

LOCK TABLES `reminder_config` WRITE;
/*!40000 ALTER TABLE `reminder_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `reminder_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reminder_message`
--

DROP TABLE IF EXISTS `reminder_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reminder_message` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_type` enum('POLICY_RENEWAL','PREMIUM_CALCULATED','POLICY_REJECT','POLICY_ISSUED','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '业务类型',
  `read_status` tinyint NOT NULL DEFAULT '0' COMMENT '阅读状态',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '消息内容',
  `sender_id` bigint DEFAULT NULL COMMENT '发送人ID: user_id（0=系统消息）',
  `receiver_id` bigint NOT NULL COMMENT '接收人ID: user_id',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `extra_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '额外参数 如 {"customer_name": "张三"}',
  `insurance_type` enum('HOUSE_INSURANCE','RENTERS_INSURANCE','AUTO_INSURANCE') COLLATE utf8mb4_bin NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='站内信消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reminder_message`
--

LOCK TABLES `reminder_message` WRITE;
/*!40000 ALTER TABLE `reminder_message` DISABLE KEYS */;
/*!40000 ALTER TABLE `reminder_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role`
--

DROP TABLE IF EXISTS `role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role`
--

LOCK TABLES `role` WRITE;
/*!40000 ALTER TABLE `role` DISABLE KEYS */;
INSERT INTO `role` VALUES (1,'administrator','系统管理员','2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,'broker','保险代理人','2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,'referral-broker','泛保险代理人角色','2025-06-03 09:56:28','2025-07-03 08:00:20'),(4,'broker-support','经纪行内勤角色','2025-07-03 08:00:20','2025-07-03 08:00:20'),(5,'brokerage-admin','经纪行管理员角色','2025-07-03 08:00:20','2025-07-03 08:00:20');
/*!40000 ALTER TABLE `role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permission`
--

DROP TABLE IF EXISTS `role_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL,
  `permission_id` bigint NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_id_permission_id` (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permission_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`),
  CONSTRAINT `role_permission_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=105 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='角色-权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permission`
--

LOCK TABLES `role_permission` WRITE;
/*!40000 ALTER TABLE `role_permission` DISABLE KEYS */;
INSERT INTO `role_permission` VALUES (1,1,1,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,1,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,1,3,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,1,4,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,1,5,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,1,6,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,1,7,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(8,1,8,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(9,1,9,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(10,1,10,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(11,1,11,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(12,1,12,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(13,1,13,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(14,2,14,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(15,2,15,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(16,2,16,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(17,2,17,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(18,2,18,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(19,2,19,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(20,2,20,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(21,2,21,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(22,2,22,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(23,2,23,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(24,2,24,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(25,2,25,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(26,2,26,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(27,2,27,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(28,2,28,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(29,2,29,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(30,2,34,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(31,2,35,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(32,2,36,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(33,2,37,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(34,2,38,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(35,2,39,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(36,2,40,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(37,2,41,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(38,3,30,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(39,3,31,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(40,3,32,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(41,3,33,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(42,3,37,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(43,3,41,'2025-06-03 09:56:28','2025-06-03 09:56:28'),(44,3,14,'2025-06-03 09:56:29','2025-06-03 09:56:29'),(45,3,15,'2025-06-03 09:56:29','2025-06-03 09:56:29'),(46,3,16,'2025-06-03 09:56:29','2025-06-03 09:56:29'),(47,3,17,'2025-06-03 09:56:29','2025-06-03 09:56:29'),(48,3,21,'2025-06-03 09:56:29','2025-06-03 09:56:29'),(84,4,18,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(85,4,19,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(86,4,20,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(87,4,21,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(88,5,18,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(89,5,19,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(90,5,20,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(91,5,21,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(92,5,42,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(93,5,43,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(94,5,45,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(95,1,44,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(96,1,47,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(97,5,48,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(98,5,49,'2025-07-03 08:00:20','2025-07-03 08:00:20'),(99,4,48,'2025-07-03 08:00:21','2025-07-03 08:00:21'),(100,4,51,'2025-07-03 08:00:21','2025-07-03 08:00:21'),(101,4,52,'2025-07-03 08:00:21','2025-07-03 08:00:21'),(102,5,50,'2025-07-03 08:00:21','2025-07-03 08:00:21'),(103,5,52,'2025-07-03 08:00:21','2025-07-03 08:00:21'),(104,1,42,'2025-07-21 04:32:28','2025-07-21 04:32:28');
/*!40000 ALTER TABLE `role_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `schema_migrations`
--

DROP TABLE IF EXISTS `schema_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schema_migrations` (
  `version` bigint NOT NULL,
  `dirty` tinyint(1) NOT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `schema_migrations`
--

LOCK TABLES `schema_migrations` WRITE;
/*!40000 ALTER TABLE `schema_migrations` DISABLE KEYS */;
INSERT INTO `schema_migrations` VALUES (32,0);
/*!40000 ALTER TABLE `schema_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '邮箱',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '手机号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '姓名',
  `user_type` enum('SAAS','SYSTEM') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'SAAS' COMMENT '用户类型',
  `status` enum('ACTIVE','INACTIVE') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `language` enum('zh','en','fr') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'zh' COMMENT '语言',
  `referer_id` bigint DEFAULT NULL COMMENT '推荐人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `avatar` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (1,'<EMAIL>',NULL,'$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W','Admin','SYSTEM','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL),(2,'<EMAIL>',NULL,'$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W','Test Broker','SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL),(3,'<EMAIL>',NULL,'$2b$12$QjEcW6CD3UwJVBmEbxPCHeC/dPZn2Q9kQUSH/apOUmJVAs7gLZh8.','Test User','SAAS','ACTIVE','zh',2,'2025-04-10 06:57:24','2025-06-03 09:56:28',NULL),(4,'<EMAIL>',NULL,'$2b$12$KKqJsmkbjjvJTypw457fpOkpQYESoKOl0kyk1WRHlVgjqceEDA4Mm','Test1 User','SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL),(5,'<EMAIL>',NULL,'$2b$12$eiqeBt1x.yQ.1v1yW6dJkuYh0UwOf7bJIFN6Y/tp7I9oRGk0rYqwa','Test2 User','SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL),(6,'<EMAIL>',NULL,'$2b$12$QjEcW6CD3UwJVBmEbxPCHeC/dPZn2Q9kQUSH/apOUmJVAs7gLZh8.',NULL,'SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL),(7,'<EMAIL>',NULL,'$2b$12$KKqJsmkbjjvJTypw457fpOkpQYESoKOl0kyk1WRHlVgjqceEDA4Mm',NULL,'SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL),(8,'<EMAIL>',NULL,'$2b$12$QjEcW6CD3UwJVBmEbxPCHeC/dPZn2Q9kQUSH/apOUmJVAs7gLZh8.',NULL,'SAAS','ACTIVE','zh',NULL,'2025-04-10 06:57:24','2025-04-10 06:57:24',NULL);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_feedback`
--

DROP TABLE IF EXISTS `user_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_feedback` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `contact` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '联系方式',
  `feedback_type` enum('FEATURE_SUGGESTION','USAGE_ISSUE','INTERFACE_OPTIMIZATION','CONTENT_ERROR','OTHER') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'FEATURE_SUGGESTION' COMMENT '反馈类型',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '反馈内容',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户反馈表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_feedback`
--

LOCK TABLES `user_feedback` WRITE;
/*!40000 ALTER TABLE `user_feedback` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_feedback` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_role`
--

DROP TABLE IF EXISTS `user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id_role_id` (`user_id`,`role_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_role_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `user_role_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户-角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_role`
--

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;
INSERT INTO `user_role` VALUES (1,1,1,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(2,2,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(3,3,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(4,4,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(5,5,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(6,6,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(7,7,2,'2025-04-10 06:57:24','2025-04-10 06:57:24'),(8,8,2,'2025-04-10 06:57:24','2025-04-10 06:57:24');
/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-21 12:32:56

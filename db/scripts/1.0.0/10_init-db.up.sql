-- Version: 1.0.0
-- CREATE DATABASE `ca_insurance_dev` CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
-- create user 'admin'@'%' identified by 'Password!23';
-- grant all privileges on `ca_insurance_dev`.* to 'admin'@'%' with grant option;


create table if not exists `user` (
  `id` bigint not null auto_increment comment '用户ID',
  `email` varchar(255) not null comment '邮箱',
  `mobile` varchar(255) comment '手机号',
  `password` varchar(255) comment '密码',
  `name` varchar(255) comment '姓名',
  `user_type` enum('SAAS', 'SYSTEM') not null default 'SAAS' comment '用户类型',
  `status` enum('ACTIVE', 'INACTIVE') not null default 'ACTIVE' comment '状态',
  `language` enum('zh', 'en', 'fr') not null default 'zh' comment '语言',
  `referer_id` bigint default null comment '推荐人ID',
  `created_at` timestamp not null default current_timestamp comment '创建时间',
  `updated_at` timestamp not null default current_timestamp on update current_timestamp comment '更新时间',
  primary key (`id`),
  unique key `email` (`email`)
) engine=InnoDB default charset=utf8mb4 comment='用户表';

create table if not exists `role` (
  `id` bigint not null auto_increment,
  `name` varchar(255) not null,
  `description` varchar(255) not null,
  `created_at` timestamp not null default current_timestamp,
  `updated_at` timestamp not null default current_timestamp on update current_timestamp,
  primary key (`id`),
  unique key `name` (`name`)
) engine=InnoDB default charset=utf8mb4;

create table if not exists `user_role` (
  `id` bigint not null auto_increment,
  `user_id` bigint not null,
  `role_id` bigint not null,
  `created_at` timestamp not null default current_timestamp,
  `updated_at` timestamp not null default current_timestamp on update current_timestamp,
  primary key (`id`),
  unique key `user_id_role_id` (`user_id`, `role_id`),
  foreign key (`user_id`) references `user` (`id`),
  foreign key (`role_id`) references `role` (`id`)
) engine=InnoDB default charset=utf8mb4;

create table if not exists `permission` (
  `id` bigint not null auto_increment,
  `code` varchar(255) not null,
  `name` varchar(255) not null,
  `description` varchar(255) not null,
  `created_at` timestamp not null default current_timestamp,
  `updated_at` timestamp not null default current_timestamp on update current_timestamp,
  primary key (`id`),
  unique key `permission_code` (`code`)
) engine=InnoDB default charset=utf8mb4;

create table if not exists `role_permission` (
  `id` bigint not null auto_increment,
  `role_id` bigint not null,
  `permission_id` bigint not null,
  `created_at` timestamp not null default current_timestamp,
  `updated_at` timestamp not null default current_timestamp on update current_timestamp,
  primary key (`id`),
  unique key `role_id_permission_id` (`role_id`, `permission_id`),
  foreign key (`role_id`) references `role` (`id`),
  foreign key (`permission_id`) references `permission` (`id`)
) engine=InnoDB default charset=utf8mb4;

-- Create tables and insert data for user, role, permission, user_role, role_permission
-- admin和testbroker的密码都是Password!23
insert into
    `user`(`id`, `email`, `password`, `name`, `user_type`, `status`)

values
    (1, '<EMAIL>','$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W','Admin','SYSTEM','ACTIVE'),
    (2, 'testbroker@total_insured.com','$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W','Test Broker','SAAS','ACTIVE');

insert into
    `role`(`id`, `name`, `description`)
values
    (1, 'administrator', '系统管理员'),
    (2, 'broker', '保险代理人');

insert into
    `user_role`(`id`, `user_id`, `role_id`)
values
    (1, 1, 1),
    (2, 2, 2);

insert into `permission`(`id`, `code`, `name`, `description`)
values
    (1, 'system:user:create', '系统管理-用户-新建', "创建用户"),
    (2, 'system:user:update', '系统管理-用户-编辑', "编辑用户"),
    (3, 'system:user:delete', '系统管理-用户-删除', "删除用户"),
    (4, 'system:user:view', '系统管理-用户-查看', "查看用户"),
    (5, 'system:role:create', '系统管理-角色-新建', "创建角色"),
    (6, 'system:role:update', '系统管理-角色-编辑', "编辑角色"),
    (7, 'system:role:delete', '系统管理-角色-删除', "删除角色"),
    (8, 'system:role:view', '系统管理-角色-查看', "查看角色"),
    (9, 'system:permission:view', '系统管理-权限-查看', "查看权限"),
    (10, 'system:user:role:assign', '系统管理-用户-角色分配', "分配角色"),
    (11, 'system:user:role:view', '系统管理-用户-查看角色', "查看角色"),
    (12, 'system:role:permission:assign', '系统管理-角色-权限分配', "分配权限"),
    (13, 'system:role:permission:view', '系统管理-角色-查看撤销', "查看权限");

insert into `role_permission`(`id`, `role_id`, `permission_id`)
values
    (1, 1, 1),
    (2, 1, 2),
    (3, 1, 3),
    (4, 1, 4),
    (5, 1, 5),
    (6, 1, 6),
    (7, 1, 7),
    (8, 1, 8),
    (9, 1, 9),
    (10, 1, 10),
    (11, 1, 11),
    (12, 1, 12),
    (13, 1, 13);

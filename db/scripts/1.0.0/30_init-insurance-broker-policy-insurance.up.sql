-- broker
CREATE TABLE `broker` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '姓名',
    `address` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
    `phone` VARCHAR(20) COMMENT '电话',
    `business_kind` varchar(255) NOT NULL default '房屋保险' COMMENT '业务类型',
    `insurance_company` VARCHAR(255) COMMENT '保险公司',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保险代理人信息表';

-- customer
CREATE TABLE `customer` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id` BIGINT NOT NULL COMMENT '代理人ID',
    `name` VARCHAR(255) NOT NULL COMMENT '名字',
    `address` VARCHAR(500) NOT NULL COMMENT '全地址，包括州、城市、街道地址(包括门牌号、街道名称、单元号等)和邮编',
    `email` VARCHAR(255) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '电话',
    `move_in_date` DATE COMMENT '入住日期',
    `birthday` DATE COMMENT '出生日期',
    `is_deleted` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP COMMENT '删除时间'
);

-- insurance_company
CREATE TABLE `insurance_company` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL COMMENT '保险公司名称',
    `code` VARCHAR(20) COMMENT '保司编码',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    unique key `insurance_company_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保险公司信息表';

-- 房屋保险申请表
CREATE TABLE house_insurance_application (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id` BIGINT COMMENT '关联的代理人ID',
    `customer_id` BIGINT NOT NULL COMMENT '关联的客户ID',
    `address` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
    `customer_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '客户姓名',
    `email` VARCHAR(255) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '电话',
    `birthday` DATE COMMENT '出生日期',
    `house_area` DECIMAL(10, 2) COMMENT '房屋面积（平方米，不含地下室）',
    `year_built` YEAR COMMENT '房屋建造年份',
    `is_owner` BOOLEAN NOT NULL COMMENT '是否本人房屋',
    `house_type` VARCHAR(50) COMMENT '房屋类型',
    `is_mortgage` BOOLEAN COMMENT '是否存在抵押',
    `is_first_apply` BOOLEAN  DEFAULT 0 COMMENT '是否首次投保',
    `has_rejection_record` BOOLEAN DEFAULT 0 COMMENT '是否有拒保记录',
    `expected_start_date` DATE COMMENT '预计生效日期',
    `start_date` DATE COMMENT '生效日期',
    `end_date` DATE COMMENT '失效日期',
    `serial_number` VARCHAR(100) NOT NULL COMMENT '唯一编码，可以根据这个编码在文件系统查询详细数据和附件',
    `status` ENUM('PENDING', 'UNDERWRITTEN') DEFAULT 'PENDING' COMMENT '状态：待承保和已承保',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否删除',
    `deleted_at` TIMESTAMP COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家庭保险申请表';

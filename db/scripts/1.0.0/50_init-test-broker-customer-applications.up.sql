insert into `broker`(`id`, `user_id`, `name`, `address`, `phone`, `business_kind`, `insurance_company`, `created_at`, `updated_at`)
values
    (1, 2, 'Test Broker', '123 Main St, San Francisco, CA 94122', '************', 'Home Insurance', 'test_ic', now(), now());

insert into `customer`(`id`, `broker_id`, `name`, `address`, `email`, `phone`, `move_in_date`, `birthday`, `is_deleted`, `created_at`, `updated_at`, `deleted_at`)
values
    (1, 1, '<PERSON>', '123 Main St, San Francisco, CA 94122', '<EMAIL>', '************', '2020-01-01', '1980-01-01', 0, now(), now(), null),
    (2, 1, '<PERSON>', '456 Main St, San Francisco, CA 94122', 'jane<PERSON><EMAIL>', '************', '2021-01-01', '1985-03-04', 0, now(), now(), null);

insert into `house_insurance_application`(`id`, `broker_id`, `customer_id`, `address`, `customer_name`, `email`, `phone`, `birthday`, `house_area`, `year_built`, `is_owner`, `house_type`, `is_mortgage`, `is_first_apply`, `has_rejection_record`, `expected_start_date`, `start_date`, `end_date`, `serial_number`, `status`)
values
    (1, 1, 1, '123 Main St, San Francisco, CA 94122', 'John Doe', '<EMAIL>', '************', '1980-01-01', 100.00, 2000, 1, 'Single Family', 0, 1, 0, '2025-01-01', '2025-01-01', '2026-01-01', 'serial_number_1', 'UNDERWRITTEN'),
    (2, 1, 2, '456 Main St, San Francisco, CA 94122', 'Jane Doe', '<EMAIL>', '************', '1985-03-04', 200.00, 2010, 0, 'Condo', 1, 1, 0, '2025-09-01', '2025-09-01', '2026-09-01', 'serial_number_2', 'UNDERWRITTEN'),
    (3, 1, 2, '789 Main St, San Francisco, CA 94122', 'Jane Doe', '<EMAIL>', '************', '1985-03-04', 200.00, 2010, 0, 'Condo', 1, 1, 0, '2025-09-01', null, null, 'serial_number_3', 'PENDING');

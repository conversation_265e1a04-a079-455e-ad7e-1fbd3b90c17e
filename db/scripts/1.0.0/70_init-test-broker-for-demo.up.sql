insert into
    `user`(`id`, `email`, `password`, `name`, `user_type`, `status`)
values
    (3, '<EMAIL>', '$2b$12$QjEcW6CD3UwJVBmEbxPCHeC/dPZn2Q9kQUSH/apOUmJVAs7gLZh8.', 'Test User', 'SAAS', 'ACTIVE'),
    (4, '<EMAIL>', '$2b$12$KKqJsmkbjjvJTypw457fpOkpQYESoKOl0kyk1WRHlVgjqceEDA4Mm', 'Test1 User', 'SAAS', 'ACTIVE'),
    (5, '<EMAIL>', '$2b$12$eiqeBt1x.yQ.1v1yW6dJkuYh0UwOf7bJIFN6Y/tp7I9oRGk0rYqwa', 'Test2 User', 'SAAS', 'ACTIVE');

insert into
    `user_role`(`id`, `user_id`, `role_id`)
values
    (3, 3, 2),
    (4, 4, 2),
    (5, 5, 2);

insert into `broker`(`id`, `user_id`, `name`, `address`, `phone`, `business_kind`, `insurance_company`, `created_at`, `updated_at`)
values
    (2, 3, 'Test Broker', '123 Main St, San Francisco, CA 94122', '************', 'Home Insurance', 'test_ic', now(), now()),
    (3, 4, 'Test1 Broker', '123 Main St, San Francisco, CA 94122', '************', 'Home Insurance', 'test_ic', now(), now()),
    (4, 5, 'Test2 Broker', '123 Main St, San Francisco, CA 94122', '************', 'Home Insurance', 'test_ic', now(), now());

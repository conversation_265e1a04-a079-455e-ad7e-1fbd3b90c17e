CREATE TABLE `lead`
(
    `id`                   BIGINT AUTO_INCREMENT PRIMARY KEY,
    `business_kind`        ENUM('HOUSE_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '线索的业务类型（参考broker同名字段）',
    `created_by`           BIGINT         NOT NULL COMMENT '创建者的用户ID',
    `customer_province`    VARCHAR(100)   NOT NULL COMMENT '客户所在省',
    `customer_city`        VARCHAR(100)   NOT NULL COMMENT '客户所在市',
    `customer_address`     VARCHAR(500)   NOT NULL COMMENT '客户地址(包括门牌号、街道名称、单元号等)',
    `customer_postal_code` VARCHAR(500)            DEFAULT NULL COMMENT '客户邮编',
    `customer_name`        VARCHAR(255)   NOT NULL COMMENT '客户姓名',
    `customer_birthday`    DATE           NOT NULL COMMENT '客户出生日期',
    `customer_email`       VARCHAR(255)   NOT NULL COMMENT '客户邮箱',
    `customer_phone`       VARCHAR(20)    NOT NULL COMMENT '客户电话',
    `assign_to`            BIGINT         NOT NULL COMMENT '目标认证代理人ID',
    `status`               ENUM('PENDING','ACCEPTED','PROCESSING','PENDING_LEAD_PAYMENT','COMPLETED','EXPIRED','WITHDREW','REJECTED') NOT NULL DEFAULT 'PENDING' COMMENT '状态',
    `referral_fee_type`    ENUM('FIXED','PREMIUM_PERCENTAGE') NOT NULL DEFAULT 'FIXED' COMMENT '线索推荐费的类型（来自broker）',
    `referral_fee_value`   DECIMAL(10, 2) NOT NULL DEFAULT 0 COMMENT '推荐费的值（来自broker）',
    `created_at`           TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线索表';

CREATE TABLE `lead_referral_fee_payment`
(
    `id`                      BIGINT AUTO_INCREMENT PRIMARY KEY,
    `payee`                   BIGINT       NOT NULL COMMENT '收款人',
    `payer`                   BIGINT       NOT NULL COMMENT '付款人',
    `lead_id`                 BIGINT       NOT NULL COMMENT '线索ID',
    `payment_currency`        ENUM('CAD','USD', 'CNY') NOT NULL DEFAULT 'CAD' COMMENT '支付货币',
    `payment_channel`         ENUM('STRIPE','OFFLINE') NOT NULL DEFAULT 'OFFLINE' COMMENT '支付渠道',
    `payment_amount_in_cents` BIGINT COMMENT '推荐费（分）',
    `payment_status`          ENUM('CREATED','PAID') NOT NULL DEFAULT 'CREATED' COMMENT '支付状态',
    `serial_number`           VARCHAR(128) NOT NULL DEFAULT '' COMMENT '支付机构流水号',
    `created_at`              TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`              TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线索推荐费支付表';

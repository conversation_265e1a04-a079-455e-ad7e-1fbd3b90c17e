CREATE TABLE `broker_profile`
(
    `id`            BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id`     BIGINT    NOT NULL COMMENT '关联代理人ID',
    `public_fields` VARCHAR(1024) NOT NULL DEFAULT 'NAME|EMAIL' COMMENT '公开的个人信息, 用｜分割，包括姓名、站内信、手机、邮箱等',
    `created_at`    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理人档案表';

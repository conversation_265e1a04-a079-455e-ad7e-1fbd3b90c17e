CREATE TABLE broker_payment_method
(
    `id`             BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id`      BIGINT       NOT NULL,
    `account_type`   ENUM('E_TRANSFER') NOT NULL DEFAULT 'E_TRANSFER' COMMENT '账户类型',
    `account_number` VARCHAR(255) NOT NULL COMMENT '账户编码（加密存储）',
    -- 当前只有一个E-Transfer账户
    `is_default`     BOOLEAN               DEFAULT TRUE COMMENT '是否是默认支付账户',
    `created_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_account_for_each_broker` (`broker_id`, `account_type`, `account_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理人支付方式表';

CREATE TABLE `broker_lead_config`
(
    `id`                 BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id`          BIGINT         NOT NULL COMMENT '关联代理人ID',
    `allow_leads`        BOOLEAN        NOT NULL DEFAULT FALSE COMMENT '是否接受线索',
    `referral_fee_type`  ENUM('PREMIUM_PERCENTAGE','FIXED') NOT NULL DEFAULT 'PREMIUM_PERCENTAGE' COMMENT '佣金类型',
    `referral_fee_value` DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT '佣金值（固定金额或百分比，其含义依据佣金类型而定）',
    `created_at`         TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理人线索与佣金配置表';

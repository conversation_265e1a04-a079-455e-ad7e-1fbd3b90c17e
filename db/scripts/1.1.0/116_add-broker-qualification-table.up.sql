CREATE TABLE `broker_qualification`
(
    `id`                BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_profile_id` BIGINT    NOT NULL COMMENT '代理人档案ID（不是代理人，目前一对一，今后也许一对多，即一个代理人拥有多种资质，而不仅仅是‘有无资质’）',
    `is_qualified`      BOOLEAN            DEFAULT FALSE COMMENT '是否认证',
    `created_at`        TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理人资质认证表';

CREATE TABLE house_insurance_policy (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id` BIGINT NOT NULL COMMENT '关联的代理人ID',
    `customer_id` BIGINT NOT NULL COMMENT '关联的客户ID',
    `customer_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '客户姓名',
    `email` VARCHAR(255) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '电话',
    `province` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '省',
    `city` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '市',
    `address` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
    `postal_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '邮编',
    `premium` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '保费',
    `start_date` DATE COMMENT '生效日期',
    `end_date` DATE COMMENT '失效日期',
    `source_type` VARCHAR(20) NOT NULL DEFAULT 'online' COMMENT '来源类型',
    `application_id` BIGINT COMMENT '申请ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否删除',
    `deleted_at` TIMESTAMP NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋保险保单表';

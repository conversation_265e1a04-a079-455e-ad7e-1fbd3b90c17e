CREATE TABLE reminder_config (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `broker_id` BIGINT NOT NULL UNIQUE COMMENT '代理人ID',
    `reminder_type` ENUM('CUSTOMER', 'BROKER') NOT NULL COMMENT '提醒类型: CUSTOMER: 客户, BROKER: 代理人',
    `business_type` SET('POLICY_RENEWAL', 'PREMIUM_CALCULATED', 'POLICY_ISSUED', 'OTHER') NOT NULL COMMENT '业务类型：policy_renewal: 保单续期提醒, PREMIUM_CALCULATED: 保单计算提醒, POLICY_ISSUED: 保单下发提醒, OTHER: 其它',
    `first_reminder_days` INT NOT NULL COMMENT '第一次提前天数',
    `second_reminder_days` INT NULL COMMENT '第二次提前天数',
    `notify_methods` SET('INBOX', 'EMAIL', 'SMS') NOT NULL COMMENT '提醒方式（多选）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uq_broker_reminder_type (`broker_id`, `reminder_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提醒配置表';

CREATE TABLE `reminder_message` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `business_type` ENUM('POLICY_RENEWAL', 'PREMIUM_CALCULATED', 'POLICY_ISSUED', 'OTHER') NOT NULL COMMENT '业务类型：POLICY_RENEWAL: 保单续期提醒, PREMIUM_CALCULATED: 保单计算提醒, POLICY_ISSUED: 保单下发提醒, OTHER: 其它',
    `read_status` TINYINT NOT NULL DEFAULT 0 COMMENT '阅读状态',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `sender_id` BIGINT DEFAULT NULL COMMENT '发送人ID: user_id（0=系统消息）',
    `receiver_id` BIGINT NOT NULL COMMENT '接收人ID: user_id',
    `is_deleted` BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='站内信消息表';

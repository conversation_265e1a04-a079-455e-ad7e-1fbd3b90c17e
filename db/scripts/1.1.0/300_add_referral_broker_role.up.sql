insert into `permission`(`id`, `code`, `name`, `description`)
values
    (26, 'insurance:policy:create', '保险-保单-新建', "创建保单"),
    (27, 'insurance:policy:update', '保险-保单-编辑', "编辑保单"),
    (28, 'insurance:policy:delete', '保险-保单-删除', "删除保单"),
    (29, 'insurance:policy:view', '保险-保单-查看', "查看保单"),
    (30, 'insurance:lead:create', '保险-线索单-新建', "创建线索单"),
    (31, 'insurance:lead:update', '保险-线索单-编辑', "编辑线索单"),
    (32, 'insurance:lead:delete', '保险-线索单-删除', "删除线索单"),
    (33, 'insurance:lead:view', '保险-线索单-查看', "查看线索单"),
    (34, 'insurance:referred-lead:view', '保险-推荐线索单-查看', "查看推荐的线索单"),
    (35, 'insurance:referred-lead:edit', '保险-推荐线索单-编辑', "编辑推荐的线索单"),
    (36, 'broker-profile:setting:my-policy', '代理人-设置-我的保单', "我的保单"),
    (37, 'broker-profile:setting:my-friends-list', '代理人-设置-我的好友', "查看我的好友"),
    (38, 'broker-profile:setting:poliy-renew-reminder', '代理人-设置-续保通知提醒', "设置续保通知提醒"),
    (39, 'broker-profile:setting:accept-lead-pushing', '代理人-设置-接收销售线索推送', "接收线索推送设置"),
    (40, 'broker-profile:setting:message-reminder', '代理人-设置-消息提醒', "我的消息提醒"),
    (41, 'broker-profile:setting:basic-infomation', '代理人-设置-基础信息', "代理人基础信息");


insert into `role`(`id`, `name`, `description`)
values
    (3, '泛代理人', '泛保险代理人角色');

insert into `role_permission`(`id`, `role_id`, `permission_id`)
values
    (26, 2, 26),
    (27, 2, 27),
    (28, 2, 28),
    (29, 2, 29),
    (30, 2, 34),
    (31, 2, 35),
    (32, 2, 36),
    (33, 2, 37),
    (34, 2, 38),
    (35, 2, 39),
    (36, 2, 40),
    (37, 2, 41),
    (38, 3, 30),
    (39, 3, 31),
    (40, 3, 32),
    (41, 3, 33),
    (42, 3, 37),
    (43, 3, 41);

ALTER TABLE `reminder_config` DROP INDEX `uq_broker_reminder_type`;

ALTER TABLE `reminder_config` DROP COLUMN `business_type`;

ALTER TABLE `reminder_config`
ADD COLUMN `business_type` ENUM(
    'POLICY_RENEWAL',
    'PREMIUM_CALCULATED',
    'POLICY_ISSUED',
    'OTHER'
) NOT NULL COMMENT '业务类型';

ALTER TABLE `reminder_config`
ADD UNIQUE KEY `uq_broker_reminder_business` (
    `broker_id`,
    `reminder_type`,
    `business_type`
);

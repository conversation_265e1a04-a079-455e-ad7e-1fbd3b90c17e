ALTER TABLE `lead`
MODIFY COLUMN `status` ENUM(
    'PENDING',
    'ACCEPTED',
    'PROCESSING',
    'PENDING_LEAD_PAYMENT',
    'COMPLETED',
    'EXPIRED',
    'WITHDREW',   -- 旧值（暂时保留）
    'WITHDRAWN',  -- 新值（正确拼写）
    'REJECTED'
) NOT NULL DEFAULT 'PENDING' COMMENT '状态';

UPDATE `lead`
SET `status` = 'WITHDRAWN'
WHERE `status` = 'WITHDREW';

ALTER TABLE `lead`
MODIFY COLUMN `status` ENUM(
    'PENDING',
    'ACCEPTED',
    'PROCESSING',
    'PENDING_LEAD_PAYMENT',
    'COMPLETED',
    'EXPIRED',
    'WITHDRAWN',  -- 仅保留正确值
    'REJECTED'
) NOT NULL DEFAULT 'PENDING' COMMENT '状态';

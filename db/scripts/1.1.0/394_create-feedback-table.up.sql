CREATE TABLE user_feedback (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `contact` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '联系方式',
    `feedback_type` ENUM('FEATURE_SUGGESTION','USAGE_ISSUE','INTERFACE_OPTIMIZATION','CONTENT_ERROR','OTHER') NOT NULL DEFAULT 'FEATURE_SUGGESTION' COMMENT '反馈类型',
    `content` TEXT NOT NULL COMMENT '反馈内容',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈表';

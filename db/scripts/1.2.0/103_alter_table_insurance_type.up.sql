ALTER TABLE `broker`
DROP COLUMN `business_kind`;

ALTER TABLE `broker`
ADD COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `lead`
DROP COLUMN `business_kind`;

ALTER TABLE `lead`
ADD COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `insurance_application`
ADD COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `insurance_policy`
ADD COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

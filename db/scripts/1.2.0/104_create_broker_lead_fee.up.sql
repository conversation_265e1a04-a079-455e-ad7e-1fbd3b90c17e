CREATE TABLE `broker_lead_fee` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id` BIGINT NOT NULL COMMENT '代理人ID',
    `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型',
    `referral_fee_type` ENUM('PREMIUM_PERCENTAGE','FIXED') NOT NULL DEFAULT 'PREMIUM_PERCENTAGE' COMMENT '佣金类型',
    `referral_fee_value` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金值（固定金额或百分比，其含义依据佣金类型而定）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `idx_broker_insurance_type` (`broker_id`, `insurance_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理人线索付费配置表';

INSERT INTO broker_lead_fee (
    broker_id,
    referral_fee_type,
    referral_fee_value,
    insurance_type
)
SELECT
    broker_id,
    referral_fee_type,
    referral_fee_value,
    'HOUSE_INSURANCE' AS insurance_type  -- 使用默认值
FROM (
    SELECT
        broker_id,
        referral_fee_type,
        referral_fee_value,
        ROW_NUMBER() OVER (PARTITION BY broker_id ORDER BY updated_at DESC) AS rn
    FROM broker_lead_config
) AS ranked
WHERE rn = 1;  -- 确保每个broker_id只取最新的一条记录

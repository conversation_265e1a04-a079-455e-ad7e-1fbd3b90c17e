-- Create table for brokerage
create table if not exists `brokerage` (
    `id` bigint not null auto_increment comment '经纪行ID',
    `name` varchar(255) comment '经纪行名称',
    `province` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '省',
    `city` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '市',
    `address` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '街道地址(包括门牌号、街道名称、单元号等)',
    `postal_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '邮编',
    `contact_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '联系人姓名',
    `contact_email` VARCHAR(255) COMMENT '联系人邮箱',
    `contact_phone` VARCHAR(20) COMMENT '联系人电话',
    `created_at` timestamp not null default current_timestamp comment '创建时间',
    `updated_at` timestamp not null default current_timestamp on update current_timestamp comment '更新时间',
    primary key (`id`)
) engine=InnoDB default charset=utf8mb4 comment='经纪行表';

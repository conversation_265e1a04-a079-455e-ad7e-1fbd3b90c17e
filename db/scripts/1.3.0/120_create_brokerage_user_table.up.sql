-- Create table for brokerage
create table if not exists `brokerage_user` (
    `id` bigint not null auto_increment comment '经纪行用户ID',
    `user_id` bigint not null comment '用户ID',
    `brokerage_id` bigint not null comment '经纪行ID',
    `name` varchar(255) comment '姓名',
    `created_at` timestamp not null default current_timestamp comment '创建时间',
    `updated_at` timestamp not null default current_timestamp on update current_timestamp comment '更新时间',
    primary key (`id`)
) engine=InnoDB default charset=utf8mb4 comment='经纪行用户表';

ALTER TABLE `insurance_application`
    ADD COLUMN `brokerage_id` BIGINT NULL COMMENT '经纪行ID' AFTER `broker_id`,
    ADD COLUMN `operator_id` BIGINT NULL COMMENT '操作人ID' AFTER `status`,
    ADD COLUMN `operator_name` VARCHAR(64) NULL COMMENT '操作人姓名' AFTER `operator_id`,
    ADD COLUMN `request_quote_at` TIMESTAMP NULL COMMENT '提交时间' AFTER `operator_name`,
    ADD COLUMN `quote_at` TIMESTAMP NULL COMMENT '报价时间' AFTER `request_quote_at`,
    ADD COLUMN `underwrite_at` TIMESTAMP NULL COMMENT '承保时间' AFTER `quote_at`,
    ADD COLUMN `memo` TEXT NULL COMMENT '备注' AFTER `underwrite_at`;

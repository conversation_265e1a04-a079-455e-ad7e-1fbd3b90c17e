CREATE TABLE `brokerage_trial_application`
(
    `id`            BIGINT AUTO_INCREMENT PRIMARY KEY,
    `contact_name`  VARCHAR(255) NOT NULL COMMENT '联系人姓名',
    `contact_email` VARCHAR(255) NOT NULL COMMENT '联系人邮箱',
    `contact_phone` VARCHAR(20)  NOT NULL COMMENT '联系人电话',
    `name`          VARCHAR(255) NOT NULL COMMENT '经纪行名称',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX `idx_name_unique` (`name`),
    UNIQUE INDEX `idx_name_contact_email_unique` (`name`, `contact_email`)
) ENGINE=InnoDB COMMENT='经纪行试用申请表（包含一小部分经纪行和系统管理员信息）';

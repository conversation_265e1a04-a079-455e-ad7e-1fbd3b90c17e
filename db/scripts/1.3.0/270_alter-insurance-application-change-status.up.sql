-- change brokage application PENDING_QUOTE to QUOTING
UPDATE `insurance_application`
SET `status` = 'QUOTING'
WHERE `status` = 'PENDING_QUOTE' and `brokerage_id` IS NOT NULL;

-- change brokage application PENDING to PENDING_QUOTE
UPDATE `insurance_application`
SET `status` = 'PENDING_QUOTE'
WHERE `status` = 'PENDING' and `brokerage_id` IS NOT NULL;

-- change regular application PENDING to PENDING_UNDERWRITTEN
UPDATE `insurance_application`
SET `status` = 'PENDING_UNDERWRITTEN'
WHERE `status` = 'PENDING' and `brokerage_id` IS NULL;

-- create policy from application with status PENDING_LEAD_PAYMENT
INSERT INTO
    `insurance_policy` (`application_id`, `broker_id`, `brokerage_id`, `customer_id`, `customer_name`, `customer_gender`, `email`, `phone`, `province`, `city`, `address`, `postal_code`, `premium`, `start_date`, `end_date`, `insurance_type`, `source_type`)
SELECT
    `id` AS `application_id`, `broker_id`, `brokerage_id`, `customer_id`, `customer_name`, `customer_gender`, `email`, `phone`, `province`,`city`,`address`, `postal_code`, `premium`, `start_date`, `end_date`, `insurance_type`, "ONLINE"
FROM
    `insurance_application`
WHERE
    `status` = 'PENDING_LEAD_PAYMENT';

-- change application status PENDING_LEAD_PAYMENT to UNDERWRITTEN
UPDATE `insurance_application`
SET `status` = 'UNDERWRITTEN'
WHERE `status` = 'PENDING_LEAD_PAYMENT';


ALTER TABLE `insurance_application`
MODIFY COLUMN `status` ENUM(
    'PENDING_QUOTE',
    'QUOTING',
    'PENDING_UNDERWRITTEN',
    'UNDERWRITTEN'
) NOT NULL COMMENT '状态:待算费、算费中、待承保、已承保';

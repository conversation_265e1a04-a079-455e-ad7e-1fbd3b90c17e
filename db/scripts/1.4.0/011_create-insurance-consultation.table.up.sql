CREATE TABLE `insurance_consultation` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `broker_id` BIGINT UNSIGNED NOT NULL COMMENT '经纪人ID',
    `source` VARCHAR(100) NULL COMMENT '咨询来源',
    `province` VARCHAR(50) NOT NULL COMMENT '省份',
    `city` VARCHAR(50) NOT NULL COMMENT '城市',
    `name` VARCHAR(100) NOT NULL COMMENT '客户姓名',
    `email` VARCHAR(100) COMMENT '电子邮箱',
    `phone` VARCHAR(20) NOT NULL COMMENT '电话号码',
    `postal_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '邮编',
    `address`     VARCHAR(500)   NOT NULL COMMENT '客户地址(包括门牌号、街道名称、单元号等)',
    `remark` TEXT COMMENT '备注信息',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保险咨询表';

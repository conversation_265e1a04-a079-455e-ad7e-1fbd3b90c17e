CREATE TABLE `insurance_consultation_customer` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `insurance_consultation` BIGINT NOT NULL COMMENT '咨询ID',
  `customer_id` BIGINT NOT NULL COMMENT '客户ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线索-保险申请关联表';
;

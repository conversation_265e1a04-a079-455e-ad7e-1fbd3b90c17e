ALTER TABLE `broker`
MODIFY COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE', 'AUTO_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `lead`
MODIFY COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE', 'AUTO_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `insurance_application`
MODIFY COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE', 'AUTO_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `insurance_policy`
MODIFY COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE', 'AUTO_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `reminder_message`
MODIFY COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE', 'AUTO_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

ALTER TABLE `broker_lead_fee`
MODIFY COLUMN `insurance_type` ENUM('HOUSE_INSURANCE', 'RENTERS_INSURANCE', 'AUTO_INSURANCE') NOT NULL DEFAULT 'HOUSE_INSURANCE' COMMENT '保险类型';

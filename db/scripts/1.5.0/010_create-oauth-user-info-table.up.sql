CREATE TABLE `oauth_user_info` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
    `provider` ENUM('GOOGLE', 'APPLE') NOT NULL COMMENT '提供商',
    `open_id` VARCHAR(255) NOT NULL COMMENT '提供商用户ID',
    `refresh_token` VARCHAR(255) NOT NULL COMMENT '刷新令牌',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `open_id_provider_user_id` (`open_id`, `provider`, `user_id`),
    INDEX `user_id_idx` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OAuth用户信息表';

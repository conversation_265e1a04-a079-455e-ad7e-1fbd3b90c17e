UPDATE broker
SET province = 'BC'
where id <= 4;

UPDATE broker
SET province = 'AB'
where id > 4;




INSERT INTO broker_profile (id,broker_id, public_fields)
VALUES (4,4, 'NAME|EMAIL|PHONE');


INSERT INTO broker_qualification (broker_profile_id, is_qualified)
VALUES (4, 1);

INSERT INTO broker_lead_config (id, broker_id, allow_leads, created_at, updated_at, willing_pay_for_leads,
                                insurance_type)
VALUES (4, 4, 1, '2025-07-10 10:18:48', '2025-07-10 10:18:48', 1, 'HOUSE_INSURANCE');

INSERT INTO broker_lead_fee (id, broker_id, insurance_type, referral_fee_type, referral_fee_value,
                             created_at, updated_at)
VALUES (6, 4, 'HOUSE_INSURANCE', 'PREMIUM_PERCENTAGE', 1.0000, '2025-07-10 18:24:34', '2025-07-10 10:24:36');



INSERT INTO broker_profile (id,broker_id, public_fields)
VALUES (5,3, 'NAME|EMAIL|PHONE');


INSERT INTO broker_qualification (broker_profile_id, is_qualified)
VALUES (5, 1);

INSERT INTO broker_lead_config (id, broker_id, allow_leads, created_at, updated_at, willing_pay_for_leads,
                                insurance_type)
VALUES (5, 3, 1, '2025-07-10 10:18:48', '2025-07-10 10:18:48', 1, 'HOUSE_INSURANCE');

INSERT INTO broker_lead_fee (id, broker_id, insurance_type, referral_fee_type, referral_fee_value,
                             created_at, updated_at)
VALUES (7, 3, 'HOUSE_INSURANCE', 'PREMIUM_PERCENTAGE', 1.0000, '2025-07-10 18:24:34', '2025-07-10 10:24:36');

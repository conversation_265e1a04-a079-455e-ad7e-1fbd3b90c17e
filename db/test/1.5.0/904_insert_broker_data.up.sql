
INSERT INTO `user` (
    `id`, `email`, `password`, `name`, `user_type`, `status`, `referer_id`,
    `avatar`, `created_at`, `updated_at`
) VALUES
(1000, '<EMAIL>', '$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W',
 'John Do<PERSON> Test Broker', 'SAAS', 'ACTIVE', NULL,
 NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),

(1001, '<EMAIL>', '$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W',
 '<PERSON>', 'SAAS', 'ACTIVE', 1000,
 NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(1002, '<EMAIL>', '$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W',
 '<PERSON> <PERSON> Referer', 'SAAS', 'ACTIVE', 1000,
 NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(1003, '<EMAIL>', '$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W',
 'Alice Brown Referer', 'SAAS', 'ACTIVE', 1000,
 NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),

(1004, '<EMAIL>', '$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W',
 'Charlie Wilson Lonely', 'SAAS', 'ACTIVE', NULL,
 NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00');

INSERT INTO `user_role` (
    `id`, `user_id`, `role_id`, `created_at`, `updated_at`
) VALUES
(1000, 1000, 2, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(1001, 1001, 2, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(1002, 1002, 2, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(1003, 1003, 2, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(1004, 1004, 2, '2024-01-01 00:00:00', '2024-01-01 00:00:00');


INSERT INTO `broker` (
    `id`, `user_id`, `uid`, `name`, `gender`, `address`, `province`, `city`,
    `postal_code`, `phone`, `insurance_company`, `description`, `insurance_type`, `brokerage_id`,
    `created_at`, `updated_at`
) VALUES
(100, 1000, 'BR-TEST100', 'John Doe Test Broker', 'MALE', '123 Test Main St',
 'BC', 'Vancouver', 'V6B 1A1', '555-1234', 'ABC Test Insurance',
 'Test broker for unit testing', 'HOUSE_INSURANCE', NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),

(101, 1001, 'BR-REF101', 'Jane Smith Referer', 'FEMALE', '456 Referer Ave',
 'BC', 'Vancouver', 'V6B 2B2', '555-2001', 'XYZ Insurance',
 'Referer broker 1', 'HOUSE_INSURANCE', NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(102, 1002, 'BR-REF102', 'Bob Johnson Referer', 'MALE', '789 Referer Blvd',
 'BC', 'Vancouver', 'V6B 3C3', '555-2002', 'DEF Insurance',
 'Referer broker 2', 'HOUSE_INSURANCE', NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(103, 1003, 'BR-REF103', 'Alice Brown Referer', 'FEMALE', '321 Referer St',
 'BC', 'Vancouver', 'V6B 4D4', '555-2003', 'GHI Insurance',
 'Referer broker 3', 'HOUSE_INSURANCE', NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),

(104, 1004, 'BR-LONELY104', 'Charlie Wilson Lonely', 'MALE', '999 Lonely Lane',
 'ON', 'Toronto', 'M5V 1A1', '************', 'Solo Insurance',
 'Lonely broker with no referers', 'HOUSE_INSURANCE', NULL, '2024-01-01 00:00:00', '2024-01-01 00:00:00');


INSERT INTO `broker_profile` (
    `id`, `broker_id`, `public_fields`, `created_at`, `updated_at`
) VALUES
(100, 100, 'NAME|EMAIL|PHONE', '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(101, 101, 'NAME|EMAIL|PHONE', '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(102, 102, 'NAME|EMAIL|PHONE', '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(103, 103, 'NAME|EMAIL|PHONE', '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(104, 104, 'NAME|EMAIL|PHONE', '2024-01-01 00:00:00', '2024-01-01 00:00:00');


INSERT INTO `broker_qualification` (
    `id`, `broker_profile_id`, `is_qualified`, `created_at`, `updated_at`
) VALUES
(100, 100, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(101, 101, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(102, 102, 0, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(103, 103, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(104, 104, 0, '2024-01-01 00:00:00', '2024-01-01 00:00:00');


INSERT INTO `broker_payment_method` (
    `id`, `broker_id`, `account_type`, `account_number`, `is_default`,
    `created_at`, `updated_at`
) VALUES
(100, 100, 'E_TRANSFER', '**********', 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(101, 101, 'E_TRANSFER', '**********', 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(102, 102, 'E_TRANSFER', '**********', 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(103, 103, 'E_TRANSFER', '**********', 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(104, 104, 'E_TRANSFER', '**********', 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00');

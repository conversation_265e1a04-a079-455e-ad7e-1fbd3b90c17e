name: comintern_bethune_ve
channels:
  - https://mirrors.bfsu.edu.cn/anaconda/pkgs/main
  - https://mirrors.bfsu.edu.cn/anaconda/pkgs/free
  - https://mirrors.bfsu.edu.cn/anaconda/pkgs/r
  - https://mirrors.bfsu.edu.cn/anaconda/pkgs/pro
  - https://mirrors.bfsu.edu.cn/anaconda/pkgs/msys2
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/auto
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/biobakery
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/bioconda
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/c4aarch64
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/caffe2
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/conda-forge
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/deepmodeling
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/dglteam
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/fastai
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/fermi
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/idaholab
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/intel
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/matsci
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/menpo
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/mordred-descriptor
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/msys2
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/numba
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/ohmeta
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/omnia
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/peterjc123
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/plotly
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/psi4
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/pytorch
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/pytorch-test
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/pytorch3d
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/pyviz
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/qiime2
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/rapidsai
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/rdkit
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/simpleitk
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/stackless
  - https://mirrors.bfsu.edu.cn/anaconda/cloud/ursky
  - defaults
dependencies:
  - python==3.13.5
  - poetry==2.1.3

[project]
name = "bethune"
version = "0.5.0"
description = "The admin backend for CA home insurance project"
readme = "README.md"
requires-python = ">=3.13,<4.0"
keywords = ["insurance", "broker", "house"]
authors = [{ name = "ca team" }]
dependencies = [
  "APScheduler>=3.11.0",
  "aiofiles>=24.1.0",
  "aiosmtplib>=4.0.0",
  "bcrypt>=4.3.0",
  "cryptography>=45.0.5",
  "dogpile-cache>=1.3.4",
  "fastapi[standard]>=0.116.1",
  "fastapi-babel>=1.0.0",
  "fastapi-health>=0.4.0",
  "jinja2>=3.1.6",
  "loguru>=0.7.3",
  "markupsafe>=3.0.2",
  "openpyxl>=3.1.5",
  "passlib>=1.7.4",
  "pretty-errors>=1.2.25",
  "pydantic>=2.11.7",
  "pydantic-settings>=2.10.1",
  "pyjwt>=2.10.1",
  "pymysql>=1.1.1",
  "python-dotenv>=1.1.1",
  "redis[hiredis]>=6.2.0",
  "sqlalchemy>=2.0.41",
  "sqlmodel>=0.0.24",
  "starlette-context>=0.4.0",
  "tenacity>=9.1.2",
  "transitions (>=0.9.3,<0.10.0)",
  "google-api-python-client (>=2.176.0,<3.0.0)",
  "google-auth (>=2.40.3,<3.0.0)",
  "google-auth-oauthlib (>=1.2.2,<2.0.0)",
  "google-auth-httplib2 (>=0.2.0,<0.3.0)"
]

[tool.poetry.group.test]
optional = true
[tool.poetry.group.test.dependencies]
pytest = ">=8.3.5"
pytest-asyncio = ">=0.26.0"
pytest-mock = ">=3.14.0"
pytest-repeat = ">=0.9.4"
testcontainers = ">=4.10.0"
pytest-cov = "^6.2.1"

[tool.poetry.group.check]
optional = true
[tool.poetry.group.check.dependencies]
pre-commit = ">=4.1.0"

[tool.poetry.group.misc]
optional = true
[tool.poetry.group.misc.dependencies]
polib = ">=1.2.0"

[project.urls]
homepage = "http://cabroker.ca/"
documentation = "http://cabroker.ca/docs"
repository = "http://192.168.1.213/comintern/bethune"
changelog = "http://192.168.1.213/comintern/bethune/-/blob/dev/CHANGELOG.md"

[[tool.poetry.source]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "primary"

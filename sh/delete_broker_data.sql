SET @broker_ids := (
    SELECT GROUP_CONCAT(b.id)
    FROM `broker` b
    JOIN `user` u ON b.user_id = u.id
    WHERE u.email IN (
        '<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>'
    )
);


-- broker_qualification
DELETE FROM `broker_qualification` WHERE broker_profile_id IN (
	SELECT id FROM `broker_profile` WHERE FIND_IN_SET(broker_id, @broker_ids)=0
);

-- broker_profile
DELETE FROM `broker_profile` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- broker_lead_config
DELETE FROM `broker_lead_config` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- broker_lead_fee
DELETE FROM `broker_lead_fee` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- broker_payment_method
DELETE FROM `broker_payment_method` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- insurance_application
DELETE FROM `insurance_application` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- insurance_policy
DELETE FROM `insurance_policy` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- lead_referral_fee_payment
DELETE from `lead_referral_fee_payment` WHERE lead_id IN
(
	SELECT id FROM `lead` WHERE FIND_IN_SET(created_by, @broker_ids)=0
);

-- lead_application
DELETE FROM `lead_application` WHERE lead_id IN
(
	SELECT id FROM `lead` WHERE FIND_IN_SET(created_by, @broker_ids)=0
);

-- lead
DELETE FROM `lead` WHERE FIND_IN_SET(created_by, @broker_ids)=0;

-- reminder_config
DELETE FROM `reminder_config` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- reminder_message
DELETE FROM `reminder_message` WHERE FIND_IN_SET(receiver_id, @broker_ids)=0;

-- customer
DELETE FROM `customer` WHERE FIND_IN_SET(broker_id, @broker_ids)=0;

-- broker
DELETE FROM `broker` WHERE FIND_IN_SET(id, @broker_ids)=0;

SET @user_ids := (
    SELECT GROUP_CONCAT(id)
    FROM `user`
    WHERE email IN (
		'<EMAIL>',
        '<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>'
    )
);

-- user_role
DELETE FROM `user_role` WHERE FIND_IN_SET(user_id, @user_ids)=0;

-- user_feedback
DELETE FROM `user_feedback` WHERE FIND_IN_SET(user_id, @user_ids)=0;

-- user
DELETE FROM `user` WHERE FIND_IN_SET(id, @user_ids)=0;

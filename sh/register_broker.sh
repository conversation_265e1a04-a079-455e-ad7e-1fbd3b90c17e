# 1. 关闭注册验证码
# 2. 更改生产环境curl地址，执行以下curl脚本

curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "tianxin.alan",
  "address": "",
  "province": "",
  "city": "",
  "postal_code": "",
  "phone": "",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": true
}'


curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "kenny",
  "address": "123 Main St, San Francisco, CA 94122",
  "province": "",
  "city": "",
  "postal_code": "",
  "phone": "************",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": true
}'


curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Gavin",
  "address": "7080 River Rd",
  "province": "BC",
  "city": "RI",
  "postal_code": "",
  "phone": "6043699551",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": false
}'

curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Laila",
  "address": "7688 Alderbridge way",
  "province": "BC",
  "city": "RI",
  "postal_code": "V6X0P7",
  "phone": "7788651366",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": false
}'


curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Boris wang",
  "address": "17171 fedoruk road",
  "province": "BC",
  "city": "RI",
  "postal_code": "V6V 1c6",
  "phone": "7782239275",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": false
}'


curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Scot Pan",
  "address": "7080 river road",
  "province": "BC",
  "city": "RI",
  "postal_code": "v7e5l4",
  "phone": "7786978330",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": false
}'

curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "liulai",
  "address": "7688 AlderBridge way",
  "gender": "FEMALE",
  "province": "BC",
  "city": "RI",
  "postal_code": "V6X 0P7",
  "phone": "*************",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": true
}'

curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Boris wang",
  "address": "17171 FEDORUK RD",
  "province": "BC",
  "city": "RI",
  "postal_code": "V6V 1C6",
  "phone": "7782239275",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": true
}'


curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Tina",
  "address": "4710 Kingsway",
  "province": "BC",
  "city": "BU",
  "postal_code": "V5H4M2",
  "phone": "*************",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": false
}'

curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Ben McCarthy",
  "address": "123 Collins Street, Melbourne VIC 3000, Australia",
  "gender": "MALE",
  "province": "BC",
  "city": "VI",
  "postal_code": "M5V 3L9",
  "phone": "*************",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": false
}'


curl -X 'POST' \
  'https://cabroker.ca/admin/api/v1/insurance/broker/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "Sophie Evans",
  "address": "Melbourne CBD",
  "gender": "MALE",
  "province": "BC",
  "city": "VI",
  "postal_code": "",
  "phone": "******-555-4321",
  "description": "",
  "email": "<EMAIL>",
  "password": "123456",
  "verification_code": "1234",
  "is_qualified": false
}'

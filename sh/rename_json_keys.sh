#!/bin/bash

# 检查目录参数是否存在
if [ $# -ne 1 ]; then
  echo "用法: $0 <目标目录>"
  exit 1
fi

target_dir="$1"

# 检查目录是否存在
if [ ! -d "$target_dir" ]; then
  echo "错误: 目录 '$target_dir' 不存在"
  exit 1
fi

# 查找并处理所有JSON文件
find "$target_dir" -type f -name "*.json" | while read -r file; do
  echo "处理文件: $file"

  # 使用jq进行键名替换并创建临时文件
  jq '(
    def rename_key:
      if type == "object" then
        with_entries(
          .key as $k
          | .value |= rename_key
          | if $k == "house_insurance_application" then .key = "insurance_application" else . end
        )
      elif type == "array" then
        map(rename_key)
      else
        .
      end;
    rename_key
  )' "$file" > "${file}.tmp" && \
  mv -f "${file}.tmp" "$file"

  # 检查jq执行状态
  if [ $? -ne 0 ]; then
    echo "错误: 处理文件 $file 失败，已跳过"
    rm -f "${file}.tmp" 2>/dev/null
  fi
done

echo "处理完成！"

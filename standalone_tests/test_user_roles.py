import pytest
from unittest.mock import MagicMock

# 测试常量
TEST_USER_ID = 123
REQUIRED_SCOPE = "system:user:role:view"

class TestGetUserRoles:
    """测试获取用户角色的接口"""

    def test_get_user_roles_success(self):
        """测试成功获取用户角色"""
        # 1. 模拟测试客户端
        test_client = MagicMock()
        
        # 2. 模拟响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 1000,
            "data": {
                "total": 2,
                "items": [
                    {"id": 1, "name": "Admin"},
                    {"id": 2, "name": "Editor"}
                ]
            }
        }
        test_client.get.return_value = mock_response
        
        # 3. 执行请求
        response = test_client.get(f"/api/v1/system/user/{TEST_USER_ID}/role")
        
        # 4. 断言结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == 1000
        assert len(response_data["data"]["items"]) == 2
        assert response_data["data"]["items"][0]["id"] == 1

    @pytest.mark.parametrize("expected_status", [
        200,  # 成功
        403,  # 权限不足
        401,  # 未认证
    ])
    def test_different_status_codes(self, expected_status):
        """测试不同的状态码"""
        # 1. 模拟测试客户端
        test_client = MagicMock()
        
        # 2. 模拟响应
        mock_response = MagicMock()
        mock_response.status_code = expected_status
        
        if expected_status == 200:
            mock_response.json.return_value = {
                "code": 1000,
                "data": {"total": 1, "items": [{"id": 1}]}
            }
        elif expected_status == 403:
            mock_response.json.return_value = {
                "code": 4003,
                "message": "Permission denied"
            }
        else:  # 401
            mock_response.json.return_value = {
                "code": 4001,
                "message": "Not authenticated"
            }
        
        test_client.get.return_value = mock_response
        
        # 3. 执行请求
        response = test_client.get(f"/api/v1/system/user/{TEST_USER_ID}/role")
        
        # 4. 断言结果
        assert response.status_code == expected_status

    def test_get_user_roles_empty_list(self):
        """测试用户没有角色的情况"""
        # 1. 模拟测试客户端
        test_client = MagicMock()
        
        # 2. 模拟响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 1000,
            "data": {
                "total": 0,
                "items": []
            }
        }
        test_client.get.return_value = mock_response
        
        # 3. 执行请求
        response = test_client.get(f"/api/v1/system/user/{TEST_USER_ID}/role")
        
        # 4. 断言结果
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == 1000
        assert response_data["data"]["total"] == 0
        assert len(response_data["data"]["items"]) == 0

@pytest.fixture(autouse=True, scope="function")
def mock_ref_code_env_prefix():
    from bethune.settings import settings
    from unittest.mock import patch
    mocked_settings = {"REF_CODE_ENV_PREFIX": ""}

    original_getattribute = type(settings).__getattribute__

    def mock_getattribute(self, name):
        if name in mocked_settings:
            return mocked_settings[name]
        return original_getattribute(self, name)

    patcher = patch.object(type(settings), "__getattribute__", mock_getattribute)
    patcher.start()

    def set_settings(settings_dict=None):
        if settings_dict is None:
            return mocked_settings
        mocked_settings.update(settings_dict)
        return mocked_settings

    yield set_settings
    patcher.stop() 
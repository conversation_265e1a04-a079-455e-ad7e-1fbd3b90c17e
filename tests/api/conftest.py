import uuid
from datetime import datetime as dt
from datetime import timedelta
from datetime import timezone
from unittest.mock import patch

import jwt
from fastapi.testclient import TestClient
from pytest import fixture

from bethune import app
from bethune import create_db_session_per_request
from bethune.db import session as db_session
from bethune.db.session_context import create_db_session
from bethune.model import User
from bethune.model import UserStatus
from bethune.model import UserType
from bethune.settings import settings
from bethune.util import concat_path


def _generate_mock_token(user_email: str, username: str) -> str:
    """
    生成 Mock JWT Token。
    """
    _now = dt.now(timezone.utc)
    payload = {
        "sub": user_email,
        "username": username,
        "iat": _now,
        "exp": _now + timedelta(minutes=settings.AUTH_ACCESS_TOKEN_EXPIRE_MINUTES),
        "jti": str(uuid.uuid4()),
    }
    return jwt.encode(payload, settings.AUTH_SECRET_KEY, algorithm=settings.AUTH_ALGORITHM)


async def rollback_only_db_session(session_maker):
    for session in create_db_session(session_maker=session_maker, rollback_only=True):
        yield session


@fixture(scope="session")
def test_client():
    app.dependency_overrides[create_db_session_per_request] = lambda: db_session()
    with TestClient(app) as client:
        yield client


@fixture(scope="session")
def api_prefix():
    return concat_path(settings.ROOT_PATH, settings.API_PREFIX, settings.API_VERSION)


@fixture(scope="session")
def mock_user():
    """
    提供一个模拟用户的字典
    """
    return {
        "user_email": "<EMAIL>",
        "username": "testbroker@total_insured.com",
        "password": "Password!23",
        "hashed_password": "$2b$12$ykPAquudjF/mxuQFhVdAmOQNyqzhn9O2Ueu70bqZz.v2ELJJmO67W",
    }


@fixture(scope="session")
def auth_headers(mock_user):
    """
    提供测试用的 Authorization Header
    """
    token = _generate_mock_token(
        user_email="testbroker@total_insured.com",
        username=mock_user["username"],
    )
    return {"Authorization": f"Bearer {token}"}


@fixture(scope="session")
def auth_headers_generator():
    """
    生成动态的 Authorization Header。
    """

    def _generate_auth_headers(user_email: str):
        token = _generate_mock_token(user_email=user_email, username="test user")
        return {"Authorization": f"Bearer {token}"}

    return _generate_auth_headers


@fixture(scope="function")
def mock_user_allowed_scopes(request):
    """
    动态设置 user_allowed_scopes 的 fixture。
    """
    scopes = request.param if hasattr(request, "param") else {"default:scope"}

    with patch("bethune.api.dependencies.authorization.user_allowed_scopes") as mock_scopes:
        mock_scopes.return_value = scopes
        yield mock_scopes


@fixture(scope="function")
def mock_auth_get_current_user():
    with patch("bethune.service.system.auth.AuthService.get_current_user") as mock_get_current_user:
        mock_get_current_user.return_value = User(
            id=1,
            email="<EMAIL>",
            user_type=UserType.SYSTEM,
            status=UserStatus.ACTIVE,
        )
        yield mock_get_current_user

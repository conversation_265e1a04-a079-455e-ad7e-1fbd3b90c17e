from pydantic import BaseModel

from bethune.api.dto import BaseResponse
from bethune.api.dto import BusinessErrorResponse
from bethune.error import NotFoundError


class SimpleUser(BaseModel):
    name: str
    age: int


def test_base_response_success():
    response = BaseResponse[str].ok("hello")
    assert response.code == 1000
    assert response.message == "success"
    assert response.data == "hello"

    response = BaseResponse[dict].ok({"hello": "world"})
    assert response.data == {"hello": "world"}

    response = BaseResponse[list].ok(["hello", "world"])
    assert response.data == ["hello", "world"]

    response = BaseResponse[int].ok(1)
    assert response.data == 1

    response = BaseResponse[float].ok(1.0)
    assert response.data == 1.0

    response = BaseResponse[SimpleUser].ok(SimpleUser(name="Alan Tam", age=25))
    assert response.data.name == "Alan Tam"
    assert response.data.age == 25


def test_base_response_fail():
    error = NotFoundError("user not found", detail={"user_id": 1})
    response = BusinessErrorResponse.fail(error)
    assert response.message == error.message
    assert response.code == error.code
    assert response.data == error.detail
    assert response.metadata == error.metadata

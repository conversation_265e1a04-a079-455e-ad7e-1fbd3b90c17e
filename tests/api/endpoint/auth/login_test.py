import pytest
from fastapi.testclient import Test<PERSON>lient

from bethune.api.dto.base import LoginType<PERSON><PERSON>


def _create_login_data(username: str, password: str, login_type: LoginTypeEnum) -> dict:
    return {
        "username": username,
        "password": password,
        "login_type": login_type.value,
    }


@pytest.mark.parametrize(
    "username,password,login_type",
    [
        ("<EMAIL>", "Password!23", LoginTypeEnum.SYSTEM),
        ("<EMAIL>", "123456", LoginTypeEnum.SYSTEM),
        ("<EMAIL>", "123456", LoginTypeEnum.MOBILE),
    ],
)
def test_login_success(test_client: TestClient, api_prefix, username, password, login_type):
    response = test_client.post(
        f"{api_prefix}/auth/login",
        json=_create_login_data(username, password, login_type),
    )

    assert response.json()["code"] == 1000


def test_login_field(test_client: TestClient, api_prefix):
    # 执行登录请求
    response = test_client.post(
        f"{api_prefix}/auth/login",
        json=_create_login_data("<EMAIL>", "123456", LoginTypeEnum.SYSTEM),
    )

    json_response = response.json()
    assert json_response["code"] == 4040
    assert json_response["message"] == "Invalid username or password"

import uuid
from http import HTTPStatus

import pytest
from fastapi.testclient import Test<PERSON><PERSON>


def test_create_trial_application_success(test_client: TestClient, api_prefix: str):
    request_data = {
        "name": "Test Brokerage",
        "contact_name": "Test Contact",
        "contact_email": "<EMAIL>",
        "contact_phone": "1234567890",
        "verification_code": "123456",
    }
    response = test_client.post(f"{api_prefix}/trial_application", json=request_data)
    response_json = response.json()
    assert response_json["code"] == 1000


def test_create_trial_application_duplicate_name(test_client: TestClient, api_prefix: str):
    unique_id = str(uuid.uuid4())[:8]
    first_application = {
        "name": f"Duplicate Brokerage {unique_id}",
        "contact_name": "First Contact",
        "contact_email": f"first_{unique_id}@example.com",
        "contact_phone": "1234567890",
        "verification_code": "123456",
    }
    first_response = test_client.post(f"{api_prefix}/trial_application", json=first_application)
    assert first_response.status_code == HTTPStatus.OK
    second_application = {
        "name": f"Duplicate Brokerage {unique_id}",
        "contact_name": "Second Contact",
        "contact_email": f"second_{unique_id}@example.com",
        "contact_phone": "9876543210",
        "verification_code": "123456",
    }
    second_response = test_client.post(f"{api_prefix}/trial_application", json=second_application)
    response_json = second_response.json()
    assert response_json["code"] == 4030
    assert "both name and contact email are unique" == response_json["message"]


def test_create_company_success(test_client: TestClient, api_prefix: str, auth_headers_generator):
    headers = auth_headers_generator("<EMAIL>")
    request_data = {
        "name": "Test Company Inc",
        "province": "BC",
        "city": "Vancouver",
        "address": "123 Test Street",
        "postal_code": "V6B 1A1",
        "contact_name": "John Smith",
        "contact_email": "<EMAIL>",
        "contact_phone": "6041234567",
        "logo": "https://example.com/logo.png",
        "website": "https://testcompany.com",
        "description": "A test company for automated testing",
    }
    response = test_client.post(f"{api_prefix}", json=request_data, headers=headers)
    response_json = response.json()
    assert response_json["code"] == 1000
    assert response_json["data"]["name"] == request_data["name"]


def test_create_company_samename_field(test_client: TestClient, api_prefix: str, auth_headers_generator):
    headers = auth_headers_generator("<EMAIL>")
    company_name = f"Test Company Inc {uuid.uuid4().hex[:8]}"
    request_data = {
        "name": company_name,
        "province": "BC",
        "city": "Vancouver",
        "address": "123 Test Street",
        "postal_code": "V6B 1A1",
        "contact_name": "John Smith",
        "contact_email": "<EMAIL>",
        "contact_phone": "6041234567",
        "logo": "https://example.com/logo.png",
        "website": "https://testcompany.com",
        "description": "A test company for automated testing",
    }
    first_response = test_client.post(f"{api_prefix}", json=request_data, headers=headers)
    first_response_json = first_response.json()
    assert first_response_json["code"] == 1000
    assert first_response_json["data"]["name"] == company_name
    duplicate_request_data = request_data.copy()
    duplicate_request_data["contact_email"] = f"different_{uuid.uuid4().hex[:8]}@example.com"
    duplicate_request_data["contact_phone"] = "7781234567"
    second_response = test_client.post(f"{api_prefix}", json=duplicate_request_data, headers=headers)
    second_response_json = second_response.json()
    assert second_response_json["code"] == 4030
    assert "both name and contact email are unique" == second_response_json["message"]


def test_get_brokerage_by_id_success(test_client: TestClient, api_prefix: str, auth_headers_generator):
    headers = auth_headers_generator("<EMAIL>")
    company_name = f"Test Brokerage {uuid.uuid4().hex[:8]}"
    create_data = {
        "name": company_name,
        "province": "BC",
        "city": "Vancouver",
        "address": "123 Test Street",
        "postal_code": "V6B 1A1",
        "contact_name": "John Smith",
        "contact_email": f"john.{uuid.uuid4().hex[:8]}@example.com",
        "contact_phone": "6041234567",
        "website": "https://testbrokerage.com",
        "description": "A test brokerage company",
    }
    create_response = test_client.post(f"{api_prefix}", json=create_data, headers=headers)
    created_company = create_response.json()["data"]
    company_id = created_company["id"]
    get_response = test_client.get(f"{api_prefix}/{company_id}", headers=headers)
    response_json = get_response.json()
    returned_company = response_json["data"]
    assert response_json["code"] == 1000
    assert returned_company["id"] == company_id
    assert returned_company["name"] == company_name
    assert returned_company["province"] == create_data["province"]
    assert returned_company["city"] == create_data["city"]


@pytest.mark.parametrize(
    "brokerage_id, expected_status, expected_code, expected_message",
    [
        (*********, HTTPStatus.NOT_FOUND, 4010, "Brokerage not found"),
        ("", HTTPStatus.METHOD_NOT_ALLOWED, 4010, "Brokerage not found"),
        (
            "null",
            HTTPStatus.UNPROCESSABLE_CONTENT,
            4030,
            "Data validation error, detail info: Field: ('path', 'id'), Message: Input should be a valid integer, unable to parse string as an integer, Type: int_parsing",
        ),
    ],
    ids=[
        "nonexistent_id",
        "empty_string",
        "null_string",
    ],
)
def test_get_nonexistent_brokerage(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    brokerage_id,
    expected_status,
    expected_code,
    expected_message,
):
    headers = auth_headers_generator("<EMAIL>")
    response = test_client.get(f"{api_prefix}/{brokerage_id}", headers=headers)
    response_json = response.json()
    assert response.status_code == expected_status
    if expected_status != HTTPStatus.METHOD_NOT_ALLOWED:
        assert response_json["code"] == expected_code
        assert expected_message == response_json["message"]

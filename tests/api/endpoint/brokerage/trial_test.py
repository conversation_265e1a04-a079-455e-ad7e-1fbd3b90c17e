from fastapi.testclient import TestClient


def test_get_trial_application_list(test_client: TestClient, api_prefix: str, auth_headers_generator):
    headers = auth_headers_generator("<EMAIL>")
    response = test_client.get(f"{api_prefix}/get_trial_application_list", headers=headers)
    response_json = response.json()
    assert response_json["code"] == 1000
    assert response_json["data"][0]["contact_name"] == "Test Contact 1"
    assert response_json["data"][0]["contact_email"] == "<EMAIL>"
    assert response_json["data"][0]["contact_phone"] == "1234567890"
    assert response_json["data"][0]["name"] == "Test Trial Brokerage SQL 1"


def test_convert_trial_user_role_is_admin(test_client: TestClient, api_prefix: str, auth_headers_generator):
    headers = auth_headers_generator("<EMAIL>")
    response = test_client.post(
        f"{api_prefix}/create_company_and_admin", json={"trial_application_id": 1}, headers=headers
    )
    response_json = response.json()
    assert response_json["data"]["brokerage_user"]["user_role"] == "ADMIN"


def test_convert_trial_duplicate_email(test_client: TestClient, api_prefix: str, auth_headers_generator):
    headers = auth_headers_generator("<EMAIL>")
    response = test_client.post(
        f"{api_prefix}/create_company_and_admin", json={"trial_application_id": 1}, headers=headers
    )
    assert response.json()["code"] == 1000
    response1 = test_client.post(
        f"{api_prefix}/create_company_and_admin", json={"trial_application_id": 1}, headers=headers
    )
    assert response1.json()["code"] == 4030


def test_verify_permissions(test_client: TestClient, api_prefix: str, auth_headers_generator):
    headers = auth_headers_generator("<EMAIL>")
    response = test_client.post(
        f"{api_prefix}/create_company_and_admin", json={"trial_application_id": 1}, headers=headers
    )
    assert response.json()["code"] == 4050

from unittest import mock

import pytest


# 模拟请求和响应类，避免导入项目复杂依赖
class MockEmailRequest:
    def __init__(self, email: str):
        self.email = email


class MockResponse:
    def __init__(self, code: int, data: str):
        self.code = code
        self.data = data


# 模拟发送验证邮件的函数
async def mock_send_verification_email(core_sc, verification_request):
    """模拟发送验证邮件的函数"""
    await core_sc.verification_code_service.send_verification_email(str(verification_request.email))
    return MockResponse(code=1000, data=verification_request.email)


@pytest.fixture
def mock_verification_code_service():
    """Mock验证码服务"""
    mock_service = mock.AsyncMock()
    mock_service.send_verification_email.return_value = "<EMAIL>"
    return mock_service


@pytest.fixture
def mock_core_service_context(mock_verification_code_service):
    """Mock核心服务上下文"""
    mock_context = mock.MagicMock()
    mock_context.verification_code_service = mock_verification_code_service
    return mock_context


@pytest.mark.asyncio
async def test_system_auth_verification_code(mock_core_service_context):
    """测试系统认证验证码发送"""
    email = "<EMAIL>"
    verification_request = MockEmailRequest(email=email)

    # 执行测试
    response = await mock_send_verification_email(mock_core_service_context, verification_request)

    # 验证结果
    assert response.code == 1000
    assert response.data == email

    # 验证mock被正确调用
    mock_core_service_context.verification_code_service.send_verification_email.assert_called_once_with(email)


@pytest.mark.asyncio
async def test_email_service_mock():
    """测试邮件服务Mock"""
    # 创建Mock邮件服务
    mock_email_service = mock.AsyncMock()
    mock_email_service.send_verification_code_email.return_value = None

    # 测试发送验证码邮件
    email = "<EMAIL>"
    verification_code = "123456"

    await mock_email_service.send_verification_code_email(email, verification_code)

    # 验证调用
    mock_email_service.send_verification_code_email.assert_called_once_with(email, verification_code)


@pytest.mark.asyncio
async def test_redis_mock():
    """测试Redis Mock"""
    # 创建Mock Redis
    mock_redis = mock.AsyncMock()
    mock_redis.setex.return_value = None
    mock_redis.get.return_value = b"123456"

    # 测试Redis操作
    email = "<EMAIL>"
    key = f"bethune:core:verification_code:{email}"

    # 设置验证码
    await mock_redis.setex(key, 600, "123456")
    mock_redis.setex.assert_called_once_with(key, 600, "123456")

    # 获取验证码
    result = await mock_redis.get(key)
    assert result == b"123456"


def test_email_validation():
    """测试邮件地址验证"""
    valid_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

    for email in valid_emails:
        assert "@" in email
        assert "." in email.split("@")[1]


def test_verification_code_generation():
    """测试验证码生成"""
    import random
    import string

    def generate_code(length=6):
        return "".join(random.choices(string.digits, k=length))

    code = generate_code(6)
    assert len(code) == 6
    assert code.isdigit()

import pytest


@pytest.mark.parametrize(
    "include_federal,first_item_code,first_item_description,lang",
    [
        (False, "BC", "British Columbia", "zh"),
        (True, "FED", "Federal", "zh_SG"),
        (True, "FED", "Federal", "en"),
    ],
    ids=[
        "test get provinces without federal, zh",
        "test get provinces with federal, zh_SG",
        "test get provinces with federal, en",
    ],
)
def test_get_all_provinces(test_client, api_prefix, include_federal, first_item_code, first_item_description, lang):
    response = test_client.get(
        f"{api_prefix}/province",
        params={"include_federal": include_federal},
        headers={"Accept-Language": lang},
    )
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert response.json()["data"][0]["code"] == first_item_code
    assert response.json()["data"][0]["description"] == first_item_description

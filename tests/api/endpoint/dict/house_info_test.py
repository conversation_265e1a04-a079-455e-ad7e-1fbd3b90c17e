import pytest


@pytest.mark.parametrize(
    "selected_categories,categories,check_category,first_item_description_in_category,lang",
    [
        (
            "",
            {
                "house_type",
                "occupancy_status",
                "primary_heating_source",
                "wiring_type",
                "roofing_material",
                "voltage",
                "plumbing_pipe_type",
                "building_material",
                "structure",
                "structure2",
                "sum_insured",
                "heating_fuel",
                "security_alarm",
                "general_year_with_none_option",
                "general_quantity",
                "general_year",
            },
            "house_type",
            "永久产权独立屋",
            "zh",
        ),
        ("house_type,occupancy_status", {"house_type", "occupancy_status"}, "house_type", "永久产权独立屋", "zh_CN"),
        (
            "house_type,occupancy_status",
            {"house_type", "occupancy_status"},
            "house_type",
            "Freehold Detached House",
            "en",
        ),
    ],
    ids=[
        "test get all house info, zh",
        "test get selected house info, zh_CN",
        "test get selected house info, en",
    ],
)
def test_get_house_info(
    test_client, api_prefix, selected_categories, categories, check_category, first_item_description_in_category, lang
):
    response = test_client.get(
        f"{api_prefix}/house-info",
        params={"selected_categories": selected_categories},
        headers={"Accept-Language": lang},
    )
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert set(response.json()["data"].keys()) == categories
    assert response.json()["data"][check_category][0]["description"] == first_item_description_in_category

import pytest


@pytest.mark.parametrize(
    "categories,check_category,first_item_description_in_category,lang",
    [
        (
            {
                "life_type",
                "driver_proficiency",
                "driver_license_type",
                "driver_license_status",
                "driver_relationship",
                "vehicle_ownership_status",
                "vehicle_yearly_mileage",
            },
            "life_type",
            "家庭理财规划",
            "zh",
        ),
        (
            {
                "life_type",
                "driver_proficiency",
                "driver_license_type",
                "driver_license_status",
                "driver_relationship",
                "vehicle_ownership_status",
                "vehicle_yearly_mileage",
            },
            "life_type",
            "Planification Financière Familiale",
            "fr",
        ),
        (
            {
                "life_type",
                "driver_proficiency",
                "driver_license_type",
                "driver_license_status",
                "driver_relationship",
                "vehicle_ownership_status",
                "vehicle_yearly_mileage",
            },
            "life_type",
            "Family Financial Planning",
            "en",
        ),
    ],
    ids=[
        "test_get_all_insurance_info_zh",
        "test_get_all_insurance_info_zh_CN",
        "test_get_all_insurance_info_en",
    ],
)
def test_get_insurance_info(
    test_client, api_prefix, categories, check_category, first_item_description_in_category, lang
):
    response = test_client.get(
        f"{api_prefix}/insurance-info",
        headers={"Accept-Language": lang},
    )
    assert response.json()["code"] == 1000
    assert response.json()["data"][check_category][0]["description"] == first_item_description_in_category

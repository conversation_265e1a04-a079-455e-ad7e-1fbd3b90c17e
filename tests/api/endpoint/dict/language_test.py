import pytest


@pytest.mark.parametrize(
    "first_item_code",
    [
        "zh",
    ],
    ids=[
        "test get all supported languages",
    ],
)
def test_get_all_supported_languages(test_client, api_prefix, first_item_code):
    response = test_client.get(f"{api_prefix}/supported-language")
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert response.json()["data"][0]["code"] == first_item_code

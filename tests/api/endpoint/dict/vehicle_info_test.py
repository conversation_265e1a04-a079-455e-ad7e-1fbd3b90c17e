import pytest


@pytest.mark.parametrize(
    "selected_categories,categories,check_category,first_item_description_in_category,lang",
    [
        (
            "",
            {
                "driver_proficiency",
                "driver_license_type",
                "driver_license_status",
                "driver_relationship",
                "vehicle_ownership_status",
                "vehicle_yearly_mileage",
            },
            "driver_proficiency",
            "是的，作为主要驾驶员",
            "zh",
        ),
        (
            "driver_proficiency,driver_license_type",
            {"driver_proficiency", "driver_license_type"},
            "driver_proficiency",
            "是的，作为主要驾驶员",
            "zh_CN",
        ),
        (
            "driver_proficiency,driver_license_type",
            {"driver_proficiency", "driver_license_type"},
            "driver_proficiency",
            "Yes, as a main driver",
            "en",
        ),
    ],
    ids=[
        "test get all vehicle info, zh",
        "test get selected vehicle info, zh_CN",
        "test get selected vehicle info, en",
    ],
)
def test_get_vehicle_info(
    test_client, api_prefix, selected_categories, categories, check_category, first_item_description_in_category, lang
):
    response = test_client.get(
        f"{api_prefix}/vehicle-info",
        params={"selected_categories": selected_categories},
        headers={"Accept-Language": lang},
    )
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert set(response.json()["data"].keys()) == categories
    assert response.json()["data"][check_category][0]["description"] == first_item_description_in_category

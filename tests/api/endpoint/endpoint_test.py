import pytest
from fastapi import Response
from fastapi.testclient import TestClient
from loguru import logger


class BaseEndpointTester:

    @pytest.fixture(autouse=True, scope="function")
    def _setup(self):
        self.test_client: TestClient = None
        self.current_user = None
        self.api_prefix = ""
        self.auth_headers_generator = None

    def _do_basic_assertions_and_return_data(self, response) -> dict:
        assert response.status_code == 200, "Response status code should be 200"
        assert response.json()["code"] == 1000, "Response code should be 1000"
        assert "data" in response.json(), "Response should contain 'data' field"
        return response.json()["data"]

    def _get(
        self, endpoint: str, params: dict[str, str] | None = None, do_basic_assertions: bool = True
    ) -> dict | Response:
        response = self.test_client.get(
            f"{self.api_prefix}{endpoint}",
            headers=self.auth_headers_generator(self.current_user.user.email),
            params=params,
        )
        logger.info(response.json())
        if do_basic_assertions:
            return self._do_basic_assertions_and_return_data(response)
        return response

    def _post(self, endpoint: str, json_data: dict | None = None, do_basic_assertions: bool = True) -> Response | dict:
        if json_data is None:
            json_data = {}
        response = self.test_client.post(
            f"{self.api_prefix}{endpoint}",
            headers=self.auth_headers_generator(self.current_user.user.email),
            json=json_data,
        )
        logger.info(response.json())
        if do_basic_assertions:
            return self._do_basic_assertions_and_return_data(response)
        return response

    def _put(self, endpoint: str, json_data: dict, do_basic_assertions: bool = True) -> Response | dict:
        response = self.test_client.put(
            f"{self.api_prefix}{endpoint}",
            headers=self.auth_headers_generator(self.current_user.user.email),
            json=json_data,
        )
        logger.info(response.json())
        if do_basic_assertions:
            return self._do_basic_assertions_and_return_data(response)
        return response

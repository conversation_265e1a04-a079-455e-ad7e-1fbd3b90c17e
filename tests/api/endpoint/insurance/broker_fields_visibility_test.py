import pytest
from fastapi.testclient import TestClient

from bethune.api.dto.base import InsuranceType


@pytest.fixture
def broker_api_prefix(api_prefix):
    return f"{api_prefix}/insurance/broker"


@pytest.fixture
def lead_api_prefix(api_prefix):
    return f"{api_prefix}/insurance/lead"


def _create_config(public_fields):
    return {
        "allow_leads": True,
        "insurance_type": [InsuranceType.HOUSE_INSURANCE],
        "willing_pay_for_leads": True,
        "broker_profile": {"public_fields": public_fields},
        "lead_fees": [
            {
                "insurance_type": InsuranceType.HOUSE_INSURANCE,
                "referral_fee_type": "PREMIUM_PERCENTAGE",
                "referral_fee_value": 0.01,
            }
        ],
    }


def _get_query_params():
    return {
        "insurance_type": InsuranceType.HOUSE_INSURANCE,
        "customer_province": "BC",
        "customer_city": "Vancouver",
        "page_no": 1,
        "page_size": 10,
    }


def _update_broker_config(test_client, broker_api_prefix, broker_headers, public_fields):
    config_data = _create_config(public_fields)
    response = test_client.put(f"{broker_api_prefix}/lead-config", json=config_data, headers=broker_headers)
    assert response.json()["code"] == 1000


def _get_broker_id(test_client, broker_api_prefix, broker_headers):
    response = test_client.get(f"{broker_api_prefix}/lead-config", headers=broker_headers)
    assert response.status_code == 200
    return response.json()["data"]["broker_id"]


def _get_target_broker(test_client, lead_api_prefix, referral_headers, broker_id):
    response = test_client.get(
        f"{lead_api_prefix}/candidate_broker_assignee",
        params=_get_query_params(),
        headers=referral_headers,
    )
    assert response.json()["code"] == 1000
    return next((broker for broker in response.json()["data"]["items"] if broker["id"] == broker_id), None)


def _verify_field_visibility(broker, field, should_be_visible):
    if should_be_visible:
        assert broker[field] != "***"
    else:
        assert broker[field] == "***"


@pytest.mark.parametrize(
    "public_fields,expected_visibility",
    [
        (["NAME", "EMAIL", "PHONE"], {"name": True, "email": True, "phone": True}),
        (["NAME", "EMAIL"], {"name": True, "email": True, "phone": False}),
        (["NAME"], {"name": True, "email": False, "phone": False}),
        ([], {"name": False, "email": False, "phone": False}),
    ],
    ids=["all_fields_visible", "name_email_visible_only", "name_only_visible", "no_fields_visible"],
)
def test_broker_field_visibility_configuration(
    test_client: TestClient,
    broker_api_prefix: str,
    lead_api_prefix: str,
    auth_headers_generator,
    public_fields: list,
    expected_visibility: dict,
):
    broker_headers = auth_headers_generator("<EMAIL>")
    referral_headers = auth_headers_generator("<EMAIL>")

    _update_broker_config(test_client, broker_api_prefix, broker_headers, public_fields)
    target_broker = _get_target_broker(
        test_client, lead_api_prefix, referral_headers, _get_broker_id(test_client, broker_api_prefix, broker_headers)
    )

    assert target_broker

    for field, should_be_visible in expected_visibility.items():
        _verify_field_visibility(target_broker, field, should_be_visible)

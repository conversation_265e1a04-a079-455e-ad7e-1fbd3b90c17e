import json
from unittest.mock import AsyncMock

import pytest
from fastapi.testclient import TestClient

from bethune.api.dto.base import GenderEnum
from bethune.api.dto.broker import BrokerCreate
from bethune.logging import logger


@pytest.fixture
def api_prefix(api_prefix):
    return f"{api_prefix}/insurance/broker"


def test_register_broker(test_client: TestClient, api_prefix: str):
    broker_create = BrokerCreate(
        name="Test Broker",
        phone="+86 13612345678",
        gender=GenderEnum.MALE,
        email="<EMAIL>",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=True,
        description="Test description for the broker.",
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Test Broker"
    assert response.json()["data"]["uid"] is not None
    assert response.json()["data"]["id"] is not None
    assert response.json()["data"]["description"] == "Test description for the broker."


def test_register_broker_without_avatar(test_client: TestClient, api_prefix: str):
    broker_create = BrokerCreate(
        name="Test Broker No Avatar",
        phone="+86 13612345679",
        gender=GenderEnum.FEMALE,
        email="<EMAIL>",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=True,
        description="Test broker without avatar.",
        # avatar=None  # 不设置avatar字段
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Test Broker No Avatar"
    assert response.json()["data"]["id"]


def test_register_broker_with_avatar_url(test_client: TestClient, api_prefix: str):
    broker_create = BrokerCreate(
        name="Test Broker With Avatar",
        phone="+86 13612345680",
        gender=GenderEnum.MALE,
        email="<EMAIL>",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=True,
        description="Test broker with avatar URL.",
        avatar="https://example.com/uploads/avatars/test-broker-avatar.jpg",
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Test Broker With Avatar"
    assert response.json()["data"]["id"]


def test_register_broker_with_referer(test_client: TestClient, api_prefix: str, test_broker):
    broker_create = BrokerCreate(
        name="Test Broker With Referer",
        phone="+86 13612345681",
        gender=GenderEnum.FEMALE,
        email="<EMAIL>",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=True,
        description="Test broker with referer.",
        referer_id=test_broker.id,
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Test Broker With Referer"
    assert response.json()["data"]["id"]


def test_register_broker_without_referer(test_client: TestClient, api_prefix: str):
    broker_create = BrokerCreate(
        name="Test Broker No Referer",
        phone="+86 13612345682",
        gender=GenderEnum.MALE,
        email="<EMAIL>",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=True,
        description="Test broker without referer.",
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Test Broker No Referer"
    assert response.json()["data"]["id"]


@pytest.mark.parametrize(
    "is_qualified,expected_qualified",
    [
        (True, True),
        (False, False),
    ],
)
def test_register_broker_role_assignment(test_client: TestClient, api_prefix: str, is_qualified, expected_qualified):
    broker_create = BrokerCreate(
        name=f"Test Broker {'Qualified' if is_qualified else 'Referral'}",
        phone=f"+86 1361234568{3 if is_qualified else 4}",
        gender=GenderEnum.MALE,
        email=f"test_{'qualified' if is_qualified else 'referral'}@testinsurance.com",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=is_qualified,
        description=f"Test {'qualified' if is_qualified else 'referral'} broker role assignment.",
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == f"Test Broker {'Qualified' if is_qualified else 'Referral'}"
    assert response.json()["data"]["id"]


def test_register_broker_invalid_referer_id(test_client: TestClient, api_prefix: str):
    broker_create = BrokerCreate(
        name="Test Invalid Referer Broker",
        phone="+86 13612345687",
        gender=GenderEnum.FEMALE,
        email="<EMAIL>",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=True,
        description="Test invalid referer ID error.",
        referer_id=99999,  # 不存在的referer_id
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 404
    assert "not found" in response.json()["message"].lower()


@pytest.fixture
def mock_redis_oauth_data(mocker):

    mock_redis = mocker.patch("bethune.api.endpoint.insurance.broker.broker.get_redis")
    mock_redis_instance = AsyncMock()
    mock_redis.return_value = mock_redis_instance

    oauth_data = {
        "id": None,
        "user_id": 0,  # 新用户时为0
        "provider": "GOOGLE",
        "open_id": "google_123456",
        "refresh_token": "mock_refresh_token",
        "expires_at": "2024-12-31T23:59:59+00:00",
        "created_at": "2024-01-01T00:00:00+00:00",
        "updated_at": "2024-01-01T00:00:00+00:00",
    }

    mock_redis_instance.get.return_value = json.dumps(oauth_data).encode("utf-8")

    mock_verification_service = mocker.patch(
        "bethune.service.core.verification_code.VerificationCodeService.delete_verification_code"
    )
    mock_verification_service.return_value = AsyncMock()

    return mock_redis_instance


def test_register_broker_with_oauth(test_client: TestClient, api_prefix: str, mock_redis_oauth_data):
    request_data = {
        "name": "Test OAuth Broker",
        "phone": "+86 13612345688",
        "gender": "MALE",
        "email": "<EMAIL>",
        "province": "bc",
        "city": "vancouver",
        "address": "123 Test St",
        "postal_code": "V1V 1V1",
        "is_qualified": True,
        "description": "Test OAuth registration.",
        "oauth_id": "test_oauth_id_123",
    }

    response = test_client.post(
        f"{api_prefix}/register",
        json=request_data,
    )

    assert response.status_code == 200
    assert "access_token" in response.json()["data"]
    assert "token_type" in response.json()["data"]
    assert response.json()["data"]["token_type"] == "Bearer"


def test_register_broker_oauth_not_found(test_client: TestClient, api_prefix: str, mocker):
    mock_redis = mocker.patch("bethune.api.endpoint.insurance.broker.broker.get_redis")
    mock_redis_instance = AsyncMock()
    mock_redis.return_value = mock_redis_instance
    mock_redis_instance.get.return_value = None

    request_data = {
        "name": "Test OAuth Not Found Broker",
        "phone": "+86 13612345689",
        "gender": "FEMALE",
        "email": "<EMAIL>",
        "province": "bc",
        "city": "vancouver",
        "address": "123 Test St",
        "postal_code": "V1V 1V1",
        "is_qualified": True,
        "description": "Test OAuth ID not found error.",
        "oauth_id": "invalid_oauth_id",
    }

    response = test_client.post(
        f"{api_prefix}/register",
        json=request_data,
    )

    assert response.status_code == 404
    assert "oauth" in response.json()["message"].lower()


def test_register_broker_comprehensive_scenario(test_client: TestClient, api_prefix: str, test_broker):
    broker_create = BrokerCreate(
        name="Test Comprehensive Broker",
        phone="+86 13612345690",
        gender=GenderEnum.FEMALE,
        email="<EMAIL>",
        password="testpassword123",
        verification_code="123456",
        province="bc",
        city="vancouver",
        address="123 Test St",
        postal_code="V1V 1V1",
        is_qualified=True,
        description="Test comprehensive registration scenario.",
        avatar="https://example.com/uploads/avatars/comprehensive-test.jpg",
        referer_id=test_broker.id,
    )
    response = test_client.post(
        f"{api_prefix}/register",
        json=broker_create.model_dump(exclude_none=True, exclude_unset=True),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Test Comprehensive Broker"
    assert response.json()["data"]["id"]
    assert response.json()["data"]["description"] == "Test comprehensive registration scenario."


def test_broker_business_card(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    response = test_client.get(
        f"{api_prefix}/{test_broker.uid}/business_card",
        headers=auth_headers_generator(test_broker.user.email),
    )

    logger.info(response.json())

    assert response.status_code == 200
    assert response.json()["data"]["name"] == test_broker.name
    assert response.json()["data"]["uid"] == test_broker.uid
    assert response.json()["data"]["description"] == test_broker.description


def test_broker_business_card_in_brokerage(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_brokerage_broker,
):
    broker = test_brokerage_broker
    response = test_client.get(
        f"{api_prefix}/{broker.uid}/business_card",
        headers=auth_headers_generator(broker.user.email),
    )

    logger.info(response.json())

    assert response.status_code == 200
    assert response.json()["data"]["name"] == broker.name
    assert response.json()["data"]["uid"] == broker.uid
    assert response.json()["data"]["description"] == broker.description
    assert response.json()["data"]["brokerage"]["name"] == broker.brokerage.name


def test_update_broker_with_avatar(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Broker Name", "avatar": "https://example.com/uploads/avatars/test-avatar.jpg"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Broker Name"


def test_update_broker_without_avatar(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Broker Name"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Broker Name"


def test_update_broker_with_payment_method(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Broker Name", "payment_method": {"account_number": "**********"}}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Broker Name"


def test_update_broker_without_payment_method(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Broker Name"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Broker Name"


def test_update_broker_with_qualification_true(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Broker Name", "is_qualified": True}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Broker Name"


def test_update_broker_with_qualification_false(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Broker Name", "is_qualified": False}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Broker Name"


def test_update_broker_without_qualification(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Broker Name"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Broker Name"


def test_update_brokerage_broker_with_name_and_phone(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_brokerage_broker,
):
    update_data = {"name": "Updated Brokerage Broker Name", "phone": "+86 ***********"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_brokerage_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Brokerage Broker Name"
    assert response.json()["data"]["phone"] == "+86 ***********"


def test_update_brokerage_broker_with_name_only(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_brokerage_broker,
):
    update_data = {"name": "Updated Brokerage Broker Name"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_brokerage_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Brokerage Broker Name"


def test_update_brokerage_broker_with_phone_only(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_brokerage_broker,
):
    update_data = {"phone": "+86 ***********"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_brokerage_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["phone"] == "+86 ***********"


def test_update_brokerage_broker_without_name_and_phone(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_brokerage_broker,
):
    update_data = {"description": "Updated description only"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_brokerage_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["description"] == "Updated description only"


def test_update_regular_broker_without_brokerage(
    # 复合分支测试
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    update_data = {"name": "Updated Regular Broker Name", "phone": "+86 ***********"}
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == "Updated Regular Broker Name"
    assert response.json()["data"]["phone"] == "+86 ***********"


@pytest.mark.parametrize(
    "broker_type,update_data,expected_name,expected_description",
    [
        (
            "regular",
            {
                "name": "Updated Broker Name",
                "avatar": "https://example.com/uploads/avatars/test-avatar.jpg",
                "payment_method": {"account_number": "**********"},
                "is_qualified": True,
                "description": "Updated description with all fields",
            },
            "Updated Broker Name",
            "Updated description with all fields",
        ),
        (
            "brokerage",
            {
                "name": "Complete Updated Brokerage Broker",
                "phone": "+86 ***********",
                "avatar": "https://example.com/uploads/avatars/brokerage-avatar.jpg",
                "payment_method": {"account_number": "****************"},
                "is_qualified": True,
                "description": "Complete update with all fields",
                "address": "Updated Address",
                "province": "Updated Province",
                "city": "Updated City",
            },
            "Complete Updated Brokerage Broker",
            "Complete update with all fields",
        ),
    ],
)
def test_update_broker_with_multiple_fields(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    test_brokerage_broker,
    broker_type,
    update_data,
    expected_name,
    expected_description,
):
    broker = test_brokerage_broker if broker_type == "brokerage" else test_broker
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == expected_name
    assert response.json()["data"]["description"] == expected_description


@pytest.mark.parametrize(
    "test_case,update_data,expected_name",
    [
        ("empty_avatar", {"name": "Updated Broker Name", "avatar": ""}, "Updated Broker Name"),
        ("null_qualification", {"name": "Updated Broker Name", "is_qualified": None}, "Updated Broker Name"),
        (
            "empty_payment_account",
            {"name": "Updated Broker Name", "payment_method": {"account_number": ""}},
            "Updated Broker Name",
        ),
        ("minimal_data", {}, None),
    ],
)
def test_update_broker_boundary_conditions(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    test_case,
    update_data,
    expected_name,
):
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    if expected_name:
        assert response.json()["data"]["name"] == expected_name


@pytest.mark.parametrize(
    "initial_qualified,final_qualified,final_name",
    [
        (True, False, "Updated Broker Name Again"),
        (False, True, "Updated Broker Name Again"),
    ],
)
def test_broker_role_change(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    initial_qualified,
    final_qualified,
    final_name,
):
    initial_update = {"name": "Updated Broker Name", "is_qualified": initial_qualified}
    response = test_client.put(
        f"{api_prefix}/me",
        json=initial_update,
        headers=auth_headers_generator(test_broker.user.email),
    )
    assert response.status_code == 200

    final_update = {"name": final_name, "is_qualified": final_qualified}
    response = test_client.put(
        f"{api_prefix}/me",
        json=final_update,
        headers=auth_headers_generator(test_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["name"] == final_name


@pytest.mark.parametrize(
    "update_data,expected_description",
    [
        (
            {"name": "", "phone": "", "description": "Updated description with empty name and phone"},
            "Updated description with empty name and phone",
        ),
    ],
)
def test_update_brokerage_broker_boundary_conditions(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_brokerage_broker,
    update_data,
    expected_description,
):
    response = test_client.put(
        f"{api_prefix}/me",
        json=update_data,
        headers=auth_headers_generator(test_brokerage_broker.user.email),
    )

    assert response.status_code == 200
    assert response.json()["data"]["description"] == expected_description


def test_get_lead_config_success(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    headers = auth_headers_generator(test_broker.user.email)
    response = test_client.get(f"{api_prefix}/lead-config", headers=headers)

    assert response.status_code == 200
    assert response.json()["data"]["broker_id"] == test_broker.id


def test_get_lead_config_with_lead_fees(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    headers = auth_headers_generator(test_broker.user.email)
    response = test_client.get(f"{api_prefix}/lead-config", headers=headers)

    assert response.status_code == 200
    data = response.json()["data"]
    assert data["broker_id"] == test_broker.id
    assert "lead_fees" in data
    assert "broker_profile" in data


def test_get_lead_config_unauthorized(
    test_client: TestClient,
    api_prefix: str,
):
    response = test_client.get(f"{api_prefix}/lead-config")

    assert response.status_code == 401


@pytest.mark.parametrize(
    "update_data,expected_allow_leads,expected_willing_pay",
    [
        (
            {"allow_leads": True, "willing_pay_for_leads": True},
            True,
            True,
        ),
        (
            {"allow_leads": False, "willing_pay_for_leads": False},
            False,
            False,
        ),
        (
            {"allow_leads": True},
            True,
            None,
        ),
        (
            {"willing_pay_for_leads": False},
            None,
            False,
        ),
    ],
)
def test_update_lead_config_basic_fields(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    update_data,
    expected_allow_leads,
    expected_willing_pay,
):
    headers = auth_headers_generator(test_broker.user.email)
    response = test_client.put(f"{api_prefix}/lead-config", json=update_data, headers=headers)

    assert response.status_code == 200
    data = response.json()["data"]
    assert data["broker_id"] == test_broker.id
    if expected_allow_leads is not None:
        assert data["allow_leads"] == expected_allow_leads
    if expected_willing_pay is not None:
        assert data["willing_pay_for_leads"] == expected_willing_pay


@pytest.mark.parametrize(
    "insurance_types,expected_count",
    [
        (["HOUSE_INSURANCE"], 1),
        (["HOUSE_INSURANCE", "RENTERS_INSURANCE"], 2),
        (["HOUSE_INSURANCE", "AUTO_INSURANCE", "LIFE_INSURANCE"], 3),
        (["RENTERS_INSURANCE"], 1),
    ],
)
def test_update_lead_config_insurance_types(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    insurance_types,
    expected_count,
):
    headers = auth_headers_generator(test_broker.user.email)
    update_data = {"insurance_type": insurance_types}
    response = test_client.put(f"{api_prefix}/lead-config", json=update_data, headers=headers)

    assert response.status_code == 200
    data = response.json()["data"]
    assert len(data["insurance_type"]) == expected_count
    for insurance_type in insurance_types:
        assert insurance_type in data["insurance_type"]


@pytest.mark.parametrize(
    "public_fields,expected_fields",
    [
        (["NAME"], ["NAME"]),
        (["NAME", "EMAIL"], ["NAME", "EMAIL"]),
        (["NAME", "EMAIL", "PHONE"], ["NAME", "EMAIL", "PHONE"]),
        ([], []),
    ],
)
def test_update_lead_config_with_broker_profile(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    public_fields,
    expected_fields,
):
    headers = auth_headers_generator(test_broker.user.email)
    update_data = {"broker_profile": {"public_fields": public_fields}}
    response = test_client.put(f"{api_prefix}/lead-config", json=update_data, headers=headers)

    assert response.status_code == 200
    data = response.json()["data"]
    assert data["broker_profile"]["public_fields"] == expected_fields


@pytest.mark.parametrize(
    "lead_fees_data,expected_count",
    [
        (
            [
                {
                    "insurance_type": "HOUSE_INSURANCE",
                    "referral_fee_type": "PREMIUM_PERCENTAGE",
                    "referral_fee_value": 0.05,
                }
            ],
            1,
        ),
        (
            [
                {"insurance_type": "HOUSE_INSURANCE", "referral_fee_type": "FIXED", "referral_fee_value": 100.0},
                {
                    "insurance_type": "RENTERS_INSURANCE",
                    "referral_fee_type": "PREMIUM_PERCENTAGE",
                    "referral_fee_value": 0.03,
                },
            ],
            2,
        ),
        (
            [
                {"insurance_type": "AUTO_INSURANCE", "referral_fee_type": "FIXED", "referral_fee_value": 200.0},
                {
                    "insurance_type": "LIFE_INSURANCE",
                    "referral_fee_type": "PREMIUM_PERCENTAGE",
                    "referral_fee_value": 0.02,
                },
            ],
            2,
        ),
    ],
)
def test_update_lead_config_with_lead_fees(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    lead_fees_data,
    expected_count,
):
    headers = auth_headers_generator(test_broker.user.email)
    update_data = {"lead_fees": lead_fees_data}
    response = test_client.put(f"{api_prefix}/lead-config", json=update_data, headers=headers)

    assert response.status_code == 200
    data = response.json()["data"]
    assert len(data["lead_fees"]) == expected_count
    for i, expected_fee in enumerate(lead_fees_data):
        actual_fee = data["lead_fees"][i]
        assert actual_fee["insurance_type"] == expected_fee["insurance_type"]
        assert actual_fee["referral_fee_type"] == expected_fee["referral_fee_type"]
        assert actual_fee["referral_fee_value"] == expected_fee["referral_fee_value"]


def test_update_lead_config_comprehensive_scenario(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
):
    headers = auth_headers_generator(test_broker.user.email)
    update_data = {
        "allow_leads": True,
        "willing_pay_for_leads": True,
        "insurance_type": ["HOUSE_INSURANCE", "AUTO_INSURANCE"],
        "broker_profile": {"public_fields": ["NAME", "EMAIL", "PHONE"]},
        "lead_fees": [
            {
                "insurance_type": "HOUSE_INSURANCE",
                "referral_fee_type": "PREMIUM_PERCENTAGE",
                "referral_fee_value": 0.05,
            },
            {"insurance_type": "AUTO_INSURANCE", "referral_fee_type": "FIXED", "referral_fee_value": 150.0},
        ],
    }
    response = test_client.put(f"{api_prefix}/lead-config", json=update_data, headers=headers)

    assert response.status_code == 200
    data = response.json()["data"]
    assert data["broker_id"] == test_broker.id
    assert len(data["insurance_type"]) == 2
    assert "HOUSE_INSURANCE" in data["insurance_type"]
    assert "AUTO_INSURANCE" in data["insurance_type"]
    assert data["broker_profile"]["public_fields"] == ["NAME", "EMAIL", "PHONE"]
    assert len(data["lead_fees"]) == 2


@pytest.mark.parametrize(
    "update_data,test_description",
    [
        ({}, "Empty update data"),
        ({"allow_leads": None}, "Null allow_leads"),
        ({"willing_pay_for_leads": None}, "Null willing_pay_for_leads"),
        ({"insurance_type": []}, "Empty insurance_type list"),
        ({"broker_profile": {"public_fields": []}}, "Empty public_fields"),
        ({"lead_fees": []}, "Empty lead_fees list"),
    ],
)
def test_update_lead_config_boundary_conditions(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
    test_broker,
    update_data,
    test_description,
):
    headers = auth_headers_generator(test_broker.user.email)
    response = test_client.put(f"{api_prefix}/lead-config", json=update_data, headers=headers)

    assert response.status_code == 200
    assert response.json()["data"]["broker_id"] == test_broker.id


def test_update_lead_config_unauthorized(
    test_client: TestClient,
    api_prefix: str,
):
    update_data = {"allow_leads": True}
    response = test_client.put(f"{api_prefix}/lead-config", json=update_data)

    assert response.status_code == 401

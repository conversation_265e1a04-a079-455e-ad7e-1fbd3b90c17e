from datetime import date
from decimal import Decimal

import pytest

from bethune.model.base import GenderEnum
from bethune.model.base import InsuranceType
from bethune.model.broker import Broker
from bethune.model.broker import BrokerLeadConfig
from bethune.model.broker import BrokerLeadFee
from bethune.model.broker import BrokerPaymentMethod
from bethune.model.broker import BrokerProfile
from bethune.model.broker import BrokerQualification
from bethune.model.brokerage import Brokerage
from bethune.model.brokerage_user import BrokerageUser
from bethune.model.customer import Customer
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.model.insurance import InsurancePolicy
from bethune.model.insurance import InsurancePolicySourceType
from bethune.model.lead import Lead
from bethune.model.lead import LeadApplication
from bethune.model.lead import LeadStatusEnum
from bethune.model.payment import AccountTypeEnum
from bethune.model.system import ROLE_BROKER_SUPPORT_ID
from bethune.model.system import UserRole
from bethune.service.insurance.insurance_application import <PERSON><PERSON>N<PERSON><PERSON><PERSON>
from bethune.settings import settings


@pytest.fixture(autouse=True)
def patch_data_dir(tmp_path):
    test_data_dir = tmp_path / "data"
    test_data_dir.mkdir(parents=True, exist_ok=True)
    (test_data_dir / settings.JSON_DATA_FOLDER / "house-insurance").mkdir(parents=True, exist_ok=True)
    original_value = settings.__dict__.get("DATA_DIR")
    settings.__dict__["DATA_DIR"] = str(test_data_dir)
    yield
    settings.__dict__["DATA_DIR"] = original_value


@pytest.fixture(autouse=True)
def patch_auth_skip_verification():

    original_value = settings.__dict__.get("AUTH_SKIP_VERIFICATION")
    settings.__dict__["AUTH_SKIP_VERIFICATION"] = True
    yield
    settings.__dict__["AUTH_SKIP_VERIFICATION"] = original_value


@pytest.fixture(scope="function")
def test_brokerage(create_model):
    """
    Fixture to create a test brokerage.
    """
    return create_model(
        Brokerage(
            name="Test Brokerage",
            contact_name="John Doe",
            contact_phone="+86 13612345678",
            contact_email="<EMAIL>",
            province="BC",
            city="Vancouver",
            address="123 Test St, Vancouver, BC",
            postal_code="A1A 2A2",
            description="Test brokerage for insurance applications.",
            website="https://www.testbrokerage.com",
        )
    )


@pytest.fixture(scope="function")
def test_broker(create_model, create_user):
    user = create_user(email="<EMAIL>")
    broker = create_model(
        Broker(
            user_id=user.id,
            name="Test Broker",
            phone="+86 13612341234",
            gender=GenderEnum.MALE,
            uid="BR-ABCD5678X",
            description="Test broker for insurance applications.",
        )
    )
    create_model(
        UserRole(
            user_id=user.id,
            role_id=2,  # 2: Broker Role ID
        )
    )
    broker_profile = BrokerProfile(
        broker_id=broker.id,
        public_fields="EMAIL|PHONE|NAME",
    )
    broker_profile = create_model(broker_profile)
    create_model(BrokerQualification(broker_profile_id=broker_profile.id, is_qualified=True))  # type: ignore
    create_model(
        BrokerLeadConfig(
            broker_id=broker.id,
            allow_leads=True,
            willing_pay_for_leads=True,
            insurance_type=InsuranceType.HOUSE_INSURANCE,
        )
    )
    create_model(
        BrokerLeadFee(
            broker_id=broker.id,
            referral_fee_value=0.01,
            insurance_type=InsuranceType.HOUSE_INSURANCE,
        ),
    )
    create_model(
        BrokerLeadFee(
            broker_id=broker.id,
            referral_fee_value=0.02,
            insurance_type=InsuranceType.RENTERS_INSURANCE,
        )
    )
    create_model(
        BrokerPaymentMethod(
            broker_id=broker.id,
            account_type=AccountTypeEnum.E_TRANSFER,
            account_number="**********",
            is_default=True,
        )
    )
    return broker


@pytest.fixture(scope="function")
def test_referral_broker(create_model, create_user):
    """
    Fixture to create a referral fee payment for the test broker.
    """
    user = create_user(email="<EMAIL>")
    broker = create_model(
        Broker(
            user_id=user.id,
            name="Referral Broker",
            phone="+86 ***********",
            gender=GenderEnum.MALE,
            uid="BR-ABCD1234X",
            description="Referral broker for testing purposes.",
        )
    )
    create_model(
        UserRole(
            user_id=user.id,
            role_id=3,  # 2: Referral Broker Role ID
        )
    )
    broker_profile = create_model(
        BrokerProfile(
            broker_id=broker.id,
            public_fields="EMAIL|PHONE|NAME",
        )
    )
    create_model(BrokerQualification(broker_profile_id=broker_profile.id, is_qualified=False))  # type: ignore
    create_model(
        BrokerPaymentMethod(
            broker_id=broker.id,
            account_type=AccountTypeEnum.E_TRANSFER,
            account_number="**********",
            is_default=True,
        )
    )
    return broker


@pytest.fixture(scope="function")
def test_customer(
    test_broker,
    create_model,
):
    """
    Fixture to create a test customer.
    """
    return create_model(
        Customer(
            broker_id=test_broker.id,
            name="王五",
            province="BC",
            city="Vancouver",
            address="123 Market St",
            postal_code="A1A 2A2",
            gender=GenderEnum.MALE,
            phone="+86 ***********",
            email="<EMAIL>",
            birthday=date(1991, 1, 1),
            move_in_date=date(2020, 1, 1),
        )
    )


@pytest.fixture(scope="function")
def test_brokerage_broker(
    create_model,
    update_model,
    test_broker,
    test_brokerage,
):
    """
    Fixture to create a test broker with an associated brokerage.
    """
    test_broker.brokerage_id = test_brokerage.id

    create_model(
        BrokerageUser(
            user_id=test_broker.user_id,
            brokerage_id=test_brokerage.id,
            name=test_broker.name,
            phone=test_broker.phone,
        )
    )

    return update_model(test_broker)


@pytest.fixture(scope="function")
def insurance_application_json_data():
    return {
        "insurance_type": "HOUSE_INSURANCE",
        "applicant_info": {
            "gender": "MALE",
            "birthday": "1991-01-01",
            "phone": "+86 ***********",
            "email": "<EMAIL>",
            "name": "王五",
        },
        "insurance_info": {
            "house_type": "FDH",
            "expected_start_date": "2025-06-14",
            "shippingSame": True,
            "province": "BC",
            "city": "Vancouver",
            "address": "123 Market St",
            "postal_code": "A1A 2A2",
        },
        "house_info": {
            "occupancy_status": "OC",
            "fireSystem": False,
            "fireAlarm": "FC",
            "theftAlarm": "FC",
        },
        "underwriting_info": {
            "amount": "20000",
            "floorIop": "20000",
            "smoker": False,
            "homeBusiness": False,
            "otherPeopleLiving": False,
        },
        "insurance_history_info": {"has_insurance": False, "claimFlag": False},
        "other_info": {
            "has_rejection_record": False,
            "move_in_date": "2025-05-13",
            "damageWithoutInsurance": False,
            "agreeRatingCheck": False,
        },
    }


@pytest.fixture(scope="function")
def test_insurance_application(test_broker, test_customer, create_model, insurance_application_json_data):
    """
    Fixture to create a test insurance application for the test broker.
    """
    serial_number = "SN123456789"
    ref_code = "IA-JJQQKKAA"
    insurance_type = InsuranceType.HOUSE_INSURANCE
    path = f"{settings.DATA_DIR}/{settings.JSON_DATA_FOLDER}/house-insurance/{serial_number}.json"
    JSONHandler.create(path).save_json(insurance_application_json_data)

    return create_model(
        InsuranceApplication(
            broker_id=test_broker.id,
            brokerage_id=test_broker.brokerage_id,
            insurance_type=insurance_type,
            customer_id=test_customer.id,
            customer_name="王五",
            province="BC",
            city="Vancouver",
            address="123 Market St",
            postal_code="A1A 2A2",
            email="<EMAIL>",
            phone="+86 13621276049",
            expected_start_date=date(2025, 6, 14),
            serial_number=serial_number,
            ref_code=ref_code,
            status=(
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN
                if test_broker.brokerage_id is None
                else InsuranceApplicationStatus.PENDING_QUOTE
            ),
        )
    )


@pytest.fixture(scope="function")
def test_lead(
    create_model,
    test_insurance_application: InsuranceApplication,
    test_referral_broker: Broker,
) -> Lead:
    """
    Add lead information to the insurance application.
    """
    customer: Customer = create_model(
        Customer(
            broker_id=test_referral_broker.id,
            name=test_insurance_application.customer_name,
            province=test_insurance_application.province,
            city=test_insurance_application.city,
            address=test_insurance_application.address,
            postal_code=test_insurance_application.postal_code,
            gender=test_insurance_application.customer_gender,
            phone=test_insurance_application.phone,
            email=test_insurance_application.email,
        )
    )
    lead: Lead = create_model(
        Lead(
            created_by=test_referral_broker.id,  # type: ignore
            customer_id=customer.id,  # type: ignore
            customer_name=customer.name,  # type: ignore
            customer_gender=customer.gender,
            customer_phone=customer.phone,
            customer_email=customer.email,
            customer_province=customer.province,
            customer_city=customer.city,
            customer_address=customer.address,
            customer_postal_code=customer.postal_code,
            customer_birthday=date(1991, 1, 1),
            status=LeadStatusEnum.PENDING,
            ref_code="LD-ABCD1234X",
            serial_number="LD123456789",
        )
    )
    create_model(
        LeadApplication(
            lead_id=lead.id,  # type: ignore
            application_id=test_insurance_application.id,  # type: ignore
        )
    )
    return lead


@pytest.fixture(scope="function")
def test_brokerage_user(
    create_model,
    create_user,
    test_brokerage,
) -> BrokerageUser:
    """
    Fixture to create a user associated with a brokerage.
    """
    user = create_user(email="<EMAIL>")
    create_model(
        UserRole(
            user_id=user.id,
            role_id=ROLE_BROKER_SUPPORT_ID,  # 4: Brokerage User Role ID
        )
    )
    return create_model(
        BrokerageUser(
            brokerage_id=test_brokerage.id,
            user_id=user.id,
            name="Test Brokerage User",
            phone="+86 13612345678",
        )
    )


@pytest.fixture(scope="function")
def test_online_insurance_policy(create_model, test_insurance_application) -> InsurancePolicy:
    """
    Fixture to create a test insurance policy.
    """
    insurance_policy = InsurancePolicy(
        **test_insurance_application.model_dump(
            exclude={
                "id",
                "created_at",
                "updated_at",
                "status",
                "ref_code",
                "deleted_at",
                "deleted_by",
                "premium",
                "start_date",
                "end_date",
                "policy_no",
            }
        ),
        premium=Decimal(1000.0),
        start_date=date(2026, 1, 1),
        end_date=date(2027, 1, 1),
        policy_no="POLICY123456",
        application_id=test_insurance_application.id,
        ref_code="IP-ABCD1234X",
        source_type=InsurancePolicySourceType.ONLINE,
    )
    return create_model(insurance_policy)


@pytest.fixture(scope="function")
def test_manual_insurance_policy(create_model, test_customer, test_broker) -> InsurancePolicy:
    policy = InsurancePolicy(
        broker_id=test_broker.id,
        brokerage_id=test_broker.brokerage_id,
        customer_id=test_customer.id,
        customer_name=test_customer.name,
        customer_gender=test_customer.gender,
        phone=test_customer.phone,
        email=test_customer.email,
        province=test_customer.province,
        city=test_customer.city,
        address=test_customer.address,
        postal_code=test_customer.postal_code,
        premium=Decimal(1000.0),
        start_date=date(2026, 1, 1),
        end_date=date(2027, 1, 1),
        policy_no="POLICY654321",
        insurance_type=InsuranceType.RENTERS_INSURANCE,
        ref_code="IP-XYZ9876W",
        source_type=InsurancePolicySourceType.MANUAL,
    )
    return create_model(policy)

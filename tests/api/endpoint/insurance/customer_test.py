from collections.abc import Callable
from datetime import timedelta

import pytest
from fastapi.testclient import TestClient

from bethune.model.broker import Broker
from bethune.util.date import get_current_date
from tests.api.endpoint.endpoint_test import BaseEndpointTester


class TestBrokerCustomer(BaseEndpointTester):

    @pytest.fixture(autouse=True, scope="function")
    def _setup(
        self,
        test_client: TestClient,
        api_prefix: str,
        test_broker: Broker,
        auth_headers_generator: Callable[[str], dict[str, str]],
    ) -> None:
        self.test_client = test_client
        self.current_user = test_broker
        self.api_prefix = f"{api_prefix}/insurance/broker/customer"
        self.auth_headers_generator = auth_headers_generator

    def test_create_customer(self):
        customer_data = {
            "name": "Test Customer",
            "email": "<EMAIL>",
            "phone": "1234567890",
            "province": "BC",
            "city": "Vancouver",
            "address": "123 Test St, Test City",
            "postal_code": "V5K0A1",
            "birthday": "1995-03-21",
            "wechat": "test_wechat",
            "memo": "This is a test customer",
            "tags": ["test", "customer"],
        }

        data: dict = self._post("", json_data=customer_data)  # type: ignore

        assert data["name"] == customer_data["name"]
        assert data["email"] == customer_data["email"]
        assert data["broker_id"] == self.current_user.id

    def test_get_customer(self, test_customer):
        data: dict = self._get(f"/{test_customer.id}")  # type: ignore

        assert data["id"] == test_customer.id
        assert data["name"] == test_customer.name
        assert data["email"] == test_customer.email

    def test_update_customer(self, test_customer):
        updated_data = {
            "id": test_customer.id,
            "name": "Updated Customer",
            "email": "<EMAIL>",
        }

        data: dict = self._put(f"/{test_customer.id}", json_data=updated_data)  # type: ignore
        assert data["id"] == test_customer.id
        assert data["name"] == updated_data["name"]
        assert data["email"] == updated_data["email"]

    def test_query_customers(self, test_customer):
        params = {
            "keyword": "ca",
        }
        data: dict = self._get("", params=params)  # type: ignore
        assert len(data["items"]) == data["total"]

        params = {
            "keyword": "nonexistent",
        }
        data = self._get("", params=params)  # type: ignore
        assert len(data["items"]) == 0

        params = {
            "name": test_customer.name,
            "email": test_customer.email,
        }
        data = self._get("", params=params)  # type: ignore
        assert len(data["items"]) == 1
        assert data["items"][0]["id"] == test_customer.id

    def test_query_customers_by_expiring_days(self, test_online_insurance_policy, update_model):
        params = {
            "expire_in_days": 30,
        }
        data: dict = self._get("", params=params)  # type: ignore
        assert len(data["items"]) == 0
        test_online_insurance_policy.end_date = get_current_date() + timedelta(days=15)
        update_model(test_online_insurance_policy)
        params = {
            "expire_in_days": 30,
        }
        data: dict = self._get("", params=params)  # type: ignore
        assert len(data["items"]) == 1

from collections.abc import Callable
from unittest.mock import AsyncMock

import pytest
from fastapi.testclient import TestClient

from ..endpoint_test import BaseEndpointTester
from bethune.api.dto.base import InsuranceType
from bethune.model.base import BaseModel
from bethune.model.broker import Broker
from bethune.model.brokerage_user import BrokerageUser
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.model.insurance import InsurancePolicy
from bethune.model.lead import Lead
from bethune.model.lead import LeadReferralFeePayment
from bethune.model.lead import PaymentStatusEnum


class TestRegularInsuranceApplication(BaseEndpointTester):

    @pytest.fixture(autouse=True, scope="function")
    def _setup(
        self,
        api_prefix: str,
        auth_headers_generator: Callable[[str], dict],
        test_client: TestClient,
        test_broker: Broker,
        test_insurance_application: InsuranceApplication,
    ):
        self.test_client = test_client
        self.current_user = test_broker
        self.test_insurance_application = test_insurance_application
        self.api_prefix = f"{api_prefix}/insurance/broker/insurance-application"
        self.auth_headers_generator = auth_headers_generator

    def test_get_insurance_application(self):
        data: dict = self._get(f"/{self.test_insurance_application.id}")
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["applicant_info"]["name"] == "王五"

    def test_get_insurance_application_by_ref_code(self):
        data = self._get(f"/{self.test_insurance_application.ref_code}")
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["applicant_info"]["name"] == "王五"

    def test_create_insurance_application(self, insurance_application_json_data):
        data = self._post("", insurance_application_json_data)
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["status"] == InsuranceApplicationStatus.PENDING_UNDERWRITTEN

    def test_underwrite(self):
        data = self._post(
            f"/{self.test_insurance_application.id}/underwritten",
            {
                "policy_no": "POLICY123456",
                "premium": 1000.0,
                "start_date": "2025-10-01",
                "end_date": "2026-10-01",
            },
        )
        assert data["status"] == InsuranceApplicationStatus.UNDERWRITTEN


class TestBrokerageInsuranceApplication(BaseEndpointTester):

    @pytest.fixture(autouse=True, scope="function")
    def _setup(
        self,
        api_prefix: str,
        auth_headers_generator: Callable[[str], dict],
        test_client: TestClient,
        test_brokerage_broker: Broker,
        test_insurance_application: InsuranceApplication,
        update_model: Callable[[BaseModel], BaseModel],
    ):
        self.test_client = test_client
        self.current_user = test_brokerage_broker
        self.api_prefix = f"{api_prefix}/insurance/broker/insurance-application"
        self.auth_headers_generator = auth_headers_generator
        self.test_insurance_application = test_insurance_application
        self.update_model = update_model

    def test_get_insurance_application(self):
        data = self._get(f"/{self.test_insurance_application.id}")
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["applicant_info"]["name"] == "王五"

    def test_get_insurance_application_by_ref_code(self):
        data = self._get(f"/{self.test_insurance_application.ref_code}")
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["applicant_info"]["name"] == "王五"

    def test_create_insurance_application(self, insurance_application_json_data):
        data = self._post("", insurance_application_json_data)
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["status"] == InsuranceApplicationStatus.PENDING_QUOTE
        assert data["brokerage_id"] == self.current_user.brokerage_id

    def test_quote_request(self):
        data = self._post(f"/{self.test_insurance_application.id}/request-quote", {})
        assert data["status"] == InsuranceApplicationStatus.QUOTING

    def test_withdraw(self):
        self.test_insurance_application.status = InsuranceApplicationStatus.QUOTING
        self.test_insurance_application.operator_id = self.current_user.id
        self.test_insurance_application.operator_name = self.current_user.name
        self.update_model(self.test_insurance_application)
        data = self._post(f"/{self.test_insurance_application.id}/withdraw", {})
        assert data["status"] == InsuranceApplicationStatus.PENDING_QUOTE

    def test_underwrite(self):
        data = self._post(
            f"/{self.test_insurance_application.id}/underwritten",
            {
                "policy_no": "POLICY123456",
                "premium": 1200.0,
                "start_date": "2025-10-01",
                "end_date": "2026-10-01",
            },
        )
        assert data["status"] == InsuranceApplicationStatus.UNDERWRITTEN
        assert data["premium"] == 1200.0


class TestBrokerageInsuranceApplicationInBrokerageSide(BaseEndpointTester):

    @pytest.fixture(autouse=True, scope="function")
    def _setup(
        self,
        api_prefix: str,
        auth_headers_generator: Callable[[str], dict],
        test_client: TestClient,
        test_brokerage_broker: Broker,
        test_brokerage_user: BrokerageUser,
        test_insurance_application: InsuranceApplication,
        update_model: Callable[[BaseModel], BaseModel],
        get_models: Callable[[BaseModel], list[BaseModel] | None],
    ):
        self.test_client = test_client
        self.test_brokerage_broker = test_brokerage_broker
        self.current_user = test_brokerage_user
        self.api_prefix = f"{api_prefix}/insurance/brokerage/insurance-application"
        self.auth_headers_generator = auth_headers_generator
        self.test_insurance_application = test_insurance_application
        self.update_model = update_model
        self.get_models = get_models

    def test_get_insurance_application(self):
        data = self._get(f"/{self.test_insurance_application.id}")
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["applicant_info"]["name"] == "王五"
        assert data["insurance_application"]["brokerage_id"] == self.test_brokerage_broker.brokerage_id

    def test_get_insurance_application_by_ref_code(self):
        data = self._get(f"/{self.test_insurance_application.ref_code}")
        assert data["insurance_type"] == InsuranceType.HOUSE_INSURANCE
        assert data["applicant_info"]["name"] == "王五"
        assert data["insurance_application"]["brokerage_id"] == self.test_brokerage_broker.brokerage_id

    def test_accept(self):
        self.test_insurance_application.status = InsuranceApplicationStatus.QUOTING
        self.update_model(self.test_insurance_application)

        data = self._post(f"/{self.test_insurance_application.id}/accept", {})

        assert data["status"] == InsuranceApplicationStatus.QUOTING
        assert data["operator_id"] == self.current_user.id
        assert data["operator_name"] == self.current_user.name

    def test_unaccept(self):
        self.test_insurance_application.status = InsuranceApplicationStatus.QUOTING
        self.test_insurance_application.operator_id = self.current_user.id
        self.test_insurance_application.operator_name = self.current_user.name
        self.update_model(self.test_insurance_application)

        data = self._post(f"/{self.test_insurance_application.id}/unaccept", {})

        assert data["status"] == InsuranceApplicationStatus.QUOTING
        assert data["operator_id"] is None
        assert data["operator_name"] is None

    def test_quote(self, mocker):
        self.test_insurance_application.status = InsuranceApplicationStatus.QUOTING
        self.test_insurance_application.operator_id = self.current_user.id
        self.test_insurance_application.operator_name = self.current_user.name
        self.update_model(self.test_insurance_application)
        mock_send_email = AsyncMock()
        mocker.patch(
            "bethune.service.core.factory.CoreServiceFactory.create_email_service",
            return_value=mocker.MagicMock(send_email=mock_send_email),
        )
        data = self._post(
            f"/{self.test_insurance_application.id}/quote",
            {
                "premium": 1200.0,
                "memo": "Test quote",
            },
        )

        assert data["status"] == InsuranceApplicationStatus.PENDING_UNDERWRITTEN
        assert data["premium"] == 1200.0
        assert data["memo"] == "Test quote"
        assert data["quote_at"] is not None
        mock_send_email.assert_called_once()

    def test_reject(self, mocker):
        self.test_insurance_application.status = InsuranceApplicationStatus.PENDING_UNDERWRITTEN
        self.test_insurance_application.operator_id = self.current_user.id
        self.test_insurance_application.operator_name = self.current_user.name
        self.update_model(self.test_insurance_application)
        mock_send_email = AsyncMock()
        mocker.patch(
            "bethune.service.core.factory.CoreServiceFactory.create_email_service",
            return_value=mocker.MagicMock(send_email=mock_send_email),
        )

        data = self._post(
            f"/{self.test_insurance_application.id}/reject",
            {
                "memo": "Test reject",
            },
        )

        assert data["status"] == InsuranceApplicationStatus.PENDING_QUOTE
        assert data["memo"] == "Test reject"
        assert data["operator_id"] == self.current_user.id
        assert data["operator_name"] == self.current_user.name
        mock_send_email.assert_called_once()

    def test_underwrite(self, mocker):
        self.test_insurance_application.status = InsuranceApplicationStatus.PENDING_UNDERWRITTEN
        self.test_insurance_application.operator_id = self.current_user.id
        self.test_insurance_application.operator_name = self.current_user.name
        self.update_model(self.test_insurance_application)
        mock_send_email = AsyncMock()
        mocker.patch(
            "bethune.service.core.factory.CoreServiceFactory.create_email_service",
            return_value=mocker.MagicMock(send_email=mock_send_email),
        )
        data = self._post(
            f"/{self.test_insurance_application.id}/underwritten",
            {
                "policy_no": "POLICY123456",
                "premium": 1500.0,
                "start_date": "2025-10-01",
                "end_date": "2026-10-01",
                "memo": "Test underwrite",
            },
        )

        assert data["status"] == InsuranceApplicationStatus.UNDERWRITTEN
        assert data["premium"] == 1500.0
        assert data["policy_no"] == "POLICY123456"
        assert data["start_date"] == "2025-10-01"
        assert data["end_date"] == "2026-10-01"
        assert data["memo"] == "Test underwrite"
        assert data["underwrite_at"] is not None

        policyes = self.get_models(InsurancePolicy(insurance_application_id=self.test_insurance_application.id))
        assert len(policyes) == 1, "One and only one insurance policy should be created"
        assert policyes[0].policy_no == "POLICY123456", "Policy number should match the one provided in underwrite"
        assert policyes[0].premium == 1500.0, "Premium should match the one provided in underwrite"
        assert str(policyes[0].start_date) == "2025-10-01", "Start date should match the one provided in underwrite"
        assert str(policyes[0].end_date) == "2026-10-01", "End date should match the one provided in underwrite"
        assert (
            policyes[0].brokerage_id == self.test_brokerage_broker.brokerage_id
        ), "Insurance policy should be created with brokerage_id from the insurance application"
        assert policyes[0].ref_code is not None, "Insurance policy should have a ref_code generated automatically"
        mock_send_email.assert_called_once()


class TestLeadInsuranceApplication(BaseEndpointTester):

    @pytest.fixture(autouse=True, scope="function")
    def _setup(
        self,
        api_prefix: str,
        auth_headers_generator: Callable[[str], dict],
        test_client: TestClient,
        test_broker: Broker,
        test_referral_broker: Broker,
        test_insurance_application: InsuranceApplication,
        test_lead: Lead,
        get_models: Callable[[BaseModel], bool],
    ):
        self.test_client = test_client
        self.current_user = test_broker
        self.test_referral_broker = test_referral_broker
        self.api_prefix = f"{api_prefix}/insurance/insurance-application"
        self.auth_headers_generator = auth_headers_generator
        self.test_insurance_application = test_insurance_application
        self.test_lead = test_lead
        self.get_models = get_models

    def test_underwrite_and_lead_payment(self):
        data = self._post(
            f"/{self.test_insurance_application.id}/underwritten",
            {
                "policy_no": "POLICY123456",
                "premium": 1000.0,
                "start_date": "2025-10-01",
                "end_date": "2026-10-01",
            },
        )
        assert data["status"] == InsuranceApplicationStatus.UNDERWRITTEN
        assert data["premium"] == 1000.0
        lead_referral_fee_payments: list[LeadReferralFeePayment] = self.get_models(
            LeadReferralFeePayment(
                lead_id=self.test_lead.id,
            )
        )
        assert len(lead_referral_fee_payments) == 1, "One and only one lead referral fee payment should be created"
        assert (
            lead_referral_fee_payments[0].payment_status == PaymentStatusEnum.CREATED
        ), "Lead referral fee payment should be created with status CREATED"

        data = self._post(
            f"/{self.test_insurance_application.id}/lead-payment",
            json_data={},
        )
        lead_referral_fee_payments: list[LeadReferralFeePayment] = self.get_models(
            LeadReferralFeePayment(
                lead_id=self.test_lead.id,
            )
        )
        assert len(lead_referral_fee_payments) == 1, "One and only one lead referral fee payment should be created"
        assert (
            lead_referral_fee_payments[0].payment_status == PaymentStatusEnum.PAID
        ), "Lead referral fee payment should be updated with status PAID"

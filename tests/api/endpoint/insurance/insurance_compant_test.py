from unittest.mock import MagicMock

import pytest
from fastapi.testclient import TestClient

from bethune.api.dto.insurance_company import InsuranceCompany


@pytest.fixture
def client(test_client: TestClient):
    return test_client


@pytest.fixture
def api_prefix(api_prefix):
    return f"{api_prefix}/insurance"


@pytest.fixture
def mock_insurance_company_service(mocker):
    mock_service = MagicMock()
    mocker.patch(
        "bethune.api.endpoint.insurance.service_context.ServiceContext.insurance_company_service",
        mock_service,
    )
    return mock_service


def test_get_by_id(
    client: TestClient,
    api_prefix: str,
    mock_insurance_company_service,
):
    insurance_company_id = 1
    mock_insurance_company_service.get_by_id.return_value = InsuranceCompany(
        id=insurance_company_id,
        name="test",
        code="test0001",
    )

    response = client.get(f"{api_prefix}/insurance-company/{insurance_company_id}")

    assert response.status_code == 200
    data = response.json()
    assert data["data"]["id"] == insurance_company_id
    assert data["data"]["name"] == "test"
    assert data["data"]["code"] == "test0001"

from collections.abc import Callable
from typing import Any

import pytest
from fastapi.testclient import TestClient

from ..endpoint_test import BaseEndpointTester
from bethune.api.dto.base import InsuranceType
from bethune.model.base import BaseModel
from bethune.model.broker import Broker
from bethune.model.customer import Customer
from bethune.model.insurance import InsurancePolicy
from bethune.model.insurance import InsurancePolicySourceType


class TestOnlineInsurancePolicy(BaseEndpointTester):

    @pytest.fixture(autouse=True, scope="function")
    def _setup(
        self,
        test_client: TestClient,
        auth_headers_generator: Callable[[str], dict[str, Any]],
        api_prefix: str,
        create_model: Callable[[BaseModel], BaseModel],
        update_model: Callable[[BaseModel], BaseModel],
        get_models: Callable[[BaseModel], list[BaseModel]],
        test_broker: Broker,
        test_online_insurance_policy: InsurancePolicy,
        test_manual_insurance_policy: InsurancePolicy,
    ) -> None:
        """
        Setup fixture for TestInsurancePolicy.
        """
        self.test_client = test_client
        self.api_prefix = f"{api_prefix}/insurance/broker/insurance-policy"
        self.auth_headers_generator = auth_headers_generator
        self.create_model = create_model
        self.update_model = update_model
        self.get_models = get_models
        self.current_user = test_broker
        self.test_online_insurance_policy = test_online_insurance_policy
        self.test_manual_insurance_policy = test_manual_insurance_policy

    def test_get_insurance_policy(self) -> None:
        data: dict = self._get(f"/{self.test_online_insurance_policy.id}")  # type: ignore
        assert data["broker_id"] == self.current_user.id, "Broker ID in response does not match expected ID"
        assert data["brokerage_id"] == self.current_user.brokerage_id, "Response does not contain insurance policy data"
        assert data["source_type"] == InsurancePolicySourceType.ONLINE, "Source type does not match expected value"

    def test_create_manual_insurance_policy(self) -> None:
        """
        Test creating a manual insurance policy.
        """
        customer_id = self.test_manual_insurance_policy.customer_id
        customer: Customer = self.get_models(Customer(id=customer_id))[0]  # type: ignore
        data: dict = self._post(
            "",
            json_data={
                "customer_id": customer.id,
                "customer_name": customer.name,
                "customer_gender": customer.gender,
                "phone": customer.phone,
                "email": customer.email,
                "province": customer.province,
                "city": customer.city,
                "address": customer.address,
                "postal_code": customer.postal_code,
                "premium": 1200.0,
                "start_date": "2026-01-01",
                "end_date": "2027-01-01",
                "policy_no": "POLICY987654",
                "insurance_type": InsuranceType.HOUSE_INSURANCE,
            },
        )  # type: ignore
        assert data["broker_id"] == self.current_user.id, "Broker ID in response does not match expected ID"
        assert (
            data["brokerage_id"] == self.current_user.brokerage_id
        ), "Brokerage ID in response does not match expected ID"
        assert data["source_type"] == InsurancePolicySourceType.MANUAL, "Source type does not match expected value"
        assert data["customer_id"] == customer.id, "Customer ID in response does not match"
        assert data["premium"] == 1200.0, "Premium in response does not match expected value"
        assert data["start_date"] == "2026-01-01", "Start date in response does not match expected value"
        assert data["end_date"] == "2027-01-01", "End date in response does not match expected value"
        assert data["policy_no"] == "POLICY987654", "Policy number in response does not match expected value"
        assert data["ref_code"].startswith("IP"), "Reference code does not start with expected prefix"

    def test_query_insurance_policies(self) -> None:
        """
        Test querying insurance policies.
        """
        data: dict = self._get("")  # type: ignore
        assert data["total"] == 2, "Total number of insurance policies does not match expected value"
        assert (
            data["items"][0]["id"] == self.test_online_insurance_policy.id
        ), "First item in response does not match expected online insurance policy"
        assert data["items"][1]["id"] == self.test_manual_insurance_policy.id

        data = self._get("", params={"insurance_type": InsuranceType.RENTERS_INSURANCE})  # type: ignore
        assert (
            data["total"] == 1
        ), "Total number of insurance policies with specified type does not match expected value"
        assert (
            data["items"][0]["id"] == self.test_manual_insurance_policy.id
        ), "Item in response does not match expected manual insurance policy"

        data = self._get("", params={"source_type": InsurancePolicySourceType.ONLINE})  # type: ignore
        assert data["total"] == 1, "Total number of online insurance policies does not match expected value"
        assert (
            data["items"][0]["id"] == self.test_online_insurance_policy.id
        ), "Item in response does not match expected online insurance policy"

from datetime import date
from unittest.mock import AsyncMock

import pytest

from bethune.api.dto.base import InsuranceType
from bethune.model.base import GenderEnum
from bethune.model.lead import LeadStatusEnum


@pytest.fixture
def lead_context(test_client, api_prefix, auth_headers_generator):
    return {
        "client": test_client,
        "api_prefix": f"{api_prefix}/insurance/lead",
        "auth_headers_referral": auth_headers_generator("<EMAIL>"),
        "auth_headers_broker": auth_headers_generator("<EMAIL>"),
    }


@pytest.fixture
def candidate_broker_api_prefix(api_prefix):
    return f"{api_prefix}/insurance/lead/candidate_broker_assignee"


@pytest.fixture
def base_lead_data():
    return {
        "insurance_type": InsuranceType.HOUSE_INSURANCE,
        "customer_province": "BC",
        "customer_city": "Vancouver",
        "customer_address": "123 Test Street",
        "customer_postal_code": "V6B 1A1",
        "customer_name": "Test Customer",
        "customer_gender": GenderEnum.MALE,
        "customer_birthday": date(1990, 1, 1).isoformat(),
        "customer_email": "<EMAIL>",
        "customer_phone": "******-456-7890",
        "assign_to": 1,
        "additional_info": "这是一个测试线索",
    }


@pytest.fixture
def create_lead(lead_context, base_lead_data):
    def _create(customer=None):
        client = lead_context["client"]
        api_prefix = lead_context["api_prefix"]
        auth_headers = lead_context["auth_headers_referral"]

        lead_data = base_lead_data.copy()
        if customer:
            lead_data.update(customer)

        response = client.post(api_prefix, json=lead_data, headers=auth_headers)
        assert response.status_code == 200
        return response.json()["data"]

    return _create


@pytest.fixture
def create_and_assign_lead(mocker, lead_context, create_lead):
    def _create_and_assign(customer=None, assign_to_broker_id=1):
        lead = create_lead(customer)
        lead_id = lead["id"]

        client = lead_context["client"]
        api_prefix = lead_context["api_prefix"]
        auth_headers = lead_context["auth_headers_referral"]
        lead_assign_request = {"is_anonymous": True, "assign_to_id": assign_to_broker_id}
        mock_send_email = AsyncMock()
        mocker.patch(
            "bethune.service.core.factory.CoreServiceFactory.create_email_service",
            return_value=mocker.MagicMock(send_email=mock_send_email),
        )
        assign_response = client.post(
            f"{api_prefix}/{lead_id}/assign_to", headers=auth_headers, json=lead_assign_request
        )
        assert assign_response.status_code == 200
        mock_send_email.assert_called_once()

        return {
            "lead": lead,
            "lead_id": lead_id,
            "broker_id": assign_to_broker_id,
            "assigned_lead": assign_response.json()["data"],
        }

    return _create_and_assign


@pytest.fixture
def change_lead_status(lead_context):
    def _change_status(lead_id, action, headers=None):
        client = lead_context["client"]
        api_prefix = lead_context["api_prefix"]

        if headers is None:
            headers = lead_context["auth_headers_broker"]

        response = client.post(f"{api_prefix}/{lead_id}/{action}", headers=headers)
        assert response.status_code == 200

        detail_response = client.get(f"{api_prefix}/{lead_id}", headers=headers)
        assert detail_response.status_code == 200

        return {"response": response.json(), "lead": response.json()["data"], "detail": detail_response.json()["data"]}

    return _change_status


@pytest.mark.parametrize(
    "customer",
    [
        {"customer_id": 1},
        {},
    ],
    ids=["with_customer_id", "without_customer_id"],
)
def test_create_lead_and_assign_to_broker(create_and_assign_lead, customer):
    result = create_and_assign_lead(customer)
    lead = result["lead"]
    assert lead["insurance_type"] == InsuranceType.HOUSE_INSURANCE
    assert lead["customer_name"] == "Test Customer"
    assert lead["customer_email"] == "<EMAIL>"
    assert lead["customer_phone"] == "******-456-7890"
    assert lead["assign_to"] == 1
    assert "ref_code" in lead

    assigned_lead = result["assigned_lead"]
    assert assigned_lead["assign_to"] == result["broker_id"]
    assert assigned_lead["status"] == LeadStatusEnum.PENDING


@pytest.mark.parametrize(
    "action,expected_status,use_headers",
    [
        ("accept", LeadStatusEnum.ACCEPTED, "auth_headers_broker"),
        ("reject", LeadStatusEnum.REJECTED, "auth_headers_broker"),
        ("withdraw", LeadStatusEnum.WITHDRAWN, "auth_headers_referral"),
    ],
    ids=["accept", "reject", "withdraw"],
)
@pytest.mark.parametrize(
    "customer",
    [
        {"customer_id": 1},
        {},
    ],
    ids=["with_customer_id", "without_customer_id"],
)
def test_lead_status_changes(create_and_assign_lead, lead_context, action, expected_status, use_headers, customer):
    result = create_and_assign_lead(customer)
    lead_id = result["lead_id"]
    broker_id = result["broker_id"]

    client = lead_context["client"]
    api_prefix = lead_context["api_prefix"]
    headers = lead_context[use_headers]

    response = client.post(f"{api_prefix}/{lead_id}/{action}", headers=headers)
    assert response.status_code == 200
    data = response.json()
    lead = data["data"]

    assert lead["status"] == expected_status
    assert lead["id"] == lead_id
    assert lead["assign_to"] == broker_id

    detail_response = client.get(f"{api_prefix}/{lead_id}", headers=headers)
    assert detail_response.status_code == 200
    detail_data = detail_response.json()["data"]
    assert detail_data["status"] == expected_status


@pytest.mark.parametrize(
    "customer",
    [
        {"customer_id": 1},
        {},
    ],
    ids=["with_customer_id", "without_customer_id"],
)
def test_accept_lead_and_create_application(create_and_assign_lead, change_lead_status, lead_context, customer):
    result = create_and_assign_lead(customer)
    lead_id = result["lead_id"]

    status_result = change_lead_status(lead_id, "accept")
    assert status_result["lead"]["status"] == LeadStatusEnum.ACCEPTED

    client = lead_context["client"]
    api_prefix = lead_context["api_prefix"]
    broker_headers = lead_context["auth_headers_broker"]

    application_response = client.post(f"{api_prefix}/{lead_id}/application", headers=broker_headers)
    assert application_response.status_code == 200

    application = application_response.json()["data"]
    assert "id" in application
    assert "ref_code" in application
    assert application["customer_name"] == "Test Customer"
    assert application["email"] == "<EMAIL>"
    assert application["insurance_type"] == InsuranceType.HOUSE_INSURANCE


@pytest.mark.parametrize("customer", [{}], ids=["without_customer_id"])
def test_modify_lead(mocker, lead_context, create_lead, customer):
    client = lead_context["client"]
    api_prefix = lead_context["api_prefix"]
    auth_headers = lead_context["auth_headers_referral"]

    lead = create_lead(customer)
    lead_id = lead["id"]

    update_data = {
        "customer_province": "ON",
        "customer_city": "Toronto",
        "customer_address": "456 Modified Street",
        "customer_postal_code": "M5V 2A1",
        "customer_name": "Modified Customer",
        "customer_gender": GenderEnum.FEMALE,
        "customer_birthday": date(1992, 2, 2).isoformat(),
        "customer_email": "<EMAIL>",
        "customer_phone": "******-654-3210",
        "additional_info": "这是修改后的测试线索",
    }
    if customer:
        update_data.update(customer)

    update_response = client.put(f"{api_prefix}/{lead_id}", json=update_data, headers=auth_headers)
    assert update_response.status_code == 200

    updated_lead = update_response.json()["data"]
    assert updated_lead["customer_province"] == "ON"
    assert updated_lead["customer_city"] == "Toronto"
    assert updated_lead["customer_address"] == "456 Modified Street"
    assert updated_lead["customer_postal_code"] == "M5V 2A1"
    assert updated_lead["customer_name"] == "Modified Customer"
    assert updated_lead["customer_gender"] == GenderEnum.FEMALE
    assert "1992-02-02" in updated_lead["customer_birthday"]
    assert updated_lead["customer_email"] == "<EMAIL>"
    assert updated_lead["customer_phone"] == "******-654-3210"
    assert updated_lead["additional_info"] == "这是修改后的测试线索"
    assert updated_lead["id"] == lead_id
    assert updated_lead["status"] == lead["status"]

    detail_response = client.get(f"{api_prefix}/{lead_id}", headers=auth_headers)
    assert detail_response.status_code == 200
    detail_data = detail_response.json()["data"]
    assert detail_data["customer_name"] == "Modified Customer"
    assert detail_data["customer_email"] == "<EMAIL>"
    lead_assign_request = {"is_anonymous": True, "assign_to_id": 1}

    mock_send_email = AsyncMock()
    mocker.patch(
        "bethune.service.core.factory.CoreServiceFactory.create_email_service",
        return_value=mocker.MagicMock(send_email=mock_send_email),
    )
    assign_response = client.post(f"{api_prefix}/{lead_id}/assign_to", headers=auth_headers, json=lead_assign_request)
    assert assign_response.status_code == 200

    broker_headers = lead_context["auth_headers_broker"]
    response = client.post(f"{api_prefix}/{lead_id}/accept", headers=broker_headers)
    assert response.status_code == 200
    assert response.json()["data"]["status"] == LeadStatusEnum.ACCEPTED

    failed_update_response = client.put(
        f"{api_prefix}/{lead_id}", json={"customer_name": "Should Not Change"}, headers=auth_headers
    )
    assert failed_update_response.status_code == 422


@pytest.mark.parametrize(
    "keyword,expected_broker_name",
    [
        ("test", "Test2 Broker"),
        ("test2", "Test2 Broker"),
        (None, "Test2 Broker"),
    ],
    ids=["with_keyword", "with_keyword_test2", "without_keyword"],
)
def test_candidate_broker_assignee_with_keyword(
    lead_context, candidate_broker_api_prefix, keyword, expected_broker_name
):
    params = {
        "page_no": 1,
        "page_size": 20,
        "insurance_type": "HOUSE_INSURANCE",
        "customer_province": "BC",
        "customer_city": "VA",
    }

    if keyword:
        params["keyword"] = keyword

    response = lead_context["client"].get(
        candidate_broker_api_prefix, params=params, headers=lead_context["auth_headers_referral"]
    )

    assert response.status_code == 200

    data = response.json()["data"]
    assert "items" in data and "page_no" in data and "page_size" in data
    assert data["page_no"] == 1 and data["page_size"] == 20
    assert data["items"][1]["name"] == expected_broker_name


@pytest.mark.parametrize(
    "customer",
    [
        {"customer_id": 1},
        {},
    ],
    ids=["with_customer_id", "without_customer_id"],
)
def test_assign_lead_with_wrong_role_should_fail(lead_context, create_lead, customer):
    lead = create_lead(customer)
    lead_id = lead["id"]

    client = lead_context["client"]
    api_prefix = lead_context["api_prefix"]
    broker_headers = lead_context["auth_headers_broker"]
    lead_assign_request = {"is_anonymous": True, "assign_to_id": 1}
    assign_response = client.post(f"{api_prefix}/{lead_id}/assign_to", headers=broker_headers, json=lead_assign_request)
    assert assign_response.json()["code"] == 4050
    assert assign_response.json()["message"] == "You are not the owner of lead"


@pytest.mark.parametrize(
    "action,expected_status,use_headers",
    [
        ("accept", LeadStatusEnum.ACCEPTED, "auth_headers_broker"),
        ("reject", LeadStatusEnum.REJECTED, "auth_headers_broker"),
        ("withdraw", LeadStatusEnum.WITHDRAWN, "auth_headers_referral"),
    ],
    ids=["accepted", "rejected", "withdrawn"],
)
@pytest.mark.parametrize(
    "customer",
    [
        {"customer_id": 1},
        {},
    ],
    ids=["with_customer_id", "without_customer_id"],
)
def test_assign_lead_after_status_change_should_fail(
    create_and_assign_lead, lead_context, action, expected_status, use_headers, customer
):
    result = create_and_assign_lead(customer)
    lead_id = result["lead_id"]

    client = lead_context["client"]
    api_prefix = lead_context["api_prefix"]
    headers = lead_context[use_headers]

    response = client.post(f"{api_prefix}/{lead_id}/{action}", headers=headers)
    assert response.status_code == 200
    assert response.json()["data"]["status"] == expected_status

    auth_headers = lead_context["auth_headers_referral"]
    lead_assign_request = {"is_anonymous": True, "assign_to_id": 1}
    assign_response = client.post(f"{api_prefix}/{lead_id}/assign_to", headers=auth_headers, json=lead_assign_request)
    assert assign_response.json()["code"] == 4030
    assert (
        assign_response.json()["message"]
        == "The status of current lead is not draft, you can not change the assignment of this lead"
    )


@pytest.mark.parametrize(
    "action,use_headers,test_scenario",
    [
        ("withdraw", "auth_headers_broker", "non_creator_withdraw"),
        ("reject", "auth_headers_broker", "rejected_lead"),
        ("withdraw", "auth_headers_referral", "withdrawn_lead"),
    ],
    ids=[
        "non_creator_withdraw",
        "rejected_lead",
        "withdrawn_lead",
    ],
)
@pytest.mark.parametrize(
    "customer",
    [
        {"customer_id": 1},
        {},
    ],
    ids=["with_customer_id", "without_customer_id"],
)
def test_create_application_error_scenarios(
    create_and_assign_lead, lead_context, action, use_headers, test_scenario, customer
):
    result = create_and_assign_lead(customer)
    lead_id = result["lead_id"]

    client = lead_context["client"]
    api_prefix = lead_context["api_prefix"]

    if action:
        headers = lead_context[use_headers]
        response = client.post(f"{api_prefix}/{lead_id}/{action}", headers=headers)
    if test_scenario == "non_creator_withdraw":
        assert response.status_code == 403
        assert response.json()["message"] == "Only the creator of the lead can withdraw it"
    application_response = client.post(f"{api_prefix}/{lead_id}/application", headers=headers)
    assert application_response.status_code == 422


def test_candidate_broker_assignee_with_lead_id(lead_context, candidate_broker_api_prefix, create_lead):
    lead_id = create_lead()["id"]

    params = {
        "page_no": 1,
        "page_size": 20,
        "lead_id": lead_id,
    }

    response = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=params, headers=lead_context["auth_headers_referral"]
    )

    assert response.status_code == 200
    assert "items" in response.json()["data"]


def test_candidate_broker_assignee_with_direct_params(lead_context, candidate_broker_api_prefix):
    params = {
        "page_no": 1,
        "page_size": 20,
        "insurance_type": "HOUSE_INSURANCE",
        "customer_province": "BC",
        "customer_city": "Vancouver",
    }

    response = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=params, headers=lead_context["auth_headers_referral"]
    )

    assert response.status_code == 200
    assert "items" in response.json()["data"]


@pytest.mark.parametrize(
    "missing_param,expected_error",
    [
        ("insurance_type", "insurance_type"),
        ("customer_province", "customer_province"),
        ("customer_city", "customer_city"),
    ],
    ids=["missing_insurance_type", "missing_customer_province", "missing_customer_city"],
)
def test_candidate_broker_assignee_missing_required_params(
    lead_context, candidate_broker_api_prefix, missing_param, expected_error
):
    base_params = {
        "page_no": 1,
        "page_size": 20,
        "insurance_type": "HOUSE_INSURANCE",
        "customer_province": "BC",
        "customer_city": "Vancouver",
    }

    del base_params[missing_param]

    response = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=base_params, headers=lead_context["auth_headers_referral"]
    )

    assert response.json()["code"] == 4080
    assert expected_error in response.json()["message"]


def test_candidate_broker_assignee_multiple_missing_params(lead_context, candidate_broker_api_prefix):
    params = {
        "page_no": 1,
        "page_size": 20,
    }

    response = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=params, headers=lead_context["auth_headers_referral"]
    )

    assert response.json()["code"] == 4080
    error_message = response.json()["message"]
    assert "insurance_type" in error_message
    assert "customer_province" in error_message
    assert "customer_city" in error_message


def test_candidate_broker_assignee_unauthorized_lead_access(lead_context, candidate_broker_api_prefix, create_lead):
    lead_id = create_lead()["id"]

    params = {
        "page_no": 1,
        "page_size": 20,
        "lead_id": lead_id,
    }

    response = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=params, headers=lead_context["auth_headers_broker"]
    )

    assert response.json()["code"] == 4050
    assert response.json()["message"] == "The Lead is not from the logged in person"


def test_candidate_broker_assignee_with_keyword_filter(lead_context, candidate_broker_api_prefix):
    params = {
        "page_no": 1,
        "page_size": 20,
        "insurance_type": "HOUSE_INSURANCE",
        "customer_province": "BC",
        "customer_city": "Vancouver",
        "keyword": "test",
    }

    response = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=params, headers=lead_context["auth_headers_referral"]
    )

    assert response.status_code == 200
    assert "items" in response.json()["data"]


def test_candidate_broker_assignee_flow_test(lead_context, candidate_broker_api_prefix, create_lead):
    lead_id = create_lead()["id"]

    params_with_lead_id = {
        "page_no": 1,
        "page_size": 20,
        "lead_id": lead_id,
    }

    response_with_lead = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=params_with_lead_id, headers=lead_context["auth_headers_referral"]
    )

    assert response_with_lead.status_code == 200

    params_direct = {
        "page_no": 1,
        "page_size": 20,
        "insurance_type": "HOUSE_INSURANCE",
        "customer_province": "BC",
        "customer_city": "Vancouver",
    }

    response_direct = lead_context["client"].get(
        f"{candidate_broker_api_prefix}", params=params_direct, headers=lead_context["auth_headers_referral"]
    )

    assert response_direct.status_code == 200

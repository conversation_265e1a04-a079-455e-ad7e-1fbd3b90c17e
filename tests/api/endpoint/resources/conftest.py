import pytest

from bethune.settings import settings


@pytest.fixture(autouse=True)
def patch_data_dir(tmp_path):
    test_data_dir = tmp_path / "data"
    test_data_dir.mkdir(parents=True, exist_ok=True)
    (test_data_dir / settings.UPLOADS_FOLDER).mkdir(parents=True, exist_ok=True)
    (test_data_dir / settings.UPLOADS_FOLDER / settings.AVATARS_FOLDER).mkdir(parents=True, exist_ok=True)
    (test_data_dir / settings.UPLOADS_FOLDER / settings.LOGOS_FOLDER).mkdir(parents=True, exist_ok=True)

    original_data_dir = settings.__dict__.get("DATA_DIR")
    original_site_url = settings.__dict__.get("BETHUNE_SITE_URL")

    settings.__dict__["DATA_DIR"] = str(test_data_dir)
    settings.__dict__["BETHUNE_SITE_URL"] = "http://test.example.com"

    yield

    settings.__dict__["DATA_DIR"] = original_data_dir
    settings.__dict__["BETHUNE_SITE_URL"] = original_site_url

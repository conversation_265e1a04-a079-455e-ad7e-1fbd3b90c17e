import base64
import io

import pytest
from fastapi.testclient import TestClient

from bethune.api.dto.base import ResourceTypeEnum


@pytest.fixture
def api_prefix(api_prefix):
    return f"{api_prefix}/resource"


def _create_test_image(image_format: str) -> io.BytesIO:
    if image_format == "png":
        return io.BytesIO(
            b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01"
            b"\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13"
            b"\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc```"
            b"\x00\x00\x00\x04\x00\x01\xdd\x8d\xb4\x1c\x00\x00\x00\x00IEND\xaeB`\x82"
        )
    if image_format == "jpg":
        return io.BytesIO(
            b"\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00"
            b"\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t"
            b"\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a"
            b"\x1f\x1e\x1d\x1a\x1c\x1c $.' \",#\x1c\x1c(7),01444\x1f'9=82<.342"
            b"\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01"
            b"\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08"
            b"\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
            b"\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9"
        )
    if image_format == "gif":
        return io.BytesIO(
            b"GIF87a\x01\x00\x01\x00\x00\x00\x00!\xf9\x04\x01\x00\x00\x00\x00"
            b",\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02\x04\x01\x00;"
        )
    if image_format == "webp":
        return io.BytesIO(
            b"RIFF\x1a\x00\x00\x00WEBPVP8 \x0e\x00\x00\x00\x10\x00\x00\x00"
            b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
        )
    return io.BytesIO(b"invalid content")


def _create_test_file(filename: str, image_format: str, content_type: str, size_mb: int = 0) -> dict:
    if size_mb > 0:
        data = io.BytesIO(b"x" * (size_mb * 1024 * 1024))
    else:
        data = _create_test_image(image_format)
    return {"file": (filename, data, content_type)}


def _create_base64_data(image_format: str) -> str:
    image_bytes = _create_test_image(image_format).getvalue()
    return base64.b64encode(image_bytes).decode()


@pytest.mark.parametrize(
    "resource_type,image_format,content_type,expected_extension",
    [
        (ResourceTypeEnum.AVATAR, "png", "image/png", ".png"),
        (ResourceTypeEnum.LOGO, "jpg", "image/jpeg", ".jpg"),
        (ResourceTypeEnum.AVATAR, "gif", "image/gif", ".gif"),
        (ResourceTypeEnum.LOGO, "png", "image/png", ".png"),
    ],
)
def test_upload_file_success(
    test_client: TestClient,
    api_prefix: str,
    resource_type: ResourceTypeEnum,
    image_format: str,
    content_type: str,
    expected_extension: str,
):
    test_file = _create_test_file(f"test.{image_format}", image_format, content_type)

    response = test_client.post(f"{api_prefix}/file", files=test_file, data={"type": resource_type.value})

    assert response.json()["code"] == 1000
    assert "file" in response.json()["data"]
    assert "file_url" in response.json()["data"]
    assert resource_type.value.lower() in response.json()["data"]["file"]


def test_upload_file_missing_type(test_client: TestClient, api_prefix: str):
    test_file = _create_test_file("test.png", "png", "image/png")

    response = test_client.post(f"{api_prefix}/file", files=test_file)

    assert response.status_code == 422


@pytest.mark.parametrize(
    "resource_type,image_format,expected_extension",
    [
        (ResourceTypeEnum.AVATAR, "png", ".png"),
        (ResourceTypeEnum.LOGO, "jpg", ".jpg"),
        (ResourceTypeEnum.AVATAR, "gif", ".gif"),
        (ResourceTypeEnum.LOGO, "webp", ".webp"),
    ],
)
def test_upload_image_base64_success(
    test_client: TestClient,
    api_prefix: str,
    resource_type: ResourceTypeEnum,
    image_format: str,
    expected_extension: str,
):
    base64_data = _create_base64_data(image_format)

    response = test_client.post(f"{api_prefix}/image", json={"type": resource_type.value, "data": base64_data})

    assert response.json()["code"] == 1000
    assert "file" in response.json()["data"]
    assert "file_url" in response.json()["data"]
    assert resource_type.value.lower() in response.json()["data"]["file"]
    assert response.json()["data"]["file"].endswith(expected_extension)


def test_upload_image_base64_with_data_url_prefix(test_client: TestClient, api_prefix: str):
    base64_data = _create_base64_data("png")
    data_url = f"data:image/png;base64,{base64_data}"

    response = test_client.post(f"{api_prefix}/image", json={"type": ResourceTypeEnum.AVATAR.value, "data": data_url})

    assert response.json()["code"] == 1000


@pytest.mark.parametrize(
    "missing_field,json_data,expected_status",
    [
        ("type", {"data": "valid_base64_data"}, 422),
        ("data", {"type": ResourceTypeEnum.AVATAR.value}, 422),
        ("invalid_type", {"type": "INVALID_TYPE", "data": "valid_base64_data"}, 422),
    ],
)
def test_upload_image_base64_validation_errors(
    test_client: TestClient, api_prefix: str, missing_field: str, json_data: dict, expected_status: int
):
    response = test_client.post(f"{api_prefix}/image", json=json_data)
    assert response.status_code == expected_status


def test_upload_image_base64_unknown_format_defaults_to_jpg(test_client: TestClient, api_prefix: str):
    unknown_bytes = b"unknown_image_format_data"
    base64_data = base64.b64encode(unknown_bytes).decode()

    response = test_client.post(
        f"{api_prefix}/image", json={"type": ResourceTypeEnum.AVATAR.value, "data": base64_data}
    )

    assert response.json()["code"] == 1000
    assert response.json()["data"]["file"].endswith(".jpg")

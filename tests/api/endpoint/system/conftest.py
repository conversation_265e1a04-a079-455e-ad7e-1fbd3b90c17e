import pytest

from bethune.model.base import GenderEnum
from bethune.model.broker import Broker
from bethune.model.broker import BrokerPaymentMethod
from bethune.model.broker import BrokerProfile
from bethune.model.broker import BrokerQualification
from bethune.model.brokerage import Brokerage
from bethune.model.payment import AccountTypeEnum
from bethune.model.system import UserRole


@pytest.fixture(scope="function")
def test_brokerage(create_model):
    return create_model(
        Brokerage(
            name="Test Brokerage",
            contact_name="<PERSON>",
            contact_phone="+86 ***********",
            contact_email="<EMAIL>",
            province="BC",
            city="Vancouver",
            address="123 Test St, Vancouver, BC",
            postal_code="A1A 2A2",
            description="Test brokerage for insurance applications.",
            website="https://www.testbrokerage.com",
        )
    )


@pytest.fixture(scope="function")
def test_broker(create_model, create_user):
    user = create_user(email="<EMAIL>")
    broker = create_model(
        Broker(
            user_id=user.id,
            name="Test Broker",
            phone="+86 ***********",
            gender=GenderEnum.MALE,
            uid="BR-ABCD5678X",
            description="Test broker for insurance applications.",
        )
    )
    create_model(
        UserRole(
            user_id=user.id,
            role_id=2,  # 2: Broker Role ID
        )
    )
    broker_profile = create_model(
        BrokerProfile(
            broker_id=broker.id,
            public_fields="EMAIL|PHONE|NAME",
        )
    )
    create_model(BrokerQualification(broker_profile_id=broker_profile.id, is_qualified=True))
    create_model(
        BrokerPaymentMethod(
            broker_id=broker.id,
            account_type=AccountTypeEnum.E_TRANSFER,
            account_number="**********",
            is_default=True,
        )
    )
    return broker


@pytest.fixture(scope="function")
def test_brokerage_broker(
    update_model,
    test_broker,
    test_brokerage,
):
    test_broker.brokerage_id = test_brokerage.id
    return update_model(test_broker)

import io

import pytest
from fastapi.testclient import TestClient
from openpyxl import load_workbook

# 根据你的项目配置调整这些导入


@pytest.fixture
def report_api_prefix():
    return "/api/v1/report"


class TestReportExport:
    @pytest.mark.parametrize("mock_user_allowed_scopes", [{"brokerage:company:create"}], indirect=True)
    def test_export_brokerage_report(
        self,
        test_client: TestClient,
        report_api_prefix: str,
        auth_headers_generator,
        mock_auth_get_current_user,
        mock_user_allowed_scopes,
    ):
        headers = auth_headers_generator("<EMAIL>")
        response = test_client.get(f"{report_api_prefix}/export", headers=headers)
        assert response.status_code == 200
        assert "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in response.headers["content-type"]
        content_disposition = response.headers.get("content-disposition", "")
        assert "attachment; filename=brokerage_report_" in content_disposition
        excel_content = io.BytesIO(response.content)
        wb = load_workbook(excel_content)
        ws = wb["代理人列表"]
        assert ws.max_row > 1
        expected_columns = ["代理人ID", "代理人姓名", "邮箱地址"]
        header_row = [cell.value for cell in ws[1]]
        for col in expected_columns:
            assert col in header_row
        return True

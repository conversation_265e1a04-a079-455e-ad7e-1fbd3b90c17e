import io

import pytest


@pytest.fixture
def _api_prefix_broker(api_prefix) -> str:
    return f"{api_prefix}/insurance/broker"


@pytest.fixture
def _api_prefix_brokerage(api_prefix):
    return f"{api_prefix}/insurance/brokerage"


@pytest.fixture
def _test_image_file_payload() -> dict:
    return {"file": ("test_avatar.png", _create_test_image(), "image/png")}


def _create_test_image() -> io.BytesIO:
    png_data = (
        b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01"
        b"\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13"
        b"\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc```"
        b"\x00\x00\x00\x04\x00\x01\xdd\x8d\xb4\x1c\x00\x00\x00\x00IEND\xaeB`\x82"
    )
    return io.BytesIO(png_data)

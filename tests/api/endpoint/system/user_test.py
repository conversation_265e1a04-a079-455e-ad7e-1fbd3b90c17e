import pytest

from bethune import ADMIN_USER_EMAIL
from bethune import ADMIN_USER_ID


@pytest.mark.parametrize("mock_user_allowed_scopes", [{"system:user:create"}], indirect=True)
def test_user_create(test_client, api_prefix, auth_headers, mock_auth_get_current_user, mock_user_allowed_scopes):
    response = test_client.post(
        f"{api_prefix}/system/user",
        json={
            "email": "<EMAIL>",
            "password": "123456",
            "first_name": "Test",
            "last_name": "User",
            "mobile": "18612345678",
        },
        headers=auth_headers,
    )
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert response.json()["data"]["email"] == "<EMAIL>"
    assert response.json()["data"]["id"] is not None


@pytest.mark.parametrize("mock_user_allowed_scopes", [{"system:user:view"}], indirect=True)
def test_user_get(test_client, api_prefix, auth_headers, mock_auth_get_current_user, mock_user_allowed_scopes):
    response = test_client.get(f"{api_prefix}/system/user/{ADMIN_USER_ID}", headers=auth_headers)
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert response.json()["data"]["email"] == ADMIN_USER_EMAIL


@pytest.mark.parametrize("mock_user_allowed_scopes", [{"system:user:role:view"}], indirect=True)
def test_get_roles(test_client, api_prefix, auth_headers, mock_auth_get_current_user, mock_user_allowed_scopes):
    response = test_client.get(f"{api_prefix}/system/user/{ADMIN_USER_ID}/role", headers=auth_headers)
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert len(response.json()["data"]) > 0
    assert response.json()["data"]["items"][0]["id"] == 1

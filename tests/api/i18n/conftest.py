from typing import Annotated

from fastapi import APIRouter
from fastapi import Request
from fastapi.params import Query
from fastapi.testclient import TestClient
from pytest import fixture

from bethune.api.dto import BaseResponse
from bethune.util import get_text


@fixture(scope="session", name="app_with_i18n_router")
def test_client(test_client: TestClient):
    api_router = APIRouter(prefix="/i18n_test")

    @api_router.get("", response_model=BaseResponse[str])
    async def _(
        greeting: Annotated[str, Query(description="Greeting")],
        use_context_var: Annotated[
            bool | None,
            Query(description="是否使用中间件绑定在context中的gettext，默认不使用"),
        ],
        request: Request,
    ) -> BaseResponse[str]:
        return BaseResponse.ok(request.state.babel.gettext(greeting) if not use_context_var else get_text(greeting))

    test_client.app.include_router(api_router)
    return TestClient(test_client.app)

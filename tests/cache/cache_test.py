from unittest.mock import patch

import pytest
from dogpile.cache import make_region
from dogpile.cache.api import NoValue
from dogpile.cache.backends.memory import MemoryBackend

from bethune.cache.cache import _func_key_generator
from bethune.cache.cache import _get_cache_settings
from bethune.cache.cache import CACHE_KEY_SEPARATOR

# from bethune.settings import settings


@pytest.fixture(autouse=True, name="test_cache")
def setup_test_cache():
    # Mock settings
    # settings.CACHE_PREFIX = "test_"
    # Reset cache configuration
    test_cache = make_region(
        key_mangler=lambda key: f"bethune:cache:{key}", function_key_generator=_func_key_generator
    ).configure_from_config(
        {"test_backend": "dogpile.cache.memory", "test_expiration_time": 3600, "test_arguments.max_size": 1000}, "test_"
    )
    yield test_cache


@pytest.fixture
def mock_settings():
    return {"test_backend": "dogpile.cache.memory", "test_expiration_time": 3600, "test_arguments_max_size": 1000}


def test_get_cache_settings(mock_settings):
    with patch("bethune.cache.cache.get_dynamic_settings", return_value=mock_settings):
        result = _get_cache_settings("test_")
        assert result == {
            "test_backend": "dogpile.cache.memory",
            "test_expiration_time": 3600,
            "test_arguments.max_size": 1000,  # This will be transformed from arguments_max_size
        }


def test_func_key_generator():
    def test_fn(a, b, c=3):
        pass

    key_gen = _func_key_generator("test_namespace", test_fn)
    key = key_gen(1, 2, c=4)
    expected = f"test_namespace{CACHE_KEY_SEPARATOR}1{CACHE_KEY_SEPARATOR}2{CACHE_KEY_SEPARATOR}c=4"
    assert key == expected


def test_cache_operations(test_cache):
    test_key = "test_key"
    test_value = "test_value"

    # Test set and get
    test_cache.set(test_key, test_value)
    assert test_cache.get(test_key) == test_value

    # Test delete
    test_cache.delete(test_key)
    assert isinstance(test_cache.get(test_key), NoValue)


def test_cache_key_mangling(test_cache):
    test_key = "test_key"
    mangled_key = test_cache.key_mangler(test_key)
    assert mangled_key == "bethune:cache:test_key"


def test_cache_region_configuration(mock_settings):
    with patch("bethune.cache.cache.get_dynamic_settings", return_value=mock_settings):
        test_region = make_region(
            key_mangler=lambda key: CACHE_KEY_SEPARATOR.join(["test", "cache", key])
        ).configure_from_config(_get_cache_settings("test_"), "test_")
        assert isinstance(test_region.backend, MemoryBackend)
        assert test_region.expiration_time == 3600
        assert test_region.key_mangler("test_key") == "test:cache:test_key"

import os
import shutil

import pytest
from loguru import logger
from packaging.version import Version
from testcontainers.mysql import MySqlContainer
from testcontainers.redis import RedisContainer

from bethune.api.dto.auth import Token
from bethune.db import create_db_session
from bethune.db import redis as redis_module
from bethune.model import User
from bethune.model import UserStatus
from bethune.model import UserType
from bethune.repository import BaseRepository
from bethune.settings import settings
from bethune.util.password import hash_password

# 定义当前基线版本常量
_CURRENT_BASELINE_VERSION = Version("1.4.0")


def _copy_version_scripts(source_dir, target_dir, current_version):
    """复制指定版本目录中的脚本文件到目标目录，并添加版本前缀"""
    for dirname in os.listdir(source_dir):
        dirpath = os.path.join(source_dir, dirname)

        # 跳过非目录
        if not os.path.isdir(dirpath):
            continue

        # 验证目录名是否是有效版本
        try:
            dir_version = Version(dirname)
        except ValueError:
            continue

        # 只处理大于当前基线版本的版本
        if dir_version > current_version:
            for filename in os.listdir(dirpath):
                src = os.path.join(dirpath, filename)
                if os.path.isfile(src):
                    # 添加版本前缀到文件名
                    new_name = f"{dirname}_{filename}"
                    dst = os.path.join(target_dir, new_name)
                    shutil.copy2(src, dst)


def _prepare_db_seed() -> str:  # type: ignore # noqa: C901
    # 定义路径
    db_scripts_dir = os.path.abspath("./db/scripts")
    db_baseline_dir = os.path.abspath("./db/baseline")
    tmp_dir = os.path.abspath("./tmp")
    test_dir = os.path.abspath("./db/test")

    # 确保临时目录存在且为空
    if os.path.exists(tmp_dir):
        shutil.rmtree(tmp_dir)  # 删除整个目录及其内容
    os.makedirs(tmp_dir)  # 重新创建空目录

    # 步骤1: 复制指定当前基线版本的基线文件
    baseline_filename = f"{_CURRENT_BASELINE_VERSION}_baseline.sql"
    baseline_src = os.path.join(db_baseline_dir, baseline_filename)

    if os.path.isfile(baseline_src):
        shutil.copy2(baseline_src, tmp_dir)
        print(f"Copied baseline file: {baseline_filename}")
    else:
        raise FileNotFoundError(
            f"Baseline file not found: {baseline_src}. "
            f"Please ensure version {_CURRENT_BASELINE_VERSION} exists in the baseline directory."
        )

    # 步骤2: 处理版本目录 - 复制数据库脚本
    _copy_version_scripts(db_scripts_dir, tmp_dir, _CURRENT_BASELINE_VERSION)

    # 步骤3: 处理测试脚本
    _copy_version_scripts(test_dir, tmp_dir, _CURRENT_BASELINE_VERSION)

    return tmp_dir


@pytest.fixture(autouse=True, scope="session", name="test_db_engine")
def create_test_db_engine():
    mysql = None
    try:
        mysql = MySqlContainer(
            dialect="pymysql",
            image="mysql:8",  # 指定 MySQL 镜像版本
            seed=_prepare_db_seed(),  # 指定 MySQL 初始化脚本目录
            dbname="ca_insurance_dev",  # 指定数据库名称
        ).with_bind_ports(3306, 23316)
        mysql.start()
        yield mysql
    finally:
        if mysql:
            mysql.stop()


@pytest.fixture(autouse=True, scope="function")
def setup_db_session():
    try:
        yield from create_db_session(
            # session_maker=lambda: Session(test_db_engine),
            rollback_only=True,
        )
    finally:
        logger.info("teardown session")


@pytest.fixture(scope="session", name="test_redis")
def create_test_redis():
    """启动 Redis 测试容器"""
    redis = None
    try:
        redis = RedisContainer(image="redis:8")
        redis.with_bind_ports(6379, 26389)
        redis.start()
        yield redis
    finally:
        if redis:
            redis.stop()


@pytest.fixture(autouse=True, scope="module")
def reset_redis_state(test_redis):
    """每个测试前重置 Redis 状态"""
    redis_module.get_sync_redis().flushall()


@pytest.fixture
def api_prefix():
    return "/api/v1/broker"


@pytest.fixture
def auth_headers():
    """Returns headers with valid authentication token"""
    test_token = Token(access_token="test-access-token", token_type="Bearer", expires_in=3600)
    return {"Authorization": f"Bearer {test_token.access_token}"}


@pytest.fixture
def mock_user_allowed_scopes(mocker):
    """Fixture to mock user's allowed scopes"""
    mock_auth = mocker.patch("bethune.api.endpoint.system.service_context.ServiceContext.auth_service")
    mock_auth.get_user_scopes.return_value = {"broker:register", "broker:login", "broker:me"}
    return mock_auth


@pytest.fixture
def create_model():
    def __create_model(model):
        """
        Helper function to create a model instance using the common repository.
        """
        common_repo = BaseRepository[model.__class__](model_class=model.__class__)
        return common_repo.create(model)

    return __create_model


@pytest.fixture
def update_model():
    def __update_model(model):
        """
        Helper function to update a model instance using the common repository.
        """
        common_repo = BaseRepository[model.__class__](model_class=model.__class__)
        return common_repo.update(model)

    return __update_model


@pytest.fixture
def get_models():
    def __get_models(model) -> list:
        """
        Helper function to check if a model instance exists using the common repository.
        """
        common_repo = BaseRepository[model.__class__](model_class=model.__class__)
        return common_repo.get_by_example(model)

    return __get_models


@pytest.fixture
def create_user(create_model):
    def _create_user(
        email,
        password="123456",
        name="test_user",
        user_type=UserType.SAAS,
        status=UserStatus.ACTIVE,
    ):
        user = User(
            email=email,
            password=hash_password(password),
            name=name,
            type=user_type,
            status=status,
        )
        return create_model(user)

    return _create_user


@pytest.fixture
def modify_settings():
    original_values = {}

    def _modify_settings(settings_dict):
        original_values.update(settings.__dict__)
        settings.__dict__.update(settings_dict)

    yield _modify_settings
    for key, value in original_values.items():
        settings.__dict__[key] = value

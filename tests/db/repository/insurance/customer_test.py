import pytest

from bethune.db import transaction
from bethune.model.customer import Customer
from bethune.repository.insurance import InsuranceRepositoryFactory


@pytest.fixture(scope="function")
def customer_data() -> Customer:
    return Customer(
        broker_id=1,
        name="<PERSON>",
        gender="MA<PERSON>",
        move_in_date="2025-01-01",
        address="123 Main St",
        email="<EMAIL>",
        phone="************",
    )


customer_repo = InsuranceRepositoryFactory.create_customer_repository()


@transaction(rollback_only=True)
def test_create_customer(customer_data: Customer):
    created_customer = customer_repo.create(customer_data)
    assert created_customer.id is not None
    assert created_customer.name == "<PERSON>"
    assert created_customer.gender == "MALE"


@transaction(rollback_only=True)
def test_update_customer(customer_data):
    created_customer = customer_repo.create(customer_data)
    assert created_customer.id is not None
    assert created_customer.name == "<PERSON>"
    assert created_customer.gender == "MALE"

    customer_data.phone = "************"
    customer_data.id = created_customer.id
    updated_customer = customer_repo.update(customer_data)
    assert updated_customer.phone == customer_data.phone


@transaction(rollback_only=True)
def test_delete_customer(customer_data):
    created_customer = customer_repo.create(customer_data)
    assert created_customer.id is not None
    assert created_customer.name == "John Doe"
    assert created_customer.gender == "MALE"

    updated_customer = customer_repo.mark_as_deleted(created_customer.id)

    assert updated_customer.id == customer_data.id
    assert updated_customer.is_deleted == customer_data.is_deleted
    assert updated_customer.deleted_at == updated_customer.deleted_at


@transaction(rollback_only=True)
def test_get_customer_by_id(customer_data):
    created_customer = customer_repo.create(customer_data)
    assert created_customer.id is not None
    assert created_customer.name == "John Doe"
    assert created_customer.gender == "MALE"

    customer = customer_repo.get_by_id(created_customer.id)
    assert customer.id == created_customer.id

from uuid import uuid4

import pytest

from bethune.constant import ADMIN_USER_EMAIL
from bethune.constant import ADMIN_USER_ID
from bethune.db import transaction
from bethune.error import NotFoundError
from bethune.model.system import User
from bethune.repository.system import SystemRepositoryFactory

# from bethune.settings import settings  # noqa, make sure settings is imported


@pytest.fixture(scope="function")
def test_user() -> User:
    email = f"{uuid4().hex}@test.com"
    return User(email=email, password="123456")


@transaction(rollback_only=True)
def test_create_user(test_user):
    user_repo = SystemRepositoryFactory.create_user_repository()
    user = user_repo.create(test_user)

    assert user.id is not None
    assert user.email == test_user.email


@transaction(rollback_only=True)
def test_get_user_by_id():
    user_repo = SystemRepositoryFactory.create_user_repository()
    user = user_repo.get_by_id(ADMIN_USER_ID)

    assert user.id == ADMIN_USER_ID
    assert user.email == ADMIN_USER_EMAIL


@transaction(rollback_only=True)
def test_get_user_by_email():
    user_repo = SystemRepositoryFactory.create_user_repository()
    user = user_repo.get_by_email(ADMIN_USER_EMAIL)

    assert user.email == ADMIN_USER_EMAIL
    assert user.id == ADMIN_USER_ID


@transaction(rollback_only=True)
def test_update_user(test_user):
    user_repo = SystemRepositoryFactory.create_user_repository()
    new_user = user_repo.create(test_user)
    test_user.name = "new user"
    test_user.id = new_user.id
    updated_user = user_repo.update(test_user)
    assert updated_user.name == "new user"


@transaction(rollback_only=True)
def test_delete_user(test_user):
    user_repo = SystemRepositoryFactory.create_user_repository()
    new_user = user_repo.create(test_user)
    user_repo.delete(new_user)
    with pytest.raises(NotFoundError):
        user_repo.get_by_id(new_user.id)


@transaction(rollback_only=True)
def test_get_by_example():
    user_repo = SystemRepositoryFactory.create_user_repository()
    users = user_repo.get_by_example(limit=10)
    assert len(users) > 0 and len(users) <= 10
    assert all(isinstance(user, User) for user in users)

    example = User(email=ADMIN_USER_EMAIL, name="Admin")
    users = user_repo.get_by_example(example=example)
    assert len(users) == 1
    assert users[0].id == ADMIN_USER_ID

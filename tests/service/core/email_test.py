from unittest import mock

import pytest

from bethune.infrastructure.email.email_client import <PERSON>ailClient
from bethune.service.core.factory import CoreServiceFactory


@pytest.fixture
def email_client():
    return EmailClient()


@pytest.mark.asyncio
async def test_check_connection(email_client):
    with mock.patch("bethune.infrastructure.email.email_client.SMTP") as mock_smtp:
        mock_smtp_instance = mock.AsyncMock()
        mock_smtp.return_value.__aenter__.return_value = mock_smtp_instance
        mock_smtp_instance.noop.return_value = "OK"

        result = await email_client.check_connection()

        assert result is True
        mock_smtp_instance.noop.assert_awaited_once()


@pytest.mark.asyncio
async def test_send_welcome_email_with_mock():
    email_service = CoreServiceFactory.create_email_service()
    with mock.patch.object(email_service._client, "send_email", return_value=None) as mock_send_email:
        await email_service.send_welcome_email(recipient_email="<EMAIL>", recipient_name="<PERSON>")

        # Check if send_email was called
        mock_send_email.assert_called_once_with(
            subject="Welcome to Our Service",
            recipients=["<EMAIL>"],
            template_name="welcome_email.html",
            template_context={"name": "John Doe"},
        )


@pytest.mark.asyncio
async def test_send_reset_password_email_with_mock():
    email_service = CoreServiceFactory.create_email_service()

    with mock.patch.object(email_service._client, "send_email", return_value=None) as mock_send_email:
        await email_service.send_reset_password_email(recipient_email="<EMAIL>", verification_code="1234")

        # Check if send_email was called
        mock_send_email.assert_called_once_with(
            subject="Reset Your Password",
            recipients=["<EMAIL>"],
            template_name="reset_password.html",
            template_context={"verification_code": "1234"},
        )

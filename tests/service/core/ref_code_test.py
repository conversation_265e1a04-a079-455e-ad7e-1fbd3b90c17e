import threading

import pytest

from bethune.db.redis import get_sync_redis
from bethune.service.core import ReferenceTypeEnum
from bethune.service.core import StealthCodeGenerator

# 测试数据
VALID_TYPES = [ReferenceTypeEnum.LEAD, ReferenceTypeEnum.INSURANCE_APPLICATION]

INVALID_TYPES = [
    "InvalidType",
    "",
    None,
]


# Fixture: 创建生成器实例
@pytest.fixture
def generator():
    return StealthCodeGenerator()


# Fixture: 重置Redis状态
@pytest.fixture(autouse=True)
def reset_redis():
    """每个测试运行前清空Redis"""
    get_sync_redis().flushall()
    yield


# 验证有效单据类型的编号生成
@pytest.mark.parametrize("ref_type", VALID_TYPES)
def test_generate_valid_types(generator, ref_type):
    # 生成编号
    result = generator.generate(ref_type)

    # 验证基本格式
    prefix = generator._PREFIX_MAP[ref_type]
    assert result.startswith(prefix)
    assert len(result) == len(prefix) + 7 + 1  # 前缀 + 7位编码 + 1位校验 (无分隔符)

    # 验证计数器已创建
    counter_key = f"uid_counter:{ref_type}"
    assert get_sync_redis().exists(counter_key) == 1
    assert int(get_sync_redis().get(counter_key)) == 1


# 验证无效单据类型的错误处理
@pytest.mark.parametrize("ref_type", INVALID_TYPES)
def test_generate_invalid_types(generator, ref_type):
    with pytest.raises(ValueError) as excinfo:
        generator.generate(ref_type)

    assert "无效UID类型" in str(excinfo.value)

    # 验证没有创建计数器
    assert len(get_sync_redis().keys("uid_counter:*")) == 0


# 验证编号的唯一性
def test_unique_codes_for_sequential_ids(generator):
    # 生成多个编号
    results = {generator.generate(ReferenceTypeEnum.LEAD) for _ in range(5)}

    # 验证所有编号都是唯一的
    assert len(results) == 5

    # 验证计数器值正确
    counter_key = "uid_counter:Lead"
    assert int(get_sync_redis().get(counter_key)) == 5


# 验证校验位机制
def test_checksum_calculation(generator):
    # 生成编号
    result = generator.generate(ReferenceTypeEnum.INSURANCE_APPLICATION)

    prefix_len = len(generator._PREFIX_MAP[ReferenceTypeEnum.INSURANCE_APPLICATION])
    body = result[prefix_len:-1]
    checksum = result[-1]

    # 手动计算校验位
    total = sum(ord(c) for c in body)
    expected_checksum = generator._CHECK_SET[total % len(generator._CHECK_SET)]

    # 验证校验位
    assert checksum == expected_checksum


# 验证混淆算法的可逆性
def test_obfuscation_reversibility(generator):
    # 测试多个序列ID
    test_ids = [1, 10, 100, 1000, 10000, 100000, 1000000]

    for seq_id in test_ids:
        # 设置序列ID
        counter_key = "uid_counter:Lead"
        get_sync_redis().set(counter_key, seq_id - 1)

        # 生成编号
        code = generator.generate(ReferenceTypeEnum.LEAD)

        prefix_len = len(generator._PREFIX_MAP[ReferenceTypeEnum.LEAD])
        body = code[prefix_len:-1]

        # 将编码转换回数字
        base = len(generator._CHAR_SET)
        num = 0
        for char in body:
            num = num * base + generator._CHAR_SET.index(char)

        # 还原原始ID
        restored_id = generator._restore_id(num)

        # 验证可逆性
        assert restored_id == seq_id


# 验证字符集合规性
def test_character_set_compliance(generator):
    result = generator.generate(ReferenceTypeEnum.LEAD)

    prefix_len = len(generator._PREFIX_MAP[ReferenceTypeEnum.LEAD])
    body = result[prefix_len:]

    # 验证所有字符都在允许的字符集中
    allowed_chars = generator._CHAR_SET + generator._CHECK_SET
    assert all(char in allowed_chars for char in body)

    # 验证没有易混淆字符
    confusing_chars = {"0", "O", "1", "I", "8", "B"}
    assert not any(char in confusing_chars for char in body)


# 验证多单据类型同时生成
def test_multiple_ref_types(generator):
    # 生成不同单据类型的编号
    lead_code = generator.generate(ReferenceTypeEnum.LEAD)
    ia_code = generator.generate(ReferenceTypeEnum.INSURANCE_APPLICATION)

    # 验证前缀不同
    assert lead_code.startswith("LD")
    assert ia_code.startswith("IA")

    # 验证编号不同
    assert lead_code != ia_code

    # 验证计数器独立
    lead_counter = int(get_sync_redis().get("uid_counter:Lead") or 0)
    ia_counter = int(get_sync_redis().get("uid_counter:InsuranceApplication") or 0)
    assert lead_counter == 1
    assert ia_counter == 1


# 验证超大序列ID的处理
def test_very_large_sequence_id(generator):
    # 设置接近最大值的序列ID
    large_id = StealthCodeGenerator._MAX_SEQ_ID - 100
    counter_key = "uid_counter:Lead"
    get_sync_redis().set(counter_key, large_id - 1)

    # 生成编号
    result = generator.generate(ReferenceTypeEnum.LEAD)

    # 验证编号格式正确
    assert result.startswith("LD")
    assert len(result) == 10

    # 验证计数器正确递增
    assert int(get_sync_redis().get(counter_key)) == large_id


# 验证超过最大序列ID时的错误处理
def test_max_sequence_id_exceeded(generator):
    # 设置序列ID为最大值
    counter_key = "uid_counter:Lead"
    get_sync_redis().set(counter_key, StealthCodeGenerator._MAX_SEQ_ID - 1)

    # 第一次生成应该成功
    generator.generate(ReferenceTypeEnum.LEAD)

    # 第二次生成应该失败
    with pytest.raises(OverflowError) as excinfo:
        generator.generate(ReferenceTypeEnum.LEAD)

    assert "序列ID已达到最大值" in str(excinfo.value)


# 验证计数器独立工作
def test_counters_independent(generator):
    # 生成多个Lead
    for _ in range(3):
        generator.generate(ReferenceTypeEnum.LEAD)

    # 生成IA
    generator.generate(ReferenceTypeEnum.INSURANCE_APPLICATION)

    # 验证计数器值
    assert int(get_sync_redis().get("uid_counter:Lead") or 0) == 3
    assert int(get_sync_redis().get("uid_counter:InsuranceApplication") or 0) == 1


# 验证高并发下的唯一性
@pytest.mark.stress
def test_high_concurrency_uniqueness(generator):
    results = []
    threads = []
    lock = threading.Lock()

    def worker():
        code = generator.generate(ReferenceTypeEnum.LEAD)
        with lock:
            results.append(code)

    # 创建50个线程并发生成编号
    for _ in range(50):
        t = threading.Thread(target=worker)
        threads.append(t)
        t.start()

    for t in threads:
        t.join()

    # 验证所有编号唯一
    assert len(results) == len(set(results))

    # 验证计数器值正确
    assert int(get_sync_redis().get("uid_counter:Lead") or 0) == 50


# 验证服务重启后计数器继续递增
def test_counter_persistence(generator):
    # 首先生成一些编号
    for _ in range(5):
        generator.generate(ReferenceTypeEnum.LEAD)

    # 模拟服务重启 - 创建新的生成器实例
    new_generator = StealthCodeGenerator()

    # 生成新编号
    new_code = new_generator.generate(ReferenceTypeEnum.LEAD)

    # 验证新编号格式正确 - 无分隔符
    ld_prefix = generator._PREFIX_MAP[ReferenceTypeEnum.LEAD]
    assert new_code.startswith(ld_prefix)

    # 验证计数器正确递增
    assert int(get_sync_redis().get("uid_counter:Lead") or 0) == 6


# 验证字符集正确性
def test_character_set(generator):
    # 生成多个编号，验证字符集
    for _ in range(100):
        code = generator.generate(ReferenceTypeEnum.LEAD)
        prefix_len = len(generator._PREFIX_MAP[ReferenceTypeEnum.LEAD])
        body = code[prefix_len:-1]  # 去掉前缀和校验位
        checksum = code[-1]

        assert all(char in generator._CHAR_SET for char in body)
        assert checksum in generator._CHECK_SET


def test_generate_with_env_prefix(generator, modify_settings):
    modify_settings({"REF_CODE_ENV_PREFIX": "S"})
    result = generator.generate(ReferenceTypeEnum.LEAD)
    assert result.startswith("SLD")
    assert len(result) == 1 + 2 + 7 + 1
    counter_key = f"uid_counter:{ReferenceTypeEnum.LEAD}"
    assert get_sync_redis().exists(counter_key) == 1
    assert int(get_sync_redis().get(counter_key)) == 1


def test_restore_id_with_env_prefix(generator, modify_settings):
    modify_settings({"REF_CODE_ENV_PREFIX": "D"})
    seq_id = 12345
    counter_key = f"uid_counter:{ReferenceTypeEnum.LEAD}"
    get_sync_redis().set(counter_key, seq_id - 1)
    code = generator.generate(ReferenceTypeEnum.LEAD)
    assert code.startswith("DLD")
    env_prefix_len = 1
    type_prefix_len = 2
    body = code[env_prefix_len + type_prefix_len : -1]
    base = len(generator._CHAR_SET)
    num = 0
    for char in body:
        num = num * base + generator._CHAR_SET.index(char)
    restored_id = generator._restore_id(num)
    assert restored_id == seq_id
    assert int(get_sync_redis().get(counter_key)) == seq_id

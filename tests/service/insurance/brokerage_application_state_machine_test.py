from collections.abc import Callable

import pytest
from transitions import MachineError

from bethune.api.error.errors import UnauthorizedError
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.service.insurance.application_state_machine import ApplicationStateMachine
from bethune.service.insurance.application_state_machine import (
    OperatorRole,
)
from bethune.service.insurance.application_state_machine import TriggerEnum


@pytest.fixture
def create_brokerage_application_state_machine(
    create_application: Callable[..., InsuranceApplication],
) -> Callable[..., ApplicationStateMachine]:
    """
    Helper method to create a BrokerageApplicationStateMachine instance.
    """

    def _create_brokerage_application_state_machine(
        premium: float = 100.0,
        start_date: str = "2023-01-01",
        end_date: str = "2024-01-01",
        status: InsuranceApplicationStatus = InsuranceApplicationStatus.PENDING_QUOTE,
        is_lead_application: bool = False,
        operator: OperatorRole = OperatorRole.BROKER,
    ) -> ApplicationStateMachine:
        """
        Create a BrokerageApplicationStateMachine instance.
        """
        application = create_application(
            premium=premium,
            start_date=start_date,
            end_date=end_date,
            status=status,
            brokerage_id=9999,  # Assuming a brokerage ID for brokerage applications
        )
        return ApplicationStateMachine.create_state_machine(
            application=application,
            is_lead_application=is_lead_application,
            operator=operator,
        )

    return _create_brokerage_application_state_machine


class TestBrokerageApplicationStateMachine:
    """
    Tests for the BrokerageApplicationStateMachine.
    """

    @pytest.mark.parametrize(
        "operator, status, expected_status, expected_error",
        [
            (
                "BROKER",
                InsuranceApplicationStatus.PENDING_QUOTE,
                InsuranceApplicationStatus.QUOTING,
                None,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.PENDING_QUOTE,
                None,
                UnauthorizedError,
            ),
        ],
    )
    def test_request_quote(
        self,
        create_brokerage_application_state_machine,
        operator,
        status,
        expected_status,
        expected_error,
    ):
        """
        Test the transition of the brokerage application state machine.
        """
        sm = create_brokerage_application_state_machine(
            status=status,
            operator=operator,
        )
        if expected_status is None:
            with pytest.raises(expected_error):
                sm.trigger(TriggerEnum.REQUEST_QUOTE)
        else:
            sm.trigger(TriggerEnum.REQUEST_QUOTE)
            assert sm.application.status == expected_status

    @pytest.mark.parametrize(
        "operator, status, expected_status, expected_error",
        [
            (
                "BROKER",
                InsuranceApplicationStatus.QUOTING,
                None,
                UnauthorizedError,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.QUOTING,
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                None,
            ),
        ],
    )
    def test_quote(
        self,
        create_brokerage_application_state_machine,
        operator,
        status,
        expected_status,
        expected_error,
    ):
        """
        Test the quote transition of the brokerage application state machine.
        """
        sm = create_brokerage_application_state_machine(
            status=status,
            operator=operator,
        )
        if expected_status is None:
            with pytest.raises(expected_error):
                sm.trigger(TriggerEnum.QUOTE)
        else:
            sm.trigger(TriggerEnum.QUOTE)
            assert sm.application.status == expected_status

    @pytest.mark.parametrize(
        "operator, status, expected_status, expected_error",
        [
            (
                "BROKER",
                InsuranceApplicationStatus.QUOTING,
                InsuranceApplicationStatus.PENDING_QUOTE,
                None,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.QUOTING,
                None,
                UnauthorizedError,
            ),
        ],
    )
    def test_withdraw_quote(
        self,
        create_brokerage_application_state_machine,
        operator,
        status,
        expected_status,
        expected_error,
    ):
        """
        Test the withdraw quote transition of the brokerage application state machine.
        """
        sm = create_brokerage_application_state_machine(
            status=status,
            operator=operator,
        )
        if expected_status is None:
            with pytest.raises(expected_error):
                sm.trigger(TriggerEnum.WITHDRAW)
        else:
            sm.trigger(TriggerEnum.WITHDRAW)
            assert sm.application.status == expected_status

    @pytest.mark.parametrize(
        "operator, status, expected_status, expected_error",
        [
            (
                "BROKER",
                InsuranceApplicationStatus.PENDING_QUOTE,
                InsuranceApplicationStatus.PENDING_QUOTE,
                None,
            ),
            (
                "BROKER",
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                InsuranceApplicationStatus.QUOTING,
                None,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.PENDING_QUOTE,
                None,
                UnauthorizedError,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                None,
                UnauthorizedError,
            ),
        ],
    )
    def test_edit(
        self,
        create_brokerage_application_state_machine,
        operator,
        status,
        expected_status,
        expected_error,
    ):
        """
        Test the edit transition of the brokerage application state machine.
        """
        sm = create_brokerage_application_state_machine(
            status=status,
            operator=operator,
        )
        if expected_status is None:
            with pytest.raises(expected_error):
                sm.trigger(TriggerEnum.EDIT)
        else:
            sm.trigger(TriggerEnum.EDIT)
            assert sm.application.status == expected_status

    @pytest.mark.parametrize(
        "operator, status, expected_status",
        [
            (
                "BROKER",
                InsuranceApplicationStatus.PENDING_QUOTE,
                InsuranceApplicationStatus.UNDERWRITTEN,
            ),
            (
                "BROKER",
                InsuranceApplicationStatus.QUOTING,
                InsuranceApplicationStatus.UNDERWRITTEN,
            ),
            (
                "BROKER",
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                InsuranceApplicationStatus.UNDERWRITTEN,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                InsuranceApplicationStatus.UNDERWRITTEN,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.QUOTING,
                InsuranceApplicationStatus.UNDERWRITTEN,
            ),
        ],
    )
    def test_underwrite(
        self,
        create_brokerage_application_state_machine,
        operator,
        status,
        expected_status,
    ):
        """
        Test the underwrite transition of the brokerage application state machine.
        """
        sm = create_brokerage_application_state_machine(
            status=status,
            operator=operator,
        )
        sm.trigger(TriggerEnum.UNDERWRITE)
        assert sm.application.status == expected_status

    @pytest.mark.parametrize(
        "operator, status, expected_status, expected_error",
        [
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.QUOTING,
                InsuranceApplicationStatus.PENDING_QUOTE,
                None,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                InsuranceApplicationStatus.PENDING_QUOTE,
                None,
            ),
            (
                "BROKER",
                InsuranceApplicationStatus.PENDING_QUOTE,
                None,
                MachineError,
            ),
            (
                "BROKER",
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                None,
                UnauthorizedError,
            ),
            (
                "BROKER_SUPPORT",
                InsuranceApplicationStatus.UNDERWRITTEN,
                None,
                MachineError,
            ),
        ],
    )
    def test_reject(
        self,
        create_brokerage_application_state_machine,
        operator,
        status,
        expected_status,
        expected_error,
    ):
        """
        Test the reject transition of the brokerage application state machine.
        """
        sm = create_brokerage_application_state_machine(
            status=status,
            is_lead_application=False,
            operator=operator,
        )
        if expected_status is None:
            with pytest.raises(expected_error):
                sm.trigger(TriggerEnum.REJECT)
        else:
            sm.trigger(TriggerEnum.REJECT)
            assert sm.application.status == expected_status

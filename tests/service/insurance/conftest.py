from collections.abc import Callable

import pytest

from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus


@pytest.fixture
def create_application() -> Callable[..., InsuranceApplication]:
    """
    Fixture to create a InsuranceApplication instance for testing.
    """

    def _create_application(
        premium=None,
        start_date=None,
        end_date=None,
        status=InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        brokerage_id=None,
    ):
        return InsuranceApplication(
            broker_id=9999,
            customer_id=9999,
            brokerage_id=brokerage_id,
            premium=premium,
            start_date=start_date,
            end_date=end_date,
            status=status,
        )

    return _create_application

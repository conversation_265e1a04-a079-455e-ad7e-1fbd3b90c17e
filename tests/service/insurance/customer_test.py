import pytest

from bethune.model.customer import Customer as CustomerModel
from bethune.repository.insurance import CustomerRepository
from bethune.repository.insurance.insurance_application import InsuranceApplicationRepository
from bethune.service.insurance.customer import CustomerService


class TestCustomerService:
    @pytest.fixture
    def mock_repo(self, mocker):
        return mocker.Mock(spec=CustomerRepository)

    @pytest.fixture
    def mock_house_application_repo(self, mocker):
        return mocker.Mock(spec=InsuranceApplicationRepository)

    @pytest.fixture
    def service(self, mock_repo, mock_house_application_repo):
        return CustomerService(mock_repo, mock_house_application_repo)

    def test_mark_as_deleted_success(self, service, mock_repo):
        mock_repo.mark_as_deleted.return_value = True

        result = service.mark_as_deleted(1)

        assert result is True
        mock_repo.mark_as_deleted.assert_called_once_with(1)

    def test_mark_as_deleted_invalid_id(self, service, mock_repo):
        mock_repo.mark_as_deleted.return_value = False

        result = service.mark_as_deleted(999)

        assert result is False
        mock_repo.mark_as_deleted.assert_called_once_with(999)

    def test_create_customer(self, service, mock_repo):
        test_customer = CustomerModel(
            id=1, broker_id=100, name="New Customer", email="<EMAIL>", address="123 Street"
        )
        mock_repo.create.return_value = test_customer

        result = service.create(test_customer)

        assert result.name == "New Customer"
        mock_repo.create.assert_called_once_with(test_customer)

    def test_update_customer(self, service, mock_repo):
        test_customer = CustomerModel(id=1, broker_id=100, name="Updated Name", email="<EMAIL>")
        mock_repo.update.return_value = test_customer

        result = service.update(test_customer)

        assert result.name == "Updated Name"
        mock_repo.update.assert_called_once_with(test_customer)

    def test_get_customer_not_found(self, service, mock_repo):
        mock_repo.get_by_id.return_value = None

        result = service.get_by_id(999)

        assert result is None
        mock_repo.get_by_id.assert_called_once_with(999)

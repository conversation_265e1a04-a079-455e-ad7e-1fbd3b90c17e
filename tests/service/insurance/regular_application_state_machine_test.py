from collections.abc import Callable

import pytest
from transitions import MachineError

from bethune.api.error.errors import UnauthorizedError
from bethune.error.errors import DataValidationError
from bethune.model.insurance import InsuranceApplication
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.service.insurance.application_state_machine import ApplicationStateMachine
from bethune.service.insurance.application_state_machine import (
    OperatorRole,
)
from bethune.service.insurance.application_state_machine import TriggerEnum


@pytest.fixture
def create_regular_application_state_machine(
    create_application: Callable[..., InsuranceApplication],
) -> Callable[..., ApplicationStateMachine]:
    """
    Helper method to create a RegularApplicationStateMachine instance.
    """

    def _create_regular_application_state_machine(
        premium: float = 100.0,
        start_date: str = "2023-01-01",
        end_date: str = "2024-01-01",
        status: InsuranceApplicationStatus = InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
        is_lead_application: bool = False,
        operator: OperatorRole = OperatorRole.BROKER,
    ) -> ApplicationStateMachine:
        """
        Create a RegularApplicationStateMachine instance.
        """
        application = create_application(
            premium=premium,
            start_date=start_date,
            end_date=end_date,
            status=status,
            brokerage_id=None,  # No brokerage for regular applications
        )
        return ApplicationStateMachine.create_state_machine(
            application=application,
            is_lead_application=is_lead_application,
            operator=operator,  # Operator is not used in this test
        )

    return _create_regular_application_state_machine


class TestRegularApplicationStateMachine:
    """
    Tests for the RegularApplicationStateMachine.
    """

    @pytest.mark.parametrize(
        "operator, expected_status, expected_error",
        [
            (
                OperatorRole.BROKER,
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                None,
            ),
            (
                OperatorRole.BROKER_SUPPORT,
                None,
                UnauthorizedError,
            ),
            (
                OperatorRole.CUSTOMER,
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                None,
            ),
        ],
    )
    def test_edit(self, create_regular_application_state_machine, operator, expected_status, expected_error):
        """
        Test the request_edit method of the application state machine.
        """
        sm = create_regular_application_state_machine(
            status=InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
            operator=operator,
        )
        if expected_error:
            with pytest.raises(expected_error):
                sm.trigger(TriggerEnum.EDIT)
        else:
            sm.trigger(TriggerEnum.EDIT)
            assert sm.application.status == expected_status

    @pytest.mark.parametrize(
        "is_lead_application, status, expected_status",
        [
            (
                False,
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                InsuranceApplicationStatus.UNDERWRITTEN,
            ),
            (
                True,
                InsuranceApplicationStatus.PENDING_UNDERWRITTEN,
                InsuranceApplicationStatus.UNDERWRITTEN,
            ),
        ],
    )
    def test_underwrite(
        self,
        create_regular_application_state_machine,
        is_lead_application,
        status,
        expected_status,
    ):
        """
        Test the transition of the application state machine.
        """
        sm = create_regular_application_state_machine(
            is_lead_application=is_lead_application,
            status=status,
        )
        sm.trigger(TriggerEnum.UNDERWRITE)
        assert sm.application.status == expected_status

    @pytest.mark.parametrize(
        "premium, start_date, end_date, status",
        [
            (None, "2023-01-01", "2024-01-01", InsuranceApplicationStatus.PENDING_UNDERWRITTEN),
            (100.0, None, "2024-01-01", InsuranceApplicationStatus.PENDING_UNDERWRITTEN),
            (100.0, "2023-01-01", None, InsuranceApplicationStatus.PENDING_UNDERWRITTEN),
        ],
    )
    def test_underwrite_without_pre_conditions(
        self,
        create_regular_application_state_machine,
        premium,
        start_date,
        end_date,
        status,
    ):
        """
        Test the transition of the application state machine with no premium.
        """
        sm = create_regular_application_state_machine(
            premium=premium,
            start_date=start_date,
            end_date=end_date,
            status=status,
        )
        with pytest.raises(DataValidationError):
            sm.trigger(TriggerEnum.UNDERWRITE)

    @pytest.mark.parametrize(
        "status",
        [
            InsuranceApplicationStatus.UNDERWRITTEN,
        ],
    )
    def test_underwrite_with_wrong_status(self, create_regular_application_state_machine, status):
        """
        Test the transition of the application state machine with wrong status.
        """
        sm = create_regular_application_state_machine(
            status=status,
        )
        with pytest.raises(MachineError):
            sm.trigger(TriggerEnum.UNDERWRITE)

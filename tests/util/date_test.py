from datetime import date
from datetime import datetime
from datetime import time

from bethune.util import add_days
from bethune.util import format_date
from bethune.util import get_current_date
from bethune.util import get_current_datetime
from bethune.util import get_current_time
from bethune.util import get_date_range
from bethune.util import get_days_between
from bethune.util import get_end_of_month
from bethune.util import get_start_of_month
from bethune.util import is_weekend
from bethune.util import parse_date
from bethune.util import subtract_days


def test_get_current_date():
    assert isinstance(get_current_date(), date)


def test_get_current_datetime():
    assert isinstance(get_current_datetime(), datetime)


def test_get_current_time():
    assert isinstance(get_current_time(), time)


def test_format_date():
    dt = date(2023, 1, 1)
    assert format_date(dt) == "2023-01-01"


def test_parse_date():
    date_str = "2023-01-01"
    assert parse_date(date_str) == date(2023, 1, 1)


def test_get_days_between():
    start_date = date(2023, 1, 1)
    end_date = date(2023, 1, 10)
    assert get_days_between(start_date, end_date) == 9


def test_add_days():
    dt = date(2023, 1, 1)
    assert add_days(dt, 1) == date(2023, 1, 2)


def test_subtract_days():
    dt = date(2023, 1, 1)
    assert subtract_days(dt, 1) == date(2022, 12, 31)


def test_get_date_range():
    start_date = date(2023, 1, 1)
    end_date = date(2023, 1, 3)
    expected_range = [date(2023, 1, 1), date(2023, 1, 2), date(2023, 1, 3)]
    assert get_date_range(start_date, end_date) == expected_range


def test_is_weekend():
    assert is_weekend(date(2023, 1, 7)) is True  # Saturday
    assert is_weekend(date(2023, 1, 8)) is True  # Sunday
    assert is_weekend(date(2023, 1, 9)) is False  # Monday


def test_get_start_of_month():
    dt = date(2023, 1, 15)
    assert get_start_of_month(dt) == date(2023, 1, 1)


def test_get_end_of_month():
    dt = date(2023, 1, 15)
    assert get_end_of_month(dt) == date(2023, 1, 31)

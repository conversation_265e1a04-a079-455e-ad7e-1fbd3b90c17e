from bethune.util import concat_path


def test_concat_path():
    assert concat_path("a", "b", "c") == "/a/b/c"
    assert concat_path("a", "", "c") == "/a/c"
    assert concat_path("a", "b", "/") == "/a/b"
    assert concat_path("a", "b", "c/") == "/a/b/c"
    assert concat_path("a", "/b", "c") == "/a/b/c"
    assert concat_path("a", "/b", "/c") == "/a/b/c"
    assert concat_path("/a", "b", "c", "d") == "/a/b/c/d"
    assert concat_path("/a", "b", "c", "d/") == "/a/b/c/d"

insert into `brokerage` (`id`, `name`, `province`, `city`, `address`, `postal_code`, `contact_name`, `contact_email`, `contact_phone`)
values
    (1, '全面保障经纪行', 'bc', 'Vancouver', '123 Main St', 'V5K0A1', '<PERSON>', '<EMAIL>', '+86 123-4567-7890');

insert into `user` (`id`, `email`, `mobile`, `password`, `name`, `user_type`, `status`, `language`)
values
    (50, '<EMAIL>', '+86 123-4567-7890', '$2b$12$Aq31mf3bLX1TS609PRSTZejh3qXi6NeGgNM3tjQMQahoo3Emk5biy', 'Admin User', 'SAAS', 'ACTIVE', 'zh'),
    (51, '<EMAIL>', '+86 123-4567-4567', '$2b$12$Aq31mf3bLX1TS609PRSTZejh3qXi6NeGgNM3tjQMQahoo3Emk5biy', 'Broker Support', 'SAAS', 'ACTIVE', 'zh');
    (52, '<EMAIL>', '+86 123-7890-7890', '$2b$12$Aq31mf3bLX1TS609PRSTZejh3qXi6NeGgNM3tjQMQahoo3Emk5biy', 'Broker Support', 'SAAS', 'ACTIVE', 'zh');

INSERT INTO `broker`
(`id`, `user_id`, `name`, `gender`, `address`, `phone`, `insurance_company`, `created_at`, `updated_at`, `province`, `city`, `postal_code`, `insurance_type`, `brokerage_id`)
VALUES(34, 52, 'Broker In Brokerage', 'MALE', '123 Main St, San Francisco, CA 94122', '************', 'test_ic', '2025-04-10 06:57:24', '2025-06-12 21:40:56', 'BC', 'RI', '', 'HOUSE_INSURANCE', 1);


insert into `user` (`id`, `email`, `mobile`, `password`, `name`, `user_type`, `status`, `language`)
values
    (52, '<EMAIL>', '+86 123-7890-7890', '$2b$12$Aq31mf3bLX1TS609PRSTZejh3qXi6NeGgNM3tjQMQahoo3Emk5biy', 'Broker In Brokerage', 'SAAS', 'ACTIVE', 'zh');


insert into brokerage_user (`id`, `brokerage_id`, `user_id`, `name`)
values
    (1, 1, 50, 'Admin User'),
    (2, 1, 51, 'Broker Support');

insert into `user_role` (`id`, `user_id`, `role_id`)
values
    (150, 50, 5),
    (151, 51, 4);

insert into `user_role` (`id`, `user_id`, `role_id`)
values
    (152, 52, 2);


insert into `broker_profile`(`broker_id`, `public_fields`)
values
    (34, 'NAME|EMAIL|PHONE');

insert into `broker_qualification`(`broker_profile_id`, `is_qualified`)
values
    (29, 1);

insert into `broker_lead_config`(`broker_id`, `allow_leads`)
values
    (34, 1);


insert into `broker_lead_fee`(`broker_id`, `insurance_type`, `referral_fee_type`, `referral_fee_value`)
values
    (34, 'HOUSE_INSURANCE', 'PREMIUM_PERCENTAGE', 3),
    (34, 'RENTERS_INSURANCE', 'PREMIUM_PERCENTAGE', 2);

insert into `broker_payment_method`(`broker_id`, `account_type`, `account_number`, `is_default`)
values
    (34, 'E_TRANSFER', '**********', 1);

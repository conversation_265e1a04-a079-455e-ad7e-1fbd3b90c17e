# 三个用户信息接口合并方案

## 📋 原接口分析

### **接口1: 经纪人信息接口**
```python
@api_router.get(
    "/me",
    summary="get current broker info",
    response_model=BaseResponse[Broker],
    dependencies=[Security(check_not_shared_link)],
)
async def _(
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
    sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],
) -> BaseResponse[Broker]:
    broker_id: int = sc.current_broker.id  # type: ignore
    profile_id = sc.broker_profile_service.get_by_broker_id(broker_id).id
    is_qualified = sc.broker_qualification_service.get_by_profile_id(profile_id).is_qualified
    user_id: int = sc.current_broker.user_id  # type: ignore
    avatar_url = None
    user = sc_system.user_service.get_by_id(user_id)
    if avatar := user.avatar:
        avatar_url = f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{avatar}"
    broker_payment_method_model = sc.broker_payment_method_service.get_by_broker_id(broker_id)
    broker_payment_method = None
    if broker_payment_method_model:
        broker_payment_method = BrokerPaymentMethod.from_model(broker_payment_method_model)

    brokerage = None
    if brokerage_id := sc.current_broker.brokerage_id:
        if brokerage_model := sc.brokerage_service.get_by_id(brokerage_id):
            brokerage = BrokerageResponse.from_model(brokerage_model)

    return BaseResponse.ok(
        Broker.from_model(sc.current_broker, sc.permissions, is_qualified, avatar_url, user.email, broker_payment_method, brokerage)  # type: ignore
    )
```

**特点**:
- 返回类型: `BaseResponse[Broker]`
- 依赖注入: `InsuranceServiceContext` + `SystemServiceContext`
- 复杂业务逻辑: 获取资质、支付方式、经纪行信息
- 权限检查: `check_not_shared_link`

### **接口2: 基础用户信息接口**
```python
@api_router.get("/me", response_model=BaseResponse[User])
async def me(
    sc: Annotated[ServiceContext, Security(ServiceContext.create)],
):
    return BaseResponse.ok(sc.current_user)
```

**特点**:
- 返回类型: `BaseResponse[User]`
- 依赖注入: `ServiceContext`
- 简单逻辑: 直接返回当前用户
- 无额外权限检查

### **接口3: 经纪行人员信息接口**
```python
@api_router.get(
    "/me",
    summary="get current personnel info",
    response_model=BaseResponse[BrokerageUserResponse],
)
async def _(
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[BrokerageUserResponse]:
    return BaseResponse.ok(BrokerageUserResponse.from_model(sc.current_brokerage_user))
```

**特点**:
- 返回类型: `BaseResponse[BrokerageUserResponse]`
- 依赖注入: `InsuranceServiceContext`
- 简单逻辑: 直接返回经纪行用户信息
- 无额外权限检查

## 🎯 合并方案设计

### **核心思路**
1. **统一响应格式**: 创建包含所有用户信息的统一DTO
2. **角色自动识别**: 根据用户角色返回对应的详细信息
3. **向后兼容**: 保持原有接口的数据结构
4. **权限继承**: 保留最严格的权限检查

### **联合类型返回设计**
```python
# 🆕 新增: 使用Union类型直接返回原始实体
from typing import Union

UserInfoResponse = Union[Broker, User, BrokerageUserResponse]

# 响应类型
response_model=BaseResponse[UserInfoResponse]
```

**优势**:
- ✅ 不需要创建新的DTO实体
- ✅ 直接复用现有的实体类型
- ✅ 前端可以根据返回的实体类型判断用户角色
- ✅ 保持原有的数据结构不变

## 🔧 合并后的代码实现

### **新的统一接口**
```python
from typing import Union

# 🆕 新增: 联合类型定义
UserInfoResponse =

@api_router.get(
    "/me",
    summary="get unified current user info",  # 🔄 修改: 更新接口描述
    response_model=BaseResponse[UserInfoResponse],  # 🔄 修改: 使用联合类型
    dependencies=[Security(check_not_shared_link)],  # ✅ 保留: 最严格的权限检查
)
async def get_unified_user_info(  # 🔄 修改: 明确的函数名
    sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],  # ✅ 保留: 系统服务
    sc_insurance: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)] = None,  # 🆕 新增: 可选的保险服务
) -> BaseResponse[UserInfoResponse]:  # 🔄 修改: 联合类型返回

    # 🆕 新增: 尝试获取经纪人信息（优先级最高）
    try:
        if sc_insurance:
            broker = sc_insurance.current_broker

            # ✅ 保留: 原经纪人接口的完整逻辑
            broker_id: int = broker.id
            profile_id = sc_insurance.broker_profile_service.get_by_broker_id(broker_id).id
            is_qualified = sc_insurance.broker_qualification_service.get_by_profile_id(profile_id).is_qualified
            user_id: int = broker.user_id

            # ✅ 保留: 头像URL生成逻辑
            avatar_url = None
            user = sc_system.user_service.get_by_id(user_id)
            if avatar := user.avatar:
                avatar_url = f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{avatar}"

            # ✅ 保留: 支付方式获取逻辑
            broker_payment_method_model = sc_insurance.broker_payment_method_service.get_by_broker_id(broker_id)
            broker_payment_method = None
            if broker_payment_method_model:
                broker_payment_method = BrokerPaymentMethod.from_model(broker_payment_method_model)

            # ✅ 保留: 经纪行信息获取逻辑
            brokerage = None
            if brokerage_id := broker.brokerage_id:
                if brokerage_model := sc_insurance.brokerage_service.get_by_id(brokerage_id):
                    brokerage = BrokerageResponse.from_model(brokerage_model)

            # ✅ 保留: 原始Broker对象构建逻辑
            broker_info = Broker.from_model(
                broker,
                sc_insurance.permissions,
                is_qualified,
                avatar_url,
                user.email,
                broker_payment_method,
                brokerage
            )

            # 🔄 修改: 直接返回Broker实体
            return BaseResponse.ok(broker_info)

    except Exception:
        # 🆕 新增: 如果不是经纪人，尝试获取经纪行用户信息
        try:
            if sc_insurance:
                brokerage_user = sc_insurance.current_brokerage_user

                # ✅ 保留: 原经纪行用户接口的逻辑
                brokerage_user_info = BrokerageUserResponse.from_model(brokerage_user)

                # 🔄 修改: 直接返回BrokerageUserResponse实体
                return BaseResponse.ok(brokerage_user_info)

        except Exception:
            # 🆕 新增: 都不是则为普通系统用户
            pass

    # 🔄 修改: 默认返回基础User实体
    return BaseResponse.ok(sc_system.current_user)
```

### **兼容性接口（可选）**
```python
# 🆕 新增: 经纪人专用接口（向后兼容）
@api_router.get(
    "/me/broker",
    summary="get current broker info (legacy)",
    response_model=BaseResponse[Broker],
    dependencies=[Security(check_not_shared_link)],
)
async def get_broker_info_legacy(
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
    sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],
) -> BaseResponse[Broker]:
    # ✅ 保留: 完全相同的原始逻辑
    # ... (原接口1的完整代码)

# 🆕 新增: 基础用户专用接口（向后兼容）
@api_router.get(
    "/me/user",
    summary="get current user info (legacy)",
    response_model=BaseResponse[User]
)
async def get_user_info_legacy(
    sc: Annotated[ServiceContext, Security(ServiceContext.create)],
):
    # ✅ 保留: 完全相同的原始逻辑
    return BaseResponse.ok(sc.current_user)

# 🆕 新增: 经纪行用户专用接口（向后兼容）
@api_router.get(
    "/me/brokerage",
    summary="get current personnel info (legacy)",
    response_model=BaseResponse[BrokerageUserResponse],
)
async def get_brokerage_user_info_legacy(
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[BrokerageUserResponse]:
    # ✅ 保留: 完全相同的原始逻辑
    return BaseResponse.ok(BrokerageUserResponse.from_model(sc.current_brokerage_user))
```

## 📊 源代码改动对比

### **🔄 主要变更**

| 方面 | 原接口 | 合并后接口 | 改动说明 |
|------|--------|------------|----------|
| **接口路径** | 3个不同的`/me` | 1个统一的`/me` | 🔄 路径统一 |
| **返回类型** | `Broker`/`User`/`BrokerageUserResponse` | `Union[Broker, User, BrokerageUserResponse]` | 🔄 联合类型 |
| **依赖注入** | 分别注入不同ServiceContext | 同时注入所有需要的ServiceContext | 🔄 依赖整合 |
| **业务逻辑** | 分散在3个函数中 | 集中在1个函数中 | 🔄 逻辑合并 |
| **权限检查** | 只有经纪人接口有 | 统一应用最严格的权限 | ✅ 权限增强 |

### **✅ 保留内容**

1. **完整业务逻辑**: 所有原接口的业务逻辑都完整保留
2. **数据获取方式**: 保持原有的数据获取和处理方式
3. **错误处理**: 保留原有的异常处理机制
4. **权限验证**: 保留最严格的权限检查

### **🆕 新增内容**

1. **角色自动识别**: 根据用户身份自动返回对应实体
2. **联合类型返回**: 直接返回原始实体，无需包装
3. **兼容性接口**: 提供向后兼容的专用接口
4. **错误容错**: 优雅处理角色识别失败的情况

### **🔄 修改内容**

1. **函数命名**: 从匿名函数改为有意义的函数名
2. **接口描述**: 更新为统一接口的描述
3. **返回结构**: 使用联合类型直接返回原始实体
4. **依赖管理**: 整合所有必要的服务依赖

## 🚀 实施建议

### **阶段1: 部署新接口（1周）**
1. 定义`UserInfoResponse`联合类型
2. 实现统一的`/me`接口
3. 部署兼容性接口
4. 进行充分测试

### **阶段2: 前端迁移（2-3周）**
1. 前端逐步迁移到新的统一接口
2. 验证数据一致性
3. 性能测试

### **阶段3: 清理旧接口（1-2个月后）**
1. 标记旧接口为deprecated
2. 监控使用情况
3. 最终移除旧接口

## ✅ 合并优势

1. **简化前端调用**: 一个接口获取用户信息，根据角色返回对应实体
2. **减少网络请求**: 避免多次API调用
3. **统一权限管理**: 应用最严格的安全策略
4. **向后兼容**: 不影响现有系统，返回原始实体结构
5. **代码维护性**: 集中管理用户信息逻辑
6. **无需新DTO**: 直接复用现有实体，减少代码复杂度

## 🎯 前端使用示例

### **TypeScript类型定义**
```typescript
// 前端可以通过类型判断来确定用户角色
type UserInfoResponse = Broker | User | BrokerageUserResponse;

// 使用示例
const response = await api.get<BaseResponse<UserInfoResponse>>('/me');
const userInfo = response.data;

// 根据返回的实体类型判断用户角色
if ('broker_id' in userInfo && 'is_qualified' in userInfo) {
    // 这是Broker实体
    console.log('当前用户是经纪人:', userInfo.name);
} else if ('brokerage_id' in userInfo && 'user_role' in userInfo) {
    // 这是BrokerageUserResponse实体
    console.log('当前用户是经纪行人员:', userInfo.name);
} else {
    // 这是基础User实体
    console.log('当前用户是系统用户:', userInfo.name);
}
```

这个合并方案既保持了原有接口的完整功能，又提供了统一的用户信息获取方式，同时避免了创建新的DTO实体，是一个简洁高效的重构解决方案。

---

## 🔍 角色与接口映射审查报告

### **📋 系统角色定义总览**

根据系统代码分析，当前系统定义了以下5个核心角色：

| 角色ID | 角色名称 | 英文标识 | 中文描述 | 用户类型 |
|--------|----------|----------|----------|----------|
| 1 | `administrator` | 系统管理员 | 超级管理员，拥有所有权限 | SYSTEM |
| 2 | `broker` | 代理人 | 保险代理人，核心业务角色 | SAAS |
| 3 | `referral-broker` | 泛代理人 | 泛保险代理人，受限业务权限 | SAAS |
| 4 | `broker-support` | 经纪行内勤 | 经纪行内部支持人员 | SAAS |
| 5 | `brokerage-admin` | 经纪行管理员 | 经纪行公司管理员 | SAAS |

### **🎯 角色与接口映射分析**

#### **1️⃣ 系统管理员 (administrator)**
```yaml
角色特征:
  - 用户类型: SYSTEM
  - 权限范围: 系统级全权限
  - 业务范围: 系统管理、用户管理、角色权限管理

应该返回的接口: 接口2 - 基础用户信息接口
返回实体: User
原因分析:
  - ✅ 系统管理员不是保险业务角色，不需要经纪人信息
  - ✅ 系统管理员不属于经纪行，不需要经纪行用户信息
  - ✅ 基础User实体包含必要的用户信息和权限
  - ✅ 符合系统管理员的职责范围
```

#### **2️⃣ 代理人 (broker)**
```yaml
角色特征:
  - 用户类型: SAAS
  - 权限范围: 保险业务全权限
  - 业务范围: 客户管理、投保单管理、提醒设置等

应该返回的接口: 接口1 - 经纪人信息接口
返回实体: Broker
原因分析:
  - ✅ 代理人是核心保险业务角色，需要完整的经纪人信息
  - ✅ 需要资质信息(is_qualified)、支付方式、经纪行信息
  - ✅ 需要头像、联系方式等详细个人信息
  - ✅ 权限检查(check_not_shared_link)符合业务安全要求
```

#### **3️⃣ 泛代理人 (referral-broker)**
```yaml
角色特征:
  - 用户类型: SAAS
  - 权限范围: 受限的保险业务权限
  - 业务范围: 线索推荐、基础设置

应该返回的接口: 接口1 - 经纪人信息接口 (受限版本)
返回实体: Broker
原因分析:
  - ✅ 泛代理人仍然是保险业务角色，需要经纪人信息
  - ⚠️ 但权限受限，可能不需要支付方式等敏感信息
  - ✅ 需要基础的经纪人档案信息
  - 🔄 建议: 可以在Broker实体中根据权限过滤敏感字段
```

#### **4️⃣ 经纪行内勤 (broker-support)**
```yaml
角色特征:
  - 用户类型: SAAS
  - 权限范围: 经纪行内部管理权限
  - 业务范围: 投保单管理、订单处理

应该返回的接口: 接口3 - 经纪行人员信息接口
返回实体: BrokerageUserResponse
原因分析:
  - ✅ 经纪行内勤属于经纪行员工，需要经纪行用户信息
  - ✅ BrokerageUserResponse包含经纪行信息、用户角色等
  - ✅ user_role字段会标识为SUPPORT
  - ✅ 包含经纪行权限和启用状态
```

#### **5️⃣ 经纪行管理员 (brokerage-admin)**
```yaml
角色特征:
  - 用户类型: SAAS
  - 权限范围: 经纪行管理权限
  - 业务范围: 人员管理、公司信息管理

应该返回的接口: 接口3 - 经纪行人员信息接口
返回实体: BrokerageUserResponse
原因分析:
  - ✅ 经纪行管理员属于经纪行高级员工，需要经纪行用户信息
  - ✅ user_role字段会标识为ADMIN
  - ✅ 包含更高级的经纪行管理权限
  - ✅ 可以管理其他经纪行用户
```

### **🔄 特殊情况处理**

#### **复合角色用户**
```yaml
场景: 用户同时拥有多个角色
例如: 既是broker又是brokerage-admin

处理策略:
  优先级: broker > brokerage-admin > broker-support > referral-broker > administrator

原因:
  - broker角色信息最完整，包含所有业务信息
  - 经纪人身份是最核心的业务角色
  - 可以通过权限字段体现复合权限
```

#### **经纪行中的经纪人**
```yaml
场景: 用户既是broker又是某个经纪行的员工

当前实现问题:
  - 接口1返回Broker实体，包含brokerage信息
  - 接口3返回BrokerageUserResponse，包含broker信息的computed_field

建议处理:
  - 优先返回Broker实体（更完整的信息）
  - BrokerageUserResponse的computed_field可以补充经纪行特定信息
```

### **📊 最终角色映射表**

| 角色 | 优先接口 | 返回实体 | 核心信息 | 备注 |
|------|----------|----------|----------|------|
| **系统管理员** | 接口2 | `User` | 基础用户信息、系统权限 | 系统管理角色 |
| **代理人** | 接口1 | `Broker` | 完整经纪人信息、资质、支付方式 | 核心业务角色 |
| **泛代理人** | 接口1 | `Broker` | 基础经纪人信息（受限） | 受限业务角色 |
| **经纪行内勤** | 接口3 | `BrokerageUserResponse` | 经纪行员工信息、SUPPORT角色 | 经纪行员工 |
| **经纪行管理员** | 接口3 | `BrokerageUserResponse` | 经纪行管理信息、ADMIN角色 | 经纪行管理 |

### **⚠️ 实现建议**

1. **角色检测优先级**: `broker` > `brokerage-admin/broker-support` > `referral-broker` > `administrator`
2. **权限过滤**: 根据具体权限过滤敏感信息（如支付方式）
3. **错误处理**: 优雅处理角色识别失败的情况
4. **性能优化**: 避免不必要的数据库查询
5. **安全检查**: 保持最严格的权限验证

# 团队开发与测试规范完整指南

## 📋 目录
- [代码编写强制性规范](#代码编写强制性规范)
- [四层架构开发模式](#四层架构开发模式)
- [测试用例编写规范](#测试用例编写规范)
- [技术栈使用标准](#技术栈使用标准)
- [开发流程检查清单](#开发流程检查清单)

---

## 🔥 代码编写强制性规范

### 1. 私有化常量
**规则**：模块/类级别的常量、元组、列表或字典定义，其名称必须以单个下划线 `_` 开头

```python
# ✅ 正确
_USER_STATUS_ACTIVE = "active"
_ALLOWED_ROLES = ["admin", "user", "guest"]
_DEFAULT_CONFIG = {"timeout": 30, "retry": 3}

# ❌ 错误
USER_STATUS_ACTIVE = "active"
ALLOWED_ROLES = ["admin", "user"]
```

### 2. 命名清晰性
**规则**：所有变量、函数、方法和类的名称，必须清晰、准确地反映其用途和含义，做到"望文生义"

```python
# ✅ 正确
def get_active_users_by_role(role_name: str) -> list[User]:
def calculate_insurance_premium(policy_data: dict) -> float:

# ❌ 错误
def get_data(param: str) -> list:
def calc(data: dict) -> float:
```

### 3. 禁止 hasattr/getattr
**规则**：绝对禁止使用 `hasattr()` 函数，尽量不用 `getattr()`，99%的属性都可以直接通过 `对象.属性` 访问

```python
# ✅ 正确
user.email
broker.user_id
order.customer.name

# ❌ 禁止
hasattr(user, 'email')
getattr(user, 'email', None)
```

### 4. 避免不必要的中间变量
**规则**：避免创建仅使用一次的中间变量，应直接返回表达式的结果

```python
# ✅ 正确
return BaseResponse.ok(UserResponse.from_model(created_user))
return session().exec(select(User).filter(User.id == user_id)).one()

# ❌ 错误
result = BaseResponse.ok(data)
return result

user = session().exec(select(User).filter(User.id == user_id)).one()
return user
```

### 5. 推导式优先
**规则**：如果 for 循环的唯一目的是创建一个列表、字典或集合，改写为对应的推导式
**限制**：仅在有return或函数返回值场景时应用

```python
# ✅ 正确
return [user.id for user in users if user.is_active]
active_emails = [user.email.lower() for user in users if user.is_active]

# ❌ 错误（非return场景不用推导式）
for user in users:
    print(user.name)  # 这种情况保持for循环
```

### 6. 链式调用
**规则**：检查是否存在可以合并为链式调用的多个顺序操作，如果可以就一定要进行链式调用

```python
# ✅ 正确
return session().exec(
    select(User)
    .join(Role)
    .where(User.status == "active")
    .order_by(User.created_at.desc())
    .limit(10)
).all()

# ❌ 错误
query = session().query(User)
query = query.filter(User.status == "active")
query = query.order_by(User.created_at.desc())
return query.limit(10).all()
```

### 7. DRY原则 (Don't Repeat Yourself)
**规则**：仔细避免重复的代码块，将其提取到一个独立的、可复用的函数或方法中

```python
# ✅ 正确
def _add_query_conditions(filters: dict, stmt: Select) -> Select:
    if filters.get("name"):
        stmt = stmt.where(col(Entity.name).ilike(f"%{filters['name']}%"))
    if filters.get("status"):
        stmt = stmt.where(Entity.status == filters["status"])
    return stmt
```

### 8. 禁止N+1查询
**规则**：严格审查循环内部，确保没有任何数据库查询操作，所有数据都应在循环前批量加载

```python
# ✅ 正确
users_with_roles = session().exec(
    select(User, Role).join(Role, User.role_id == Role.id)
).all()

# ❌ 错误
users = session().exec(select(User)).all()
for user in users:
    role = session().exec(select(Role).where(Role.id == user.role_id)).one()  # N+1查询
```

### 9. 拒绝无意义的注释
**规则**：检查并避免那些只解释了代码字面意思的"废话注释"

```python
# ✅ 正确（有价值的注释）
# 根据业务规则，保险费率不能超过基础费率的150%
if premium_rate > base_rate * 1.5:
    raise ValueError("Premium rate exceeds maximum allowed")

# ❌ 错误（废话注释）
i = i + 1  # i自增1
user.save()  # 保存用户
```

### 10. 强制一致性
**规则**：各方面的一致性是强制性要求，体现在命名结构化、代码编写方式等各个方面

```python
# ✅ 正确（参数命名一致）
def create_user(name: str, email: str, phone: str):
    pass

# ❌ 错误（参数命名不一致）
def create_user(name, email: str, phone):  # 要么都有类型注解，要么都没有
    pass
```

---

## 🏗️ 四层架构开发模式

### 架构层次
```
API层 (endpoint)
    ↓
DTO层 (api/dto)
    ↓
Service层 (service)
    ↓
Repository层 (repository)
```

### 1. DTO层 (api/dto)
**职责**：数据传输对象，请求/响应数据结构定义

```python
# 标准DTO类结构
class EntityBase(BaseModel):
    name: str
    code: str

class EntityQueryFilter(BaseModel):
    name: str | None = None
    code: str | None = None

    def to_model(self) -> EntityModel:
        return EntityModel(**self.model_dump(exclude_unset=True))

class EntityQueryParams(PageParams):
    name: str | None = Field(None, description="名称过滤")
    code: str | None = Field(None, description="代码过滤")

class EntityCreate(EntityBase):
    def to_model(self) -> EntityModel:
        return EntityModel(**self.model_dump(exclude_unset=True))

class EntityUpdate(BaseModel):
    name: str | None = None
    code: str | None = None

    def to_model(self, id: int) -> EntityModel:
        return EntityModel(**self.model_dump(exclude_unset=True) | {"id": id})

class EntityResponse(EntityBase, WithId, AuditMixin):
    @classmethod
    def from_model(cls, model: EntityModel):
        return cls(**model.model_dump())

class EntityList(EntityBase, WithId, AuditMixin):
    @classmethod
    def from_model(cls, model: EntityModel):
        return cls(**model.model_dump())

    @classmethod
    def from_models(cls, models: list[EntityModel]):
        return [cls.from_model(model) for model in models]
```

### 2. Repository层 (repository)
**职责**：数据访问层，数据库操作和查询

```python
# 私有函数使用下划线前缀
def _add_query_conditions(filters: EntityModel, stmt: Select | SelectOfScalar) -> Select | SelectOfScalar:
    if filters.name:
        stmt = stmt.where(col(Entity.name).ilike(f"%{filters.name}%"))
    if filters.code:
        stmt = stmt.where(col(Entity.code).ilike(f"%{filters.code}%"))
    return stmt

class EntityRepository(BaseRepository[Entity]):
    def __init__(self):
        super().__init__(model_class=Entity)

    def get_by_query_filters(
        self,
        filters: Entity,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[Entity]]:
        # 计算总数
        count_stmt = _add_query_conditions(filters, select(func.count(Entity.id)))
        total = session().exec(count_stmt).one()

        # 查询数据 - 链式调用
        stmt = _add_query_conditions(filters, select(Entity))
        stmt = stmt.order_by(col(Entity.created_at).desc())
        stmt = stmt.limit(limit).offset(offset)

        return total, session().exec(stmt).all()
```

### 3. Service层 (service)
**职责**：业务逻辑处理，数据转换和业务规则

```python
class EntityService(BaseService[Entity, EntityRepository]):
    def __init__(self, repository: EntityRepository):
        super().__init__(repository)

    def get_by_query_filters(
        self,
        filters: Entity,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[Entity]]:
        return self.repository.get_by_query_filters(filters, offset, limit)
```

### 4. API层 (api/endpoint)
**职责**：HTTP接口定义，请求处理和响应

```python
@api_router.get("/search", response_model=BaseResponse[Pagination[EntityList]])
async def _(
    query_params: Annotated[EntityQueryParams, Query()],
    sc: Annotated[ServiceContext, Depends()],
) -> BaseResponse[Pagination[EntityList]]:
    filters = EntityQueryFilter(
        **query_params.model_dump(exclude={"page_no", "page_size"}, exclude_unset=True)
    )

    total, entities = sc.entity_service.get_by_query_filters(
        filters.to_model(),
        query_params.offset(),
        query_params.limit(),
    )

    return BaseResponse.ok(
        Pagination[EntityList].from_items(
            EntityList.from_models(entities),
            total,
            query_params.page_no,
            query_params.page_size,
        )
    )
```

---

## 🧪 测试用例编写规范

### 测试场景分类
1. **单接口测试**：测试接口功能实现、正确调用和错误调用
2. **流程测试**：多接口调用链测试（A接口→B接口→C接口）

### 基础编写原则

#### 1. 学习项目测试习惯
**规则**：严格保证代码编写的一致性，完全按照项目现有测试风格

#### 2. 简洁断言规范
```python
# ✅ 正确：只验证code字段
assert response.json()["code"] == 1000

# ✅ 必要时验证关键数据
assert response.json()["data"]["name"] == "Expected Name"
assert response.json()["data"]["id"]  # 直接断言真值

# ❌ 错误：不添加错误信息
assert target_broker, f"Target broker not found"  # 禁止

# ❌ 错误：冗余的非空检查
assert response.json()["data"]["id"] is not None  # 禁止
assert len(response.json()["data"]["name"]) > 0   # 禁止
```

#### 3. 权限验证标准
**五种角色权限获取方式**：
```python
# 系统管理员
headers = auth_headers_generator("<EMAIL>")

# 代理人
headers = auth_headers_generator("<EMAIL>")

# 泛代
headers = auth_headers_generator("<EMAIL>")

# 内勤（使用系统管理员）
headers = auth_headers_generator("<EMAIL>")

# 经纪行管理员
headers = auth_headers_generator("<EMAIL>")
```

#### 4. 测试函数命名规范
```python
# ✅ 正确：清晰、简短、望文知意
def test_broker_field_visibility_by_referral_broker()
def test_get_insurance_companies_success()
def test_search_with_name_filter()

# ❌ 错误：模糊或过长
def test_function()
def test_very_long_function_name_that_describes_everything()
```

#### 5. 参数化测试规范
```python
@pytest.mark.parametrize(
    "public_fields,name_visible,email_visible,phone_visible",
    [
        (["NAME", "EMAIL", "PHONE"], True, True, True),
        (["NAME", "EMAIL"], True, True, False),
        (["NAME"], True, False, False),
        ([], False, False, False),
    ],
    ids=[
        "all_fields_visible",
        "name_email_visible",
        "only_name_visible",
        "no_fields_visible",
    ],
)
def test_function_name(test_client, public_fields, name_visible, email_visible, phone_visible):
    # 测试实现
```

#### 6. 通用代码封装
```python
# ✅ 正确：封装重复逻辑
def _create_login_data(username: str, password: str, login_type: LoginType) -> dict:
    return {
        "username": username,
        "password": password,
        "login_type": login_type.value,
    }

def _update_broker_config(test_client, api_prefix, headers, config_data):
    response = test_client.put(f"{api_prefix}/config", json=config_data, headers=headers)
    assert response.json()["code"] == 1000
```

#### 7. 测试结构标准
```python
def test_function_name(
    test_client: TestClient,
    api_prefix: str,
    auth_headers_generator,
):
    # 1. 准备权限和数据
    headers = auth_headers_generator("<EMAIL>")

    # 2. 调用接口
    response = test_client.get(f"{api_prefix}/endpoint", headers=headers)

    # 3. 验证结果
    assert response.json()["code"] == 1000
```

### 强制性要求

#### ❌ 禁止事项
1. **禁止使用class封装**：直接以函数方式编写
2. **禁止修改业务代码**：测试出错优先修改测试用例
3. **禁止自己拓展**：严格按照需求实现，不添加想象的功能
4. **禁止添加注释**：保持代码简洁
5. **禁止不必要的中间变量**：只用一次的变量直接内联

#### ✅ 必须事项
1. **必须使用参数化测试**：避免重复代码
2. **必须封装通用代码**：保证代码简洁
3. **必须添加ids**：参数化测试必须有清晰命名
4. **必须按需求实现**：严格按照业务逻辑流程
5. **必须先学习项目习惯**：保证一致性

---

## ⚙️ 技术栈使用标准

### 核心技术栈
- **FastAPI + SQLModel + Pydantic + pytest + MySQL**
- **依赖注入**：`ServiceContext` 管理所有服务依赖
- **数据库操作**：`session().exec()` 执行查询
- **响应格式**：统一使用 `BaseResponse[T]`
- **分页处理**：`Pagination[T]` 标准分页格式

### 关键使用模式
```python
# 依赖注入模式
sc: Annotated[ServiceContext, Depends()]

# 查询参数模式
query_params: Annotated[QueryParams, Query()]

# 统一响应模式
return BaseResponse.ok(data)

# 分页响应模式
Pagination[T].from_items(items, total, page_no, page_size)

# DTO转换模式
request.to_model()    # 请求转Model
Response.from_model() # Model转响应
```

---

## 📋 开发流程检查清单

### 接口开发检查清单
- [ ] **DTO层**：QueryFilter、QueryParams、Create、Update、Response、List类
- [ ] **Repository层**：私有函数 `_add_query_conditions`，查询方法返回 `tuple[int, list[T]]`
- [ ] **Service层**：继承BaseService，简洁的业务方法
- [ ] **API层**：Query依赖注入，BaseResponse响应，Pagination分页
- [ ] **编码规范**：私有常量前缀、链式调用、避免中间变量、直接属性访问
- [ ] **一致性检查**：参数命名、响应格式、错误处理保持一致

### 测试用例检查清单
- [ ] **权限验证**：使用正确的角色邮箱获取headers
- [ ] **断言规范**：只验证code字段和必要数据，不添加错误信息
- [ ] **参数化测试**：必须添加ids，避免重复代码
- [ ] **通用封装**：重复逻辑封装成函数
- [ ] **命名规范**：函数名清晰、简短、望文知意
- [ ] **代码简洁**：不使用class，不添加注释，避免中间变量
- [ ] **业务逻辑**：严格按照需求实现，不自己拓展

### 代码审查要点
- [ ] 是否遵循私有化常量规范？
- [ ] 是否使用了链式调用？
- [ ] 是否避免了不必要的中间变量？
- [ ] 是否保持了各方面的一致性？
- [ ] 是否使用了正确的DTO转换模式？
- [ ] 是否遵循了四层架构分离？
- [ ] 测试用例是否符合团队规范？

---

## 🎯 总结

这套规范体系确保了：
1. **代码质量**：通过强制性编码规范保证代码的一致性和可维护性
2. **架构清晰**：四层架构明确职责分离，便于团队协作
3. **测试完整**：标准化的测试编写流程，保证功能质量
4. **技术统一**：统一的技术栈使用方式，降低学习成本
5. **流程规范**：完整的开发检查清单，确保交付质量

**严格遵循这些规范，将确保团队开发的高效性和代码的高质量！**

---

## 📚 实践示例

### 完整接口开发示例

#### 保险公司查询接口实现

**1. DTO层实现**
```python
class InsuranceCompanyQueryFilter(BaseModel):
    name: str | None = None
    code: str | None = None

    def to_model(self) -> InsuranceCompanyModel:
        return InsuranceCompanyModel(**self.model_dump(exclude_unset=True))

class InsuranceCompanyQueryParams(PageParams):
    name: str | None = Field(None, description="保险公司名称过滤")
    code: str | None = Field(None, description="保险公司代码过滤")
```

**2. Repository层实现**
```python
def _add_query_conditions(filters: InsuranceCompany, stmt: Select | SelectOfScalar) -> Select | SelectOfScalar:
    if filters.name:
        stmt = stmt.where(col(InsuranceCompany.name).ilike(f"%{filters.name}%"))
    if filters.code:
        stmt = stmt.where(col(InsuranceCompany.code).ilike(f"%{filters.code}%"))
    return stmt

class InsuranceCompanyRepository(BaseRepository[InsuranceCompany]):
    def get_by_query_filters(self, filters: InsuranceCompany, offset: int = 0, limit: int = DEFAULT_LIMIT) -> tuple[int, list[InsuranceCompany]]:
        count_stmt = _add_query_conditions(filters, select(func.count(InsuranceCompany.id)))
        total = session().exec(count_stmt).one()

        stmt = _add_query_conditions(filters, select(InsuranceCompany))
        return total, session().exec(stmt.order_by(col(InsuranceCompany.created_at).desc()).limit(limit).offset(offset)).all()
```

**3. Service层实现**
```python
class InsuranceCompanyService(BaseService[InsuranceCompany, InsuranceCompanyRepository]):
    def get_by_query_filters(self, filters: InsuranceCompany, offset: int = 0, limit: int = DEFAULT_LIMIT) -> tuple[int, list[InsuranceCompany]]:
        return self.repository.get_by_query_filters(filters, offset, limit)
```

**4. API层实现**
```python
@api_router.get("/search", response_model=BaseResponse[Pagination[InsuranceCompanyList]])
async def _(
    query_params: Annotated[InsuranceCompanyQueryParams, Query()],
    sc: Annotated[ServiceContext, Depends()],
) -> BaseResponse[Pagination[InsuranceCompanyList]]:
    filters = InsuranceCompanyQueryFilter(**query_params.model_dump(exclude={"page_no", "page_size"}, exclude_unset=True))
    total, companies = sc.insurance_company_service.get_by_query_filters(filters.to_model(), query_params.offset(), query_params.limit())

    return BaseResponse.ok(Pagination[InsuranceCompanyList].from_items(InsuranceCompanyList.from_models(companies), total, query_params.page_no, query_params.page_size))
```

### 完整测试用例示例

#### 字段可见性测试实现

```python
@pytest.fixture
def broker_api_prefix(api_prefix):
    return f"{api_prefix}/insurance/broker"

@pytest.fixture
def lead_api_prefix(api_prefix):
    return f"{api_prefix}/insurance/lead"

def _update_broker_lead_config(test_client, broker_api_prefix, broker_headers, public_fields):
    config_data = {"broker_profile": {"public_fields": public_fields}}
    response = test_client.put(f"{broker_api_prefix}/lead-config", json=config_data, headers=broker_headers)
    assert response.json()["code"] == 1000

def _get_target_broker_from_candidates(test_client, lead_api_prefix, referral_headers, target_broker_id):
    response = test_client.get(f"{lead_api_prefix}/candidate_broker_assignee", headers=referral_headers)
    assert response.json()["code"] == 1000

    for broker in response.json()["data"]["items"]:
        if broker["id"] == target_broker_id:
            return broker
    assert False

@pytest.mark.parametrize(
    "public_fields,name_visible,email_visible,phone_visible",
    [
        (["NAME", "EMAIL", "PHONE"], True, True, True),
        (["NAME", "EMAIL"], True, True, False),
        (["NAME"], True, False, False),
        ([], False, False, False),
    ],
    ids=["all_fields_visible", "name_email_visible", "only_name_visible", "no_fields_visible"],
)
def test_broker_field_visibility_by_referral_broker(test_client: TestClient, broker_api_prefix: str, lead_api_prefix: str, auth_headers_generator, public_fields: list, name_visible: bool, email_visible: bool, phone_visible: bool):
    broker_headers = auth_headers_generator("<EMAIL>")
    referral_headers = auth_headers_generator("<EMAIL>")

    _update_broker_lead_config(test_client, broker_api_prefix, broker_headers, public_fields)
    target_broker = _get_target_broker_from_candidates(test_client, lead_api_prefix, referral_headers, _get_broker_id(test_client, broker_api_prefix, broker_headers))

    assert (target_broker["name"] != "***") == name_visible
    assert (target_broker["email"] != "***") == email_visible
    assert (target_broker["phone"] != "***") == phone_visible
```

---

## 🚀 快速上手指南

### 新接口开发步骤
1. **分析需求** → 确定业务逻辑和数据流
2. **设计DTO** → 定义请求/响应数据结构
3. **实现Repository** → 编写数据访问逻辑
4. **实现Service** → 编写业务处理逻辑
5. **实现API** → 编写HTTP接口
6. **编写测试** → 覆盖主要业务场景
7. **代码审查** → 检查规范遵循情况

### 测试用例编写步骤
1. **理解业务** → 明确测试目的和验证点
2. **设计场景** → 确定测试用例覆盖范围
3. **准备数据** → 封装通用的数据准备方法
4. **编写测试** → 使用参数化测试避免重复
5. **验证结果** → 简洁的断言验证
6. **优化代码** → 消除重复，保持简洁

### 常见问题避免
- ❌ 不要定义只用一次的中间变量
- ❌ 不要在断言中添加错误信息
- ❌ 不要使用hasattr/getattr
- ❌ 不要忘记私有常量的下划线前缀
- ❌ 不要忘记参数化测试的ids
- ❌ 不要修改业务代码来适配测试

---

*本指南基于团队实际项目经验总结，严格遵循将确保代码质量和团队协作效率！*

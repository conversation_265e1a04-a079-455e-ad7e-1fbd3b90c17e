# 用户删除DDL语句说明文档

## 概述

本文档提供了删除email为 `<EMAIL>` 用户的完整DDL语句，包括外键关联关系分析和安全删除步骤。

## 外键关联关系分析

用户表(`user`)被以下6个表引用：

### 1. 显式外键约束表

| 表名 | 字段 | 约束类型 | 用途说明 |
|------|------|----------|----------|
| `user_role` | `user_id` | foreign_key="user.id" | 用户角色关联表 |
| `oauth_user_info` | `user_id` | foreign_key="user.id" | OAuth用户信息表 |
| `brokerage_user` | `user_id` | foreign_key="user.id" | 经纪行用户表 |
| `broker` | `user_id` | foreign_key="user.id" | 保险代理人表 |

### 2. 逻辑引用表

| 表名 | 字段 | 引用类型 | 用途说明 |
|------|------|----------|----------|
| `user_feedback` | `user_id` | 逻辑引用 | 用户反馈表 |
| `reminder_message` | `sender_id`, `receiver_id` | 逻辑引用 | 提醒消息表 |

### 3. 代理人相关级联表

代理人(`broker`)表关联的子表：
- `promotion_material` - 代理人推广素材
- `broker_lead_fee` - 代理人线索费用配置
- `broker_qualification` - 代理人资质认证
- `broker_profile` - 代理人档案
- `broker_payment_method` - 代理人支付方式
- `broker_lead_config` - 代理人线索配置
- `insurance_consultation` - 保险咨询记录
- `insurance_consultation_application` - 保险咨询申请关联
- `insurance_consultation_customer` - 保险咨询客户关联

## 安全删除DDL语句

### 执行原则
- **执行顺序**：按依赖关系从子表到父表依次删除
- **数据完整性**：确保不违反外键约束
- **级联删除**：处理所有相关联的数据

### 完整删除脚本

```sql
-- =====================================================
-- 用户删除DDL语句 (email: <EMAIL>)
-- 执行时间：请填写实际执行时间
-- 执行人员：请填写执行人员
-- =====================================================

-- 1. 首先获取要删除的用户ID
SET @user_id = (SELECT id FROM user WHERE email = '<EMAIL>');

-- 验证用户是否存在
SELECT 
    CASE 
        WHEN @user_id IS NULL THEN '用户不存在，无需删除'
        ELSE CONCAT('找到用户ID: ', @user_id, '，开始删除流程')
    END AS status;

-- 如果用户不存在，停止执行
-- 以下删除操作仅在用户存在时执行

-- 2. 删除用户角色关联（有外键约束）
DELETE FROM user_role WHERE user_id = @user_id;

-- 3. 删除OAuth用户信息（有外键约束）
DELETE FROM oauth_user_info WHERE user_id = @user_id;

-- 4. 删除经纪行用户关联（有外键约束）
DELETE FROM brokerage_user WHERE user_id = @user_id;

-- 5. 删除代理人相关数据（需要级联删除代理人相关表）
-- 5.1 删除代理人推广素材
DELETE FROM promotion_material WHERE broker_id IN (
    SELECT id FROM broker WHERE user_id = @user_id
);

-- 5.2 删除代理人线索费用配置
DELETE FROM broker_lead_fee WHERE broker_id IN (
    SELECT id FROM broker WHERE user_id = @user_id
);

-- 5.3 删除代理人资质认证
DELETE FROM broker_qualification WHERE broker_profile_id IN (
    SELECT id FROM broker_profile WHERE broker_id IN (
        SELECT id FROM broker WHERE user_id = @user_id
    )
);

-- 5.4 删除代理人档案
DELETE FROM broker_profile WHERE broker_id IN (
    SELECT id FROM broker WHERE user_id = @user_id
);

-- 5.5 删除代理人支付方式
DELETE FROM broker_payment_method WHERE broker_id IN (
    SELECT id FROM broker WHERE user_id = @user_id
);

-- 5.6 删除代理人线索配置
DELETE FROM broker_lead_config WHERE broker_id IN (
    SELECT id FROM broker WHERE user_id = @user_id
);

-- 5.7 删除保险咨询申请关联
DELETE FROM insurance_consultation_application WHERE insurance_consultation IN (
    SELECT id FROM insurance_consultation WHERE broker_id IN (
        SELECT id FROM broker WHERE user_id = @user_id
    )
);

-- 5.8 删除保险咨询客户关联
DELETE FROM insurance_consultation_customer WHERE insurance_consultation IN (
    SELECT id FROM insurance_consultation WHERE broker_id IN (
        SELECT id FROM broker WHERE user_id = @user_id
    )
);

-- 5.9 删除保险咨询记录
DELETE FROM insurance_consultation WHERE broker_id IN (
    SELECT id FROM broker WHERE user_id = @user_id
);

-- 5.10 删除代理人记录（有外键约束）
DELETE FROM broker WHERE user_id = @user_id;

-- 6. 删除用户反馈（逻辑引用）
DELETE FROM user_feedback WHERE user_id = @user_id;

-- 7. 删除提醒消息（逻辑引用）
DELETE FROM reminder_message WHERE sender_id = @user_id OR receiver_id = @user_id;

-- 8. 最后删除用户主记录
DELETE FROM user WHERE email = '<EMAIL>';

-- 9. 验证删除结果
SELECT CASE 
    WHEN (SELECT COUNT(*) FROM user WHERE email = '<EMAIL>') = 0 
    THEN '✅ 用户删除成功' 
    ELSE '❌ 用户删除失败' 
END AS delete_result;

-- 10. 统计删除影响行数
SELECT 
    'user_role' as table_name,
    (SELECT COUNT(*) FROM user_role WHERE user_id = @user_id) as remaining_records
UNION ALL
SELECT 'oauth_user_info', (SELECT COUNT(*) FROM oauth_user_info WHERE user_id = @user_id)
UNION ALL  
SELECT 'brokerage_user', (SELECT COUNT(*) FROM brokerage_user WHERE user_id = @user_id)
UNION ALL
SELECT 'broker', (SELECT COUNT(*) FROM broker WHERE user_id = @user_id)
UNION ALL
SELECT 'user_feedback', (SELECT COUNT(*) FROM user_feedback WHERE user_id = @user_id)
UNION ALL
SELECT 'user', (SELECT COUNT(*) FROM user WHERE email = '<EMAIL>');
```

## 事务版本（推荐使用）

```sql
-- =====================================================
-- 事务安全版本 - 用户删除DDL语句
-- =====================================================

START TRANSACTION;

-- 设置用户ID变量
SET @user_id = (SELECT id FROM user WHERE email = '<EMAIL>');

-- 检查用户是否存在
SELECT @user_id as user_id_to_delete;

-- 如果用户不存在，回滚事务
-- 以下是所有删除操作...
-- [插入上述完整删除脚本内容]

-- 最终验证
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM user WHERE email = '<EMAIL>') = 0 
        THEN '准备提交事务 - 用户删除成功'
        ELSE '准备回滚事务 - 用户删除失败'
    END as transaction_status;

-- 如果验证通过，提交事务
COMMIT;

-- 如果有任何问题，使用以下命令回滚
-- ROLLBACK;
```

## 执行前注意事项

### 1. 数据备份
```sql
-- 建议执行删除前先备份相关数据
CREATE TABLE user_backup_20240729 AS SELECT * FROM user WHERE email = '<EMAIL>';
CREATE TABLE user_role_backup_20240729 AS SELECT * FROM user_role WHERE user_id = (SELECT id FROM user WHERE email = '<EMAIL>');
-- ... 其他相关表的备份
```

### 2. 权限检查
- 确保当前数据库用户有DELETE权限
- 确保有足够权限操作所有相关表
- 建议使用管理员权限执行

### 3. 业务影响评估
- **代理人数据**：如果该用户是代理人，将删除所有相关业务数据
- **咨询记录**：相关的保险咨询记录将被删除
- **用户反馈**：该用户的所有反馈记录将被删除
- **消息记录**：相关的提醒消息将被删除

### 4. 执行环境
- 建议在测试环境先执行验证
- 生产环境执行时选择业务低峰期
- 执行前通知相关业务人员

## 回滚方案

如果删除后需要恢复数据：

```sql
-- 从备份表恢复数据
INSERT INTO user SELECT * FROM user_backup_20240729;
INSERT INTO user_role SELECT * FROM user_role_backup_20240729;
-- ... 其他表的恢复操作
```

## 执行日志模板

```
执行时间：____年____月____日 ____时____分
执行人员：________________
执行环境：[ ] 测试环境  [ ] 生产环境
备份状态：[ ] 已完成备份  [ ] 无需备份
执行结果：[ ] 成功  [ ] 失败
影响记录数：________条
备注：________________________________
```

---

**⚠️ 重要提醒**：
1. 此操作不可逆，执行前务必确认
2. 建议在测试环境先验证脚本正确性
3. 生产环境执行时务必先备份数据
4. 如有疑问，请联系数据库管理员确认